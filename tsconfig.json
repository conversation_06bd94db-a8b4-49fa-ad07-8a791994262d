{
  "compilerOptions": {
    "allowImportingTsExtensions": true,
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "baseUrl": "src",
    "esModuleInterop": false,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "noEmit": true,
    "noFallthroughCasesInSwitch": true,
    /* Linting */
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "paths": {
      "~/*": [
        "./*"
      ]
    },
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": false,
    "strictNullChecks": true,
    "target": "ESNext",
    "useDefineForClassFields": true
  },
  "exclude": [
    "node_modules"
  ],
  "include": [
    "src",
    "**/*.ts",
    "**/*.tsx"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}