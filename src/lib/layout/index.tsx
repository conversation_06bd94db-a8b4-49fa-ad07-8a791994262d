import { Box } from '@chakra-ui/react';
import type { ReactNode } from 'react';
import { motion } from 'framer-motion';

import Footer from './Footer';
import Header from './Header';
import Meta from './Meta';

const MotionBox = motion(Box);

type LayoutProps = {
  children: ReactNode;
};

const Layout = ({ children }: LayoutProps) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5, ease: 'easeInOut' } }, // Add a transition duration
  };

  return (
    <Box margin="0 auto">
      <Meta />
      <Header />
      <Box width="full" as="main" marginY={22}>
        {children}
      </Box>
      <MotionBox
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{ position: 'relative' }}
      >
        <Footer />
      </MotionBox>
    </Box>
  );
};

export default Layout;
