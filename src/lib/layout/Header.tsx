import {
  // Box,
  Flex,
} from '@chakra-ui/react';

import Navbar from './NavBar';
// import ThemeToggle from './ThemeToggle';

const Header = () => {
  return (
    <Flex
      as="header"
      width="full"
      align="center"
      alignSelf="flex-start"
      justifyContent="center"
      gridGap={2}
    >
      <Navbar />
      {/* <Box marginLeft="auto">
        <ThemeToggle />
      </Box> */}
    </Flex>
  );
};

export default Header;
