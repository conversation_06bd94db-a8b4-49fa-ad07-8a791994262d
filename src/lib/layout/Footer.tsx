/* eslint-disable react/no-unescaped-entities */
import {
  Box,
  chakra,
  Container,
  SimpleGrid,
  Stack,
  Text,
  Image,
  VisuallyHidden,
  Input,
  IconButton,
  useColorModeValue,
  useBreakpointValue,
  Flex,
  Link,
} from '@chakra-ui/react';
import type { ReactNode } from 'react';
import { BiMailSend } from 'react-icons/bi';
import { FaInstagram, FaTwitter, FaYoutube } from 'react-icons/fa';

const SocialButton = ({
  children,
  label,
  href,
}: {
  children: ReactNode;
  label: string;
  href: string;
}) => {
  return (
    <chakra.button
      bg={useColorModeValue('blackAlpha.100', 'whiteAlpha.100')}
      rounded="full"
      w={10}
      h={10}
      cursor="pointer"
      as="a"
      href={href}
      display="inline-flex"
      alignItems="center"
      justifyContent="center"
      transition="all 0.3s ease"
      _hover={{
        bg: useColorModeValue('blackAlpha.200', 'whiteAlpha.200'),
        transform: 'translateY(-2px)',
      }}
    >
      <VisuallyHidden>{label}</VisuallyHidden>
      {children}
    </chakra.button>
  );
};

const ListHeader = ({ children }: { children: ReactNode }) => {
  return (
    <Text
      fontWeight="600"
      fontSize="lg"
      mb={4}
      color={useColorModeValue('gray.800', 'white')}
    >
      {children}
    </Text>
  );
};

const FooterLink = ({
  href,
  children,
}: {
  href: string;
  children: ReactNode;
}) => {
  return (
    <Link
      href={href}
      color={useColorModeValue('gray.600', 'gray.400')}
      _hover={{
        color: useColorModeValue('gray.800', 'white'),
        textDecoration: 'none',
      }}
      transition="color 0.2s ease"
    >
      {children}
    </Link>
  );
};

export default function LargeWithNewsletter() {
  return (
    <Box
      bg={useColorModeValue('gray.50', 'gray.900')}
      color={useColorModeValue('gray.700', 'gray.200')}
      borderTop="1px solid"
      borderColor={useColorModeValue('gray.200', 'gray.700')}
    >
      <Container as={Stack} maxW="6xl" py={12}>
        <SimpleGrid
          templateColumns={{ sm: '1fr 1fr', md: '2fr 1fr 1fr 2fr' }}
          spacing={10}
        >
          <Stack spacing={8}>
            <Flex alignItems="center" gap="12px">
              <Image
                style={{ display: 'fit-content' }}
                src="/icon/mpr.webp"
                alt="Mon Partenaire Renta Logo"
                boxSize="40px"
              />
              <Text
                textAlign={useBreakpointValue({ base: 'center', md: 'left' })}
                fontFamily="heading"
                fontWeight="bold"
                fontSize="xl"
                color={useColorModeValue('gray.800', 'white')}
              >
                Mon Partenaire Renta
              </Text>
            </Flex>
            <Text
              fontSize="sm"
              color={useColorModeValue('gray.600', 'gray.400')}
            >
              Copyright © 2024 Mon Partenaire Renta. Tous droits réservés.
            </Text>
            <Stack direction="row" spacing={6}>
              <SocialButton label="Twitter" href="#">
                <FaTwitter size="20px" />
              </SocialButton>
              <SocialButton label="YouTube" href="#">
                <FaYoutube size="20px" />
              </SocialButton>
              <SocialButton label="Instagram" href="#">
                <FaInstagram size="20px" />
              </SocialButton>
            </Stack>
          </Stack>
          <Stack align="flex-start" spacing={4}>
            <ListHeader>Entreprise</ListHeader>
            <FooterLink href="#">À propos de nous</FooterLink>
            <FooterLink href="#">Blog</FooterLink>
            <FooterLink href="#">Contactez-nous</FooterLink>
            <FooterLink href="#">Tarifs</FooterLink>
            <FooterLink href="#">Testimonials</FooterLink>
          </Stack>
          <Stack align="flex-start" spacing={4}>
            <ListHeader>Soutien</ListHeader>
            <FooterLink href="#">Centre d'aide</FooterLink>
            <FooterLink href="#">Conditions d'utilisation</FooterLink>
            <FooterLink href="#">Légal</FooterLink>
            <FooterLink href="#">Politique de confidentialité</FooterLink>
          </Stack>
          <Stack align="flex-start" spacing={4}>
            <ListHeader>Tiens-toi à jour</ListHeader>
            <Stack direction="row" spacing={2}>
              <Input
                placeholder="Votre adresse e-mail"
                bg={useColorModeValue('white', 'whiteAlpha.100')}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'whiteAlpha.200')}
                _focus={{
                  borderColor: useColorModeValue('green.400', 'green.200'),
                  boxShadow: '0 0 0 1px var(--chakra-colors-green-400)',
                }}
                _hover={{
                  borderColor: useColorModeValue('gray.300', 'whiteAlpha.300'),
                }}
                transition="all 0.2s ease"
              />
              <IconButton
                bg={useColorModeValue('green.400', 'green.500')}
                color="white"
                _hover={{
                  bg: useColorModeValue('green.500', 'green.600'),
                  transform: 'translateY(-1px)',
                }}
                _active={{
                  bg: useColorModeValue('green.600', 'green.700'),
                }}
                aria-label="Subscribe"
                icon={<BiMailSend size="20px" />}
                transition="all 0.2s ease"
              />
            </Stack>
          </Stack>
        </SimpleGrid>
      </Container>
    </Box>
  );
}
