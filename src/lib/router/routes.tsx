import React from 'react';
import type { PathRouteProps } from 'react-router-dom';
 
const Home = React.lazy(() => import('~/lib/pages/home'));
const Services = React.lazy(() => import('~/lib/pages/services'));
const Pricing = React.lazy(() => import('~/lib/pages/pricing'));
const Links = React.lazy(() => import('~/lib/pages/links'));
const ProjectProfitability = React.lazy(
  () => import('~/lib/pages/projectProfitability')
);
const BuyerAssessment = React.lazy(() => import('~/lib/pages/buyerAssessment'));
const TableOfContents = React.lazy(() => import('~/lib/pages/tableOfContents'));
const VisitForm = React.lazy(() => import('~/lib/pages/visitForm'));
const CheckListForm = React.lazy(() => import('~/lib/pages/checkListForm'));
const InvestmentAnalysis = React.lazy(() => import('~/lib/pages/investementAnalysis'));
const BudgetDefinition = React.lazy(
  () => import('~/lib/pages/methodologicalGuides/components/BudgetDefinition')
);

const BorrowCapacity = React.lazy(
  () => import('~/lib/pages/methodologicalGuides/components/BorrowCapacity')
);
const DebtRate = React.lazy(
  () => import('~/lib/pages/methodologicalGuides/components/DebtRate')
);
const RemainToSurvive = React.lazy(
  () => import('~/lib/pages/methodologicalGuides/components/RemainToSurvive')
);

export const routes: Array<PathRouteProps> = [
  {
    path: '/',
    element: <Home />,
  },
  {
    path: '/services',
    element: <Services />,
  },
  {
    path: '/pricing',
    element: <Pricing />,
  },
  {
    path: '/links',
    element: <Links />,
  },
  {
    path: '/project-profitability',
    element: <ProjectProfitability />,
  },
  {
    path: '/buyer-assessment',
    element: <BuyerAssessment />,
  },
  {
    path: '/table-of-contents',
    element: <TableOfContents />,
  },
  {
    path: '/table-of-contents/budget-definition',
    element: <BudgetDefinition />,
  },
  {
    path: '/table-of-contents/borrow-capacity',
    element: <BorrowCapacity />,
  },
  {
    path: '/table-of-contents/depth-rate',
    element: <DebtRate />,
  },
  {
    path: '/table-of-contents/remain-to-survive',
    element: <RemainToSurvive />,
  },
  {
    path: '/visit-form',
    element: <VisitForm />,
  },
  {
    path: '/check-list',
    element: <CheckListForm />,
  },
  {
    path: '/investement-analysis',
    element: <InvestmentAnalysis />,
  },
];

export const privateRoutes: Array<PathRouteProps> = [];
