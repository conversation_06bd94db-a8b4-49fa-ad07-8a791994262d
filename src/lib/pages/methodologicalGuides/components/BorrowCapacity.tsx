'use client';

import {
  <PERSON>,
  But<PERSON>,
  Center,
  Container,
  <PERSON>I<PERSON>,
  Stack,
  Text,
  UnorderedList,
} from '@chakra-ui/react';
import { IoIosArrowForward } from 'react-icons/io';
import type { YouTubeProps } from 'react-youtube';
// eslint-disable-next-line import/no-extraneous-dependencies
import YouTube from 'react-youtube';

const data = {
  title: "Quelle-est votre capacité d'emprunt ?",
  videoId: 'YN581VBDE94',
  header: `La capacité d’emprunt (également appelée capacité d’endettement") correspond au montant maximal que la banque voudra bien nous prêter au travers d’un crédit.`,
  children: [
    {
      id: '1',
      title: 'Ce montant dépendra de 3 critères majeurs :',
      items: [
        {
          id: 'a1',
          text: 'Notre Taux d’endettement',
        },
        { id: 'a2', text: 'Notre Reste à vivre' },
        { id: 'a3', text: 'Le risque pesant sur l’opération' },
      ],
    },
  ],
  secondSection: `La banque sera souvent à même de nous informer de notre capacité d’emprunt avant même d’effectuer nos recherches immobilières. Attention toutefois, à ce stade prérecherche, cette information fournie par la banque ne restera qu’une indication, et n’implique absolument pas un futur refus ou acceptation de sa part en cas de respect ou non-respect de cette indication.`,
  thirdSection: `(La nature du bien et son rendement, non connus à ce stade, influenceront également le choix de la banque.)`,
};
const onPlayerReady: YouTubeProps['onReady'] = (event) => {
  // access to player in all event handlers via event.target
  event.target.pauseVideo();
};
const opts: YouTubeProps['opts'] = {
  width: { base: '2xl', sm: '4xl' },
  height: '400',
  playerVars: {
    // https://developers.google.com/youtube/player_parameters
    autoplay: 1,
  },
};

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={5}
          gap={5}
          justifyContent="center"
        >
          <Button
            as="a"
            size="md"
            width="150px"
            border="1px"
            borderRadius={0}
            fontWeight="none"
            rightIcon={<IoIosArrowForward />}
            variant="outline"
            href="/table-of-contents"
          >
            Retour en arrière
          </Button>
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            {data.title}
          </Text>
          {/* <Container maxW="2xl" h="lg"> */}
          <YouTube videoId={data.videoId} opts={opts} onReady={onPlayerReady} />
          {/* </Container> */}
          <Text>{data.header}</Text>
          <>
            {data.children.map((element) => (
              <div key={element.id}>
                <Text>{element.title}</Text>
                <UnorderedList pl={4}>
                  {element.items.map((listElement) => (
                    <ListItem key={listElement.id}>{listElement.text}</ListItem>
                  ))}
                </UnorderedList>
              </div>
            ))}
          </>
          <Text fontStyle="italic">{data.secondSection}</Text>
          <Text fontStyle="italic">{data.thirdSection}</Text>
        </Stack>
      </Box>
    </Center>
  );
}
