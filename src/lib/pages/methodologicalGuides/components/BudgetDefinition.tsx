'use client';

import {
  <PERSON>,
  But<PERSON>,
  Center,
  Container,
  ListItem,
  Stack,
  Text,
  UnorderedList,
} from '@chakra-ui/react';
import { IoIosArrowForward } from 'react-icons/io';
import type { YouTubeProps } from 'react-youtube';
// eslint-disable-next-line import/no-extraneous-dependencies
import YouTube from 'react-youtube';

const data = {
  title: 'Définir sa tranche de prix:',
  videoId: 'n5s1FqFJgrM',
  header:
    "Il nous faudra dès le début définir le budget que nous souhaitons/pouvons allouer pour l'acquisition de notre bien.",
  children: [
    {
      id: '1',
      title: 'Plusieurs critères entreront en compte pour définir ce budget :',
      items: [
        {
          id: 'a1',
          text: 'La capacité d’emprunt (Taux d’endettement / Reste à vivre / Risque de l’opération)',
        },
        { id: 'a2', text: 'L’apport possible' },
        { id: 'a3', text: 'La diversification' },
        { id: 'a4', text: 'La réserve de sécurité' },
      ],
    },
    {
      id: '2',
      title:
        'En fonction du budget que nous souhaitons allouer au bien à acquérir, nous nous dirigerons alors vers différents types de biens :',
      items: [
        {
          id: 'b1',
          text: 'Budget très limité : places de parking / Box / Sous-location',
        },
        { id: 'b2', text: 'Budget moyen : appartements' },
        {
          id: 'b3',
          text: 'Budget plus conséquent : IDR (Immeubles de rapport)',
        },
      ],
    },
    {
      id: '3',
      title:
        '(Nous pourrons également avec un budget conséquent acquérir plusieurs biens d’une catégorie moins onéreuse)',
      items: [],
    },
  ],
};
const onPlayerReady: YouTubeProps['onReady'] = (event) => {
  // access to player in all event handlers via event.target
  event.target.pauseVideo();
};
const opts: YouTubeProps['opts'] = {
  width: { base: '2xl', sm: '4xl' },
  height: '400',
  playerVars: {
    // https://developers.google.com/youtube/player_parameters
    autoplay: 1,
  },
};

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={5}
          gap={5}
          justifyContent="center"
        >
          <Button
            as="a"
            size="md"
            width="150px"
            border="1px"
            borderRadius={0}
            fontWeight="none"
            rightIcon={<IoIosArrowForward />}
            variant="outline"
            href="/table-of-contents"
          >
            Retour en arrière
          </Button>
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            {data.title}
          </Text>
          {/* <Container maxW="2xl" h="lg"> */}
          <YouTube videoId={data.videoId} opts={opts} onReady={onPlayerReady} />
          {/* </Container> */}
          <Text>{data.header}</Text>
          <>
            {data.children.map((element) => (
              <div key={element.id}>
                <Text>{element.title}</Text>
                <UnorderedList pl={4}>
                  {element.items.map((listElement) => (
                    <ListItem key={listElement.id}>{listElement.text}</ListItem>
                  ))}
                </UnorderedList>
              </div>
            ))}
          </>
        </Stack>
      </Box>
    </Center>
  );
}
