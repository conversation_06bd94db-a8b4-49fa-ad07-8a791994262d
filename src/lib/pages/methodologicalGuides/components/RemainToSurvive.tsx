'use client';

import { <PERSON>, But<PERSON>, Center, Container, Stack, Text } from '@chakra-ui/react';
import { IoIosArrowForward } from 'react-icons/io';
import type { YouTubeProps } from 'react-youtube';
// eslint-disable-next-line import/no-extraneous-dependencies
import YouTube from 'react-youtube';

import ListItems from '~/lib/components/commonComponents/ListItems';

const data = {
  title: `Votre "Reste à vivre"`,
  videoId: 'PulU2OyvUm0',
  firstSection: `Le reste à vivre correspond au revenu qu’il restera à l’investisseur chaque mois après déduction de ses charges récurrentes.`,
  secondSection: `Ce sera également un critère déterminant pour la détermination du montant empruntable.`,
  thirdSection: `L’investisseur A dispose de revenus mensuels de 1 500 €, et souhaite demander un crédit l’amenant à un montant total de charges mensuelles récurrentes de 400 €.Son taux d’endettement sera alors de 26,7%

Son reste à vivre sera alors de 1 500-400 = 1 100€/mois`,
  fourthSection: `L’investisseur B dispose de revenus mensuels de 8 000 €, et souhaite demander un crédit l’amenant à un montant total de charges mensuelles récurrentes de 2 800 €.Son taux d’endettement sera alors de 35%

Son reste à vivre sera alors de 8 000 - 2800 = 5 200€/mois

`,
  fifthSection: `Malgré un taux d’endettement de notre INVESTISSEUR B (35%) plus important que celui de notre investisseur A (seulement 26,7%), notre INVESTISSEUR B sera (a priori) plus susceptible de se voir accepter son crédit, et ce même s’il atteint la limite critique des 35% d’endettement.

 `,
  firstList: [
    {
      id: '1',
      title: '',
      items: [
        {
          id: 'a1',
          text: `En effet, après remboursement de ses charges, il ne restera que 1 100€ de reste à vivre pour notre INVESTISSEUR A. On comprend qu’il aura par la suite du mal à épargner (il lui faudra assurer un minimum de dépenses personnelles) et qu’en cas de difficultés financières, il sera vite incapable de rembourser ses mensualités dues à la banque.`,
        },
        {
          id: 'a2',
          text: `Au contraire, l’investisseur B dispose lui d’un reste à vivre de 5 200€/mois après avoir remboursé ses charges récurrentes.
Bien qu’ayant atteint la limite critique des 35% de taux d’endettement, il lui restera donc une somme plus que suffisante pour assurer ses dépenses quotidiennes tout en épargnant, et sera donc plus à même de rassurer une banque.`,
        },
      ],
    },
  ],
};
const onPlayerReady: YouTubeProps['onReady'] = (event) => {
  // access to player in all event handlers via event.target
  event.target.pauseVideo();
};
const opts: YouTubeProps['opts'] = {
  width: { base: '2xl', sm: '4xl' },
  height: '400',
  playerVars: {
    // https://developers.google.com/youtube/player_parameters
    autoplay: 1,
  },
};

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={5}
          gap={5}
          justifyContent="center"
        >
          <Button
            as="a"
            size="md"
            width="150px"
            border="1px"
            borderRadius={0}
            fontWeight="none"
            rightIcon={<IoIosArrowForward />}
            variant="outline"
            href="/table-of-contents"
          >
            Retour en arrière
          </Button>
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            {data.title}
          </Text>
          {/* <Container maxW="2xl" h="lg"> */}
          <YouTube videoId={data.videoId} opts={opts} onReady={onPlayerReady} />
          {/* </Container> */}
          <Text>{data.firstSection}</Text>
          <Text>{data.secondSection}</Text>
          <Center py={5}>
            <Text fontWeight="bold" textDecoration="underline">
              Comparons le profil de 2 investisseurs :
            </Text>
          </Center>
          <Text textDecoration="underline">INVESTISSEUR A :</Text>
          <Text>{data.thirdSection}</Text>
          <Text textDecoration="underline">INVESTISSEUR B :</Text>
          <Text>{data.fourthSection}</Text>
          <Text>{data.fifthSection}</Text>
          <>
            {data.firstList.map((element) => (
              <ListItems {...element} />
            ))}
          </>
        </Stack>
      </Box>
    </Center>
  );
}
