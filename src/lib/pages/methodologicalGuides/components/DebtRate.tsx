'use client';

import { <PERSON>, But<PERSON>, Center, Container, Stack, Text } from '@chakra-ui/react';
import { IoIosArrowForward } from 'react-icons/io';
import type { YouTubeProps } from 'react-youtube';
// eslint-disable-next-line import/no-extraneous-dependencies
import YouTube from 'react-youtube';

import ListItems from '~/lib/components/commonComponents/ListItems';

const data = {
  title: "Le Taux d'endettement",
  videoId: 'PulU2OyvUm0',
  firstSection: `Le taux d’endettement représente la proportion de nos revenus consacrée au remboursement de charges récurrentes.`,
  secondSection: `Depuis Janvier 2022, les recommandations du HCSF (Haut Conseil de Stabilité Financière) sont devenues contraignantes (les banques pouvant être sanctionnées par l’Autorité de contrôle prudentiel et de résolution (ACPR) en cas de non-respect de ces règles).`,
  thirdSection: `Elles fixent un seuil limite de 35% pour le taux d’endettement, et une durée maximale de 25ans (27ans en cas de différé de 2 ans) pour rembourser un nouveau prêt immobilier`,
  fourthSection: `Cela signifie globalement que nos charges ne devraient pas représenter plus d’un tiers de nos revenus pour espérer obtenir l’acceptation de la banque concernant notre crédit.`,
  fifthSection: `Le HSCF a toutefois prévu une marge de flexibilité : les établissements bancaires pourront déroger à ces règles juridiques pour 20 % de leur production totale de crédit immobilier. Pour un dossier sur cinq financé, les banques seront donc en mesure de financer les projets d’achats même si le taux d’endettement dépasse 35 % ou/et que la durée de remboursement est supérieure à 25 ans. Sur cette part, 70% (en 2024) de cette flexibilité sera réservée aux acquéreurs de leur résidence principale. Pour les 30% (en 2024) restants, les banques feront ce qu'elles veulent.`,
  sixthSection: ` Dans le cadre du calcul du taux d’endettement, la banque ne prendra souvent en considération que 70 à 90% du montant de nos revenus locatifs afin de tenir compte du risque de vacance locative.`,
  seventhSection: `C’est pourquoi il sera fortement déconseillé de contracter de tels crédits si nous avons pour projet d’investir en immobilier prochainement.`,
  firstList: [
    {
      id: '1',
      title: 'Ce montant dépendra de 3 critères majeurs :',
      items: [
        {
          id: 'a1',
          text: ' Notre salaire ou revenus professionnels',
        },
        { id: 'a2', text: 'Les loyers perçus' },
        { id: 'a3', text: ' Les pensions, aides et autres revenus récurrents' },
      ],
    },
  ],
  secondList: [
    {
      id: '2',
      title: 'Seront considérés comme des « charges récurrentes » :',
      items: [
        {
          id: 'a1',
          text: 'Un éventuel loyer que nous payons si nous sommes locataires',
        },
        {
          id: 'a2',
          text: 'Le crédit immobilier d’une éventuelle résidence principale',
        },
        {
          id: 'a3',
          text: ' Les crédits immobiliers d’éventuelles résidences secondaires/locatives',
        },
        { id: 'a4', text: ' Les crédits à la consommation' },
      ],
    },
  ],
};
const onPlayerReady: YouTubeProps['onReady'] = (event) => {
  // access to player in all event handlers via event.target
  event.target.pauseVideo();
};
const opts: YouTubeProps['opts'] = {
  width: { base: '2xl', sm: '4xl' },
  height: '400',
  playerVars: {
    // https://developers.google.com/youtube/player_parameters
    autoplay: 1,
  },
};

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={5}
          gap={5}
          justifyContent="center"
        >
          <Button
            as="a"
            size="md"
            width="150px"
            border="1px"
            borderRadius={0}
            fontWeight="none"
            rightIcon={<IoIosArrowForward />}
            variant="outline"
            href="/table-of-contents"
          >
            Retour en arrière
          </Button>
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            {data.title}
          </Text>
          {/* <Container maxW="2xl" h="lg"> */}
          <YouTube videoId={data.videoId} opts={opts} onReady={onPlayerReady} />
          {/* </Container> */}
          <Text>{data.firstSection}</Text>
          <Text>{data.secondSection}</Text>
          <Text>{data.thirdSection}</Text>
          <Text>{data.fourthSection}</Text>
          <Text>{data.fifthSection}</Text>
          <>
            {data.firstList.map((element) => (
              <ListItems {...element} />
            ))}
          </>
          <Text fontStyle="italic">{data.sixthSection}</Text>
          <>
            {data.secondList.map((element) => (
              <ListItems {...element} />
            ))}
          </>
          <Text fontStyle="italic">{data.seventhSection}</Text>
        </Stack>
      </Box>
    </Center>
  );
}
