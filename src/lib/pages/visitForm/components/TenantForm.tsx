import { useState } from 'react';
import {
  Box,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Textarea,
  Divider,
  Flex,
  Text,
  Button,
  VStack,
  HStack,
  useColorModeValue,
  FormHelperText,
  Grid,
  Select,
} from '@chakra-ui/react';

function TenantForm() {
  const [formData, setFormData] = useState({
    nomPrenom: '',
    dateNaissance: '',
    nombrePersonnes: '',
    statutProfessionnel1: '',
    statutProfessionnel2: '',
    autre1: '',
    autre2: '',
    periodeEssai1: 'Non',
    periodeEssai2: 'Non',
    posteEmployeur1: '',
    posteEmployeur2: '',
    revenusMensuels1: '',
    revenusMensuels2: '',
    garant1: 'Oui',
    garant2: 'Oui',
    visale: 'Oui',
    dureeLocation: '1',
    demandesSpecifiques: '',
  });
  const dureesLocation = [
    { value: '1', label: '1 an' },
    { value: '2', label: '2 ans' },
    { value: '3-5', label: 'Entre 3 et 5 ans' },
    { value: '5+', label: 'Plus de 5 ans' },
  ];
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      p={{ base: 4, md: 6 }}
      maxW={{ base: '100%', md: '800px' }}
      mx="auto"
      minH="100vh"
    >
      <VStack
        spacing={4}
        p={{ base: 4, md: 6 }}
        bg={cardBg}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="md"
        boxShadow="md"
        align="stretch"
      >
        <Heading size="md" textAlign="center" color="teal.600" fontSize="xl">
          FICHE D'INFORMATION LOCATAIRE
        </Heading>

        {/* Basic Info */}
        <VStack spacing={3} align="stretch">
          <FormControl>
            <FormLabel fontWeight="medium" fontSize="md">
              Nom - Prénom
            </FormLabel>
            <Input
              name="nomPrenom"
              value={formData.nomPrenom}
              onChange={handleChange}
              size="md"
              focusBorderColor="teal.400"
            />
          </FormControl>

          <FormControl>
            <FormLabel fontWeight="medium" fontSize="md">
              Date de naissance
            </FormLabel>
            <Input
              name="dateNaissance"
              type="date"
              value={formData.dateNaissance}
              onChange={handleChange}
              size="md"
              focusBorderColor="teal.400"
            />
          </FormControl>

          <FormControl>
            <FormLabel fontWeight="medium" fontSize="md">
              Nombre de personnes à loger
            </FormLabel>
            <Input
              name="nombrePersonnes"
              type="number"
              value={formData.nombrePersonnes}
              onChange={handleChange}
              size="md"
              focusBorderColor="teal.400"
            />
          </FormControl>
        </VStack>

        {/* Professional Status (Statut professionnel) */}
        <Divider />
        <Text fontWeight="semibold" fontSize="md" color="teal.700">
          Statut professionnel
        </Text>
        <Grid
          templateColumns={{ base: '1fr', md: '1fr 1px 1fr' }}
          gap={3}
          bg={useColorModeValue('white', 'gray.600')}
          p={{ base: 3, md: 4 }}
          borderRadius="md"
          borderWidth="1px"
          borderColor={borderColor}
          boxShadow="sm"
        >
          {/* First Person */}
          <Box>
            <Text fontWeight="semibold" mb={2} fontSize="md" color="gray.700">
              Première personne
            </Text>
            <RadioGroup
              name="statutProfessionnel1"
              value={formData.statutProfessionnel1}
              onChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  statutProfessionnel1: value,
                }))
              }
            >
              <Stack spacing={2}>
                {[
                  'Étudiant',
                  'Sans activité',
                  'En stage',
                  'CDD',
                  'CDI',
                  'Autre',
                ].map((status) => (
                  <Radio
                    key={status}
                    value={status}
                    colorScheme="teal"
                    size="md"
                  >
                    <Text fontSize="md">{status}</Text>
                  </Radio>
                ))}
              </Stack>
            </RadioGroup>
            {formData.statutProfessionnel1 === 'Autre' && (
              <Input
                name="autre1"
                value={formData.autre1}
                onChange={handleChange}
                placeholder="Précisez votre statut"
                size="md"
                mt={2}
                focusBorderColor="teal.400"
                bg={useColorModeValue('gray.100', 'gray.500')}
                borderRadius="md"
              />
            )}
          </Box>

          {/* Separator (Vertical Line on Desktop) */}
          <Box
            h="auto"
            borderLeft={{ base: 'none', md: '1px solid lightgrey' }}
            borderColor="gray.100"
            display={{ base: 'none', md: 'block' }}
          />

          {/* Second Person */}
          <Box>
            <Text fontWeight="semibold" mb={2} fontSize="md" color="gray.700">
              Deuxième personne
            </Text>
            <RadioGroup
              name="statutProfessionnel2"
              value={formData.statutProfessionnel2}
              onChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  statutProfessionnel2: value,
                }))
              }
            >
              <Stack spacing={2}>
                {[
                  'Étudiant',
                  'Sans activité',
                  'En stage',
                  'CDD',
                  'CDI',
                  'Autre',
                ].map((status) => (
                  <Radio
                    key={status}
                    value={status}
                    colorScheme="teal"
                    size="md"
                  >
                    <Text fontSize="md">{status}</Text>
                  </Radio>
                ))}
              </Stack>
            </RadioGroup>
            {formData.statutProfessionnel2 === 'Autre' && (
              <Input
                name="autre2"
                value={formData.autre2}
                onChange={handleChange}
                placeholder="Précisez votre statut"
                size="md"
                mt={2}
                focusBorderColor="teal.400"
                bg={useColorModeValue('gray.100', 'gray.500')}
                borderRadius="md"
              />
            )}
          </Box>
        </Grid>

        {/* Trial Period (Période d’essai en cours) */}
        <Divider />
        <Text fontWeight="semibold" fontSize="md" color="teal.700">
          Période d’essai en cours
        </Text>
        <Flex
          direction={{ base: 'column', md: 'row' }}
          gap={{ base: 4, md: 0 }}
          align="stretch"
          bg={useColorModeValue('white', 'gray.600')}
          p={3}
          borderRadius="md"
          borderWidth="1px"
          borderColor={borderColor}
          boxShadow="sm"
        >
          <Grid
            templateColumns={{ base: '1fr', md: '1fr 1px 1fr' }}
            w="100%"
            gap={3}
          >
            {/* First Person */}
            <Box>
              <FormControl>
                <FormLabel fontWeight="medium" fontSize="md" color="gray.700">
                  Première personne
                </FormLabel>
                <RadioGroup
                  name="periodeEssai1"
                  value={formData.periodeEssai1}
                  onChange={(value) =>
                    setFormData((prev) => ({ ...prev, periodeEssai1: value }))
                  }
                >
                  <HStack spacing={3}>
                    <Radio value="Oui" colorScheme="teal" size="md">
                      <Text fontSize="md">Oui</Text>
                    </Radio>
                    <Radio value="Non" colorScheme="teal" size="md">
                      <Text fontSize="md">Non</Text>
                    </Radio>
                  </HStack>
                </RadioGroup>
              </FormControl>
            </Box>

            {/* Separator (Vertical Line on Desktop) */}
            <Box
              h="auto"
              borderLeft={{ base: 'none', md: '1px solid lightgrey' }}
              borderColor="teal.500"
              display={{ base: 'none', md: 'block' }}
            />

            {/* Second Person */}
            <Box>
              <FormControl>
                <FormLabel fontWeight="medium" fontSize="md" color="gray.700">
                  Deuxième personne
                </FormLabel>
                <RadioGroup
                  name="periodeEssai2"
                  value={formData.periodeEssai2}
                  onChange={(value) =>
                    setFormData((prev) => ({ ...prev, periodeEssai2: value }))
                  }
                >
                  <HStack spacing={3}>
                    <Radio value="Oui" colorScheme="teal" size="md">
                      <Text fontSize="md">Oui</Text>
                    </Radio>
                    <Radio value="Non" colorScheme="teal" size="md">
                      <Text fontSize="md">Non</Text>
                    </Radio>
                  </HStack>
                </RadioGroup>
              </FormControl>
            </Box>
          </Grid>
        </Flex>

        {/* Job & Employer + Income */}
        <Divider />
        <Text fontWeight="semibold" fontSize="md" color="teal.700">
          Informations professionnelles
        </Text>
        <Grid
          templateColumns={{ base: '1fr', md: '1fr 1px 1fr' }}
          gap={3}
          bg={useColorModeValue('white', 'gray.600')}
          p={{ base: 3, md: 4 }}
          borderRadius="md"
          borderWidth="1px"
          borderColor={borderColor}
          boxShadow="sm"
        >
          {/* First Person */}
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontWeight="medium" fontSize="md" color="gray.800">
                Poste & Employeur (1ère personne)
              </FormLabel>
              <Input
                name="posteEmployeur1"
                value={formData.posteEmployeur1}
                onChange={handleChange}
                size="md"
                focusBorderColor="teal.400"
                borderRadius="md"
              />
            </FormControl>
            <FormControl>
              <FormLabel fontWeight="medium" fontSize="md" color="gray.800">
                Revenus mensuels bruts (1ère personne)
              </FormLabel>
              <Input
                name="revenusMensuels1"
                value={formData.revenusMensuels1}
                onChange={handleChange}
                size="md"
                focusBorderColor="teal.400"
                borderRadius="md"
              />
            </FormControl>
          </VStack>

          {/* Separator (Vertical Line on Desktop) */}
          <Box
            h="auto"
            borderLeft={{ base: 'none', md: '1px solid lightgrey' }}
            borderColor="teal.500"
            display={{ base: 'none', md: 'block' }}
          />

          {/* Second Person */}
          <VStack spacing={3} align="stretch">
            <FormControl>
              <FormLabel fontWeight="medium" fontSize="md" color="gray.800">
                Poste & Employeur (2ème personne)
              </FormLabel>
              <Input
                name="posteEmployeur2"
                value={formData.posteEmployeur2}
                onChange={handleChange}
                size="md"
                focusBorderColor="teal.400"
                borderRadius="md"
              />
            </FormControl>
            <FormControl>
              <FormLabel fontWeight="medium" fontSize="md" color="gray.800">
                Revenus mensuels bruts (2ème personne)
              </FormLabel>
              <Input
                name="revenusMensuels2"
                value={formData.revenusMensuels2}
                onChange={handleChange}
                size="md"
                focusBorderColor="teal.400"
                borderRadius="md"
              />
            </FormControl>
          </VStack>
        </Grid>

        {/* Guarantors */}
        <Divider />
        <Text fontWeight="semibold" fontSize="md" color="teal.700">
          Garant(s) personne(s) physique(s)
        </Text>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <FormControl flex="1">
            <FormLabel fontWeight="medium" fontSize="md">
              Première personne
            </FormLabel>
            <RadioGroup
              name="garant1"
              value={formData.garant1}
              onChange={(value) =>
                setFormData((prev) => ({ ...prev, garant1: value }))
              }
            >
              <HStack spacing={3}>
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">Oui</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">Non</Text>
                </Radio>
              </HStack>
            </RadioGroup>
          </FormControl>
          <FormControl flex="1">
            <FormLabel fontWeight="medium" fontSize="md">
              Deuxième personne
            </FormLabel>
            <RadioGroup
              name="garant2"
              value={formData.garant2}
              onChange={(value) =>
                setFormData((prev) => ({ ...prev, garant2: value }))
              }
            >
              <HStack spacing={3}>
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">Oui</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">Non</Text>
                </Radio>
              </HStack>
            </RadioGroup>
          </FormControl>
        </Flex>

        {/* VISALE Guarantee */}
        <FormControl mt={4}>
          <FormLabel fontWeight="medium" fontSize="md">
            Éligible Garantie VISALE
          </FormLabel>
          <RadioGroup
            name="visale"
            value={formData.visale}
            onChange={(value) =>
              setFormData((prev) => ({ ...prev, visale: value }))
            }
          >
            <HStack spacing={3}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </HStack>
          </RadioGroup>
        </FormControl>

        {/* Rental Duration & Specific Requests */}
        <Divider />
        <FormControl>
          <FormLabel fontWeight="medium" fontSize="md">
            Durée de location totale souhaitée
          </FormLabel>
          <Select
            name="dureeLocation"
            value={formData.dureeLocation}
            onChange={handleChange}
            size="md"
            focusBorderColor="teal.400"
          >
            {dureesLocation.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        </FormControl>

        <FormControl>
          <FormLabel fontWeight="medium" fontSize="md">
            Demandes spécifiques
          </FormLabel>
          <Textarea
            name="demandesSpecifiques"
            value={formData.demandesSpecifiques}
            onChange={handleChange}
            size="md"
            focusBorderColor="teal.400"
            rows={4}
          />
        </FormControl>

        {/* Signature */}
        <Divider />
        <Text fontWeight="semibold" fontSize="md" color="teal.700">
          DATER ET SIGNER
        </Text>
        <FormControl>
          <Input
            placeholder="Date (JJ/MM/AAAA)"
            size="md"
            focusBorderColor="teal.400"
            mb={3}
          />
          <Input
            placeholder="Signature (tapez votre nom ou laissez vide)"
            size="md"
            focusBorderColor="teal.400"
            mt={2}
          />
          <FormHelperText color="gray.500" fontSize="sm">
            Pour une signature numérique, utilisez un outil dédié si requis.
          </FormHelperText>
        </FormControl>

        {/* Submit Button */}
        <Button
          colorScheme="teal"
          size="md"
          mt={4}
          onClick={() => console.log(formData)}
          _hover={{ transform: 'scale(1.05)', transition: 'all 0.2s' }}
        >
          Soumettre
        </Button>
      </VStack>
    </Box>
  );
}

export default TenantForm;
