'use client';

import {
  Heading,
  FormControl,
  Grid,
  GridItem,
  FormLabel,
  Input,
  Select,
  HStack,
  Radio,
  RadioGroup,
  Container,
  Box,
  FormErrorMessage,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import type { ChangeEvent } from 'react';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

import { BuyingOptions } from '~/lib/enums/BuyingOptions';
import type {
  IProfileInvestor,
  ITableAssetsInfo,
  SetParentFormDataType,
} from '~/lib/types/buyer-assessment';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

import AssetsInfoTable from './hooks/AssetsInfoTable';

interface Errors {
  buyingOption?: string;
}
// Interface for storing totals by tax regime
interface TaxRegimeTotals {
  [key: string]: number;
}

type IAssetsInfoTableRef = {
  rowData: ITableAssetsInfo[];
  taxRegimeTotals: TaxRegimeTotals;
  calculateTaxRegimeTotals: () => TaxRegimeTotals;
};
const buyingOptions = [
  BuyingOptions.SEUL,
  BuyingOptions.EN_COUPLE,
  // BuyingOptions.MEMBRES_FAMILLE_LIGNE_DIRECTE,
  // BuyingOptions.MEMBRES_FAMILLE_NON_LIGNE_DIRECTE,
];

const regimeMatrimonial = [
  'Marié sans contrat - communauté universelle',
  'Séparation de biens',
  'Participation aux acquêts',
];

const ProfileInvestor = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      data: IProfileInvestor;
    },
    ref
  ) => {
    const assetsInfoTableRef = useRef<IAssetsInfoTableRef | null>(null);

    const [formData, setFormData] = useState(props.data);
    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const { name, value } = e.target;
      setFormData((prevData) => ({
        ...prevData,
        [name]: value,
      }));
      setErrors({ ...errors, [name]: '' });
    };
    const [errors, setErrors] = useState<Errors>({
      buyingOption: '',
    });
    const validateForm = () => {
      const newErrors: Errors = {};
      if (!formData.buyingOption) newErrors.buyingOption = 'Champ est requis';

      setErrors({ ...errors, ...newErrors });
      return Object.keys(newErrors).length === 0;
    };
    const onSubmit = () => {
      if (validateForm()) {
        props.setParentFormData({
          ...formData,
          assetsInfoTable: assetsInfoTableRef.current?.rowData,
          taxRegimeTotals: assetsInfoTableRef.current?.taxRegimeTotals,
        });
        return true;
      }
      return false;
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box
        width="100%"
        maxWidth="1200px"
        mx="auto"
        p={[4, 6, 8]}
        boxShadow="lg"
        bg="white"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
      >
        <Container maxW="5xl" py={6}>
          <Heading
            as="h2"
            size="lg"
            textAlign="center"
            fontWeight="medium"
            mb={6}
          >
            Présentation des Régimes de la Location Meublée
          </Heading>

          <Grid templateColumns="repeat(2, 1fr)" gap={6}>
            {/* Buying Option */}
            <FormControl
              as={GridItem}
              colSpan={[2, 1]}
              isRequired
              isInvalid={errors.buyingOption !== ''}
            >
              <FormLabel fontWeight="medium">Vous achetez</FormLabel>
              <Select
                id="buyingOption"
                name="buyingOption"
                placeholder="Sélectionner une option"
                value={formData.buyingOption}
                onChange={handleInputChange}
                focusBorderColor="brand.400"
                shadow="sm"
                size="md"
                rounded="md"
              >
                {buyingOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.buyingOption}</FormErrorMessage>
            </FormControl>

            {/* Married Status */}
            {formData.buyingOption === BuyingOptions.SEUL && (
              <GridItem colSpan={[2, 1]}>
                <FormControl as="fieldset">
                  <FormLabel fontWeight="medium">Marié(e) ?</FormLabel>
                  <RadioGroup
                    name="isMarried"
                    value={formData.isMarried}
                    onChange={(value) =>
                      setFormData((prev) => ({ ...prev, isMarried: value }))
                    }
                  >
                    <HStack spacing="24px">
                      <Radio value="true">Oui</Radio>
                      <Radio value="false">Non</Radio>
                    </HStack>
                  </RadioGroup>
                </FormControl>
              </GridItem>
            )}
            {/* Matrimonial Regime */}
            <FormControl as={GridItem} colSpan={[2, 1]}>
              <FormLabel fontWeight="medium">
                Si Oui, sous quel régime matrimonial ?
              </FormLabel>
              <Select
                id="regimeMatrimonial"
                name="regimeMatrimonial"
                placeholder="Sélectionner un régime"
                value={formData.regimeMatrimonial}
                onChange={handleInputChange}
                focusBorderColor="brand.400"
                shadow="sm"
                size="md"
                rounded="md"
              >
                {regimeMatrimonial.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Warning about matrimonial regime */}
            {formData.isMarried === "true" && (
              <GridItem colSpan={2}>
                <Alert status="warning" borderRadius="md" py={2} mb={4}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    Attention : Si vous êtes marié(e) sans contrat, votre époux/épouse sera inclus(e) dans le projet d'investissement.
                  </AlertDescription>
                </Alert>
              </GridItem>
            )}

            {/* Project Members Count */}
            <FormControl as={GridItem} colSpan={[2, 1]}>
              <FormLabel fontWeight="medium">
                Nombre de personnes dans le projet
              </FormLabel>
              <Input
                id="numberOfPerson"
                name="numberOfPerson"
                type="number"
                onWheel={disableWheelOnInputNumber}
                placeholder="Entrez le nombre"
                value={formData.numberOfPerson}
                onChange={handleInputChange}
                focusBorderColor="brand.400"
                shadow="sm"
                size="md"
              />
              {formData.isMarried === "true" &&
               formData.regimeMatrimonial === "Marié sans contrat - communauté universelle" &&
               Number(formData.numberOfPerson) === 1 && (
                <Alert status="warning" mt={2} borderRadius="md" py={2}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    Attention : Étant marié(e) sans contrat de mariage, vous devez indiquer 2 personnes dans le projet.
                  </AlertDescription>
                </Alert>
              )}
            </FormControl>

            {/* Real Estate Ownership */}
            <GridItem colSpan={[2, 1]}>
              <FormControl as="fieldset">
                <FormLabel fontWeight="medium">
                  Possédez-vous déjà des biens immobiliers locatifs ?
                </FormLabel>
                <RadioGroup
                  name="ownsRealEstate"
                  value={formData.ownsRealEstate}
                  onChange={(value) =>
                    setFormData((prev) => ({ ...prev, ownsRealEstate: value }))
                  }
                >
                  <HStack spacing="24px">
                    <Radio value="true">Oui</Radio>
                    <Radio value="false">Non</Radio>
                  </HStack>
                </RadioGroup>
              </FormControl>
            </GridItem>

            {/* Number of Real Estate Properties */}
            <FormControl as={GridItem} colSpan={[2, 1]}>
              <FormLabel fontWeight="medium">
                Si Oui, combien de biens immobiliers détenez-vous ?
              </FormLabel>
              <Input
                id="numberOfRealEstate"
                name="numberOfRealEstate"
                type="number"
                onWheel={disableWheelOnInputNumber}
                placeholder="Entrez le nombre"
                value={formData.numberOfRealEstate}
                onChange={handleInputChange}
                focusBorderColor="brand.400"
                shadow="sm"
                size="md"
              />
            </FormControl>
          </Grid>

          {/* Property Management Section */}
          <Box mt={6}>
            <AssetsInfoTable
              ref={assetsInfoTableRef}
              data={formData.assetsInfoTable}
            />
          </Box>
        </Container>
      </Box>
    );
  }
);
export default ProfileInvestor;
