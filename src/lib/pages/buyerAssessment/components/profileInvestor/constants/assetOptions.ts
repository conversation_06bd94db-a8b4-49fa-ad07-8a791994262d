/**
 * Dropdown options for asset information forms
 * Each option has a key (used for value) and a label (displayed to user)
 */

export interface DropdownOption {
  key: string;
  label: string;
}

// Rental mode options
export const RENTAL_MODE_OPTIONS: DropdownOption[] = [
  { key: 'meublee', label: 'Meublée' },
  { key: 'nue', label: 'Nue' },
];

// Juridical status options
export const JURIDIC_STATUS_OPTIONS: DropdownOption[] = [
  { key: 'noms_propres', label: 'En noms propres' },
  { key: 'indivision', label: 'En indivision' },
  { key: 'sci_ir', label: "En SCI à l'IR" },
  { key: 'sci_is', label: "En SCI à l'IS" },
  { key: 'sarl_famille', label: 'En SARL de famille' },
  { key: 'sasu', label: 'En SASU' },
  { key: 'eurl', label: 'En EURL' },
  { key: 'snc', label: 'En SNC' },
  { key: 'sci_familiale', label: 'En SCI Familiale' },
  { key: 'na', label: 'N/A' },
];

// Fiscal status options
export const FISCAL_STATUS_OPTIONS: DropdownOption[] = [
  { key: 'lmnp', label: 'LMNP' },
  { key: 'lmp', label: 'LMP' },
  { key: 'pinel', label: 'PINEL' },
  { key: 'duflot', label: 'DUFLOT' },
  { key: 'na', label: 'N/A' },
];

// Applicable fiscal regime options
export const APPLICABLE_FISCAL_OPTIONS: DropdownOption[] = [
  { key: 'micro_fonciers', label: 'Micro-fonciers' },
  { key: 'fonciers_reel', label: 'Fonciers au régime réel' },
  { key: 'micro_bic', label: 'Micro-BIC' },
  { key: 'bic_reel_simplifie', label: 'BIC au régime réel simplifié' },
  { key: 'bic_reel', label: 'BIC au régime réel' },
];

// Helper functions to get labels and keys
export const getLabelByKey = (options: DropdownOption[], key: string): string => {
  const option = options.find(opt => opt.key === key);
  return option ? option.label : '';
};

export const getKeyByLabel = (options: DropdownOption[], label: string): string => {
  const option = options.find(opt => opt.label === label);
  return option ? option.key : '';
};
