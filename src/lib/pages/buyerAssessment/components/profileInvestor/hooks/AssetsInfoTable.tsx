import { DeleteIcon, PlusSquareIcon } from '@chakra-ui/icons';
import {
  Button,
  VStack,
  Heading,
  useDisclosure,
} from '@chakra-ui/react';
import type { CellValueChangedEvent, ColDef } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import {
  useState,
  useRef,
  useCallback,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from 'react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

import type { ITableAssetsInfo } from '~/lib/types/buyer-assessment';
import AssetFormModal from './AssetFormModal';
import {
  RENTAL_MODE_OPTIONS,
  JURIDIC_STATUS_OPTIONS,
  FISCAL_STATUS_OPTIONS,
  APPLICABLE_FISCAL_OPTIONS,
  getKeyByLabel,
} from '../constants/assetOptions';

// Interface for storing totals by tax regime
interface TaxRegimeTotals {
  [key: string]: number;
}

const AssetsInfoTable = forwardRef(
  (
    props: {
      data: ITableAssetsInfo[];
    },
    ref
  ) => {
    const [rowData, setRowData] = useState<ITableAssetsInfo[]>(props.data);
    const [taxRegimeTotals, setTaxRegimeTotals] = useState<TaxRegimeTotals>({});
    const gridRef = useRef<AgGridReact>(null);
    const { isOpen, onOpen, onClose } = useDisclosure();

    const defaultColDef = {
      sortable: true,
      filter: true,
      resizable: true,
      flex: 1,
      singleClickEdit: true,
    };

    // Function to calculate totals by tax regime
    const calculateTaxRegimeTotals = useCallback(() => {
      const totals: TaxRegimeTotals = {};

      rowData.forEach((asset) => {
        // The applicableTaxRegime should already be a key, but handle legacy data
        // that might still have labels stored
        let regimeKey = asset.applicableTaxRegime;

        // If it's not a valid key (e.g., it's a label from old data), try to get the key
        if (!APPLICABLE_FISCAL_OPTIONS.some(opt => opt.key === regimeKey)) {
          const foundKey = getKeyByLabel(APPLICABLE_FISCAL_OPTIONS, regimeKey);
          if (foundKey) {
            regimeKey = foundKey;
          }
        }

        const rent = parseFloat(asset.annualRent) || 0;

        if (regimeKey) {
          if (!totals[regimeKey]) {
            totals[regimeKey] = 0;
          }
          totals[regimeKey] += rent;
        }
      });

      setTaxRegimeTotals(totals);
      return totals;
    }, [rowData]);

    // Update totals whenever rowData changes
    useEffect(() => {
      calculateTaxRegimeTotals();
    }, [rowData, calculateTaxRegimeTotals]);

    // Function to open the add row modal
    const addRow = () => {
      onOpen();
    };

    // Handle form submission
    const handleAddAsset = (assetData: Omit<ITableAssetsInfo, 'number'>) => {
      const newRow: ITableAssetsInfo = {
        number: rowData.length + 1,
        ...assetData,
      };

      const updatedData = [...rowData, newRow];
      setRowData(updatedData);
    };

    // Function to Handle Cell Value Changes
    const onCellValueChanged = useCallback(
      (params: CellValueChangedEvent) => {
        // Make sure we're storing keys for dropdown fields
        const updatedData = rowData.map((row) => {
          if (row.number === params.data.number) {
            // The ag-grid editor already stores the key value, so we don't need to convert
            // The cellRenderer will handle displaying the label
            return { ...row, ...params.data };
          }
          return row;
        });
        setRowData(updatedData);

        // Recalculate tax regime totals when data changes
        calculateTaxRegimeTotals();
      },
      [rowData, calculateTaxRegimeTotals]
    );

    // Function to Delete a Row
    const deleteRow = (number: number) => {
      const updatedData = rowData.filter((row) => row.number !== number);
      // Reassign numbers to maintain sequence
      const reassignedData = updatedData.map((row, index) => ({
        ...row,
        number: index + 1,
      }));
      setRowData(reassignedData);
    };
    const localeText = {
      noRowsToShow: 'Aucune ligne à afficher',
    };
    // Column Definitions with Editable Cells and Dropdown Editors
    const columnDefs: ColDef[] = [
      {
        headerName: '#',
        field: 'number',
        width: 70,
        editable: false,
      },
      {
        headerName: 'Mode de location',
        field: 'rentalMode',
        flex: 1,
        minWidth: 150,
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: RENTAL_MODE_OPTIONS.map(option => option.label),
        },
        // Custom cell renderer to display the label instead of the key
        cellRenderer: (params: { value: string }) => {
          // Handle both key and label formats for backward compatibility
          const option = RENTAL_MODE_OPTIONS.find(opt =>
            opt.key === params.value || opt.label === params.value
          );
          return option ? option.label : params.value;
        },
      },
      {
        headerName:
          'Montant loyers annuels (charges comprises en meublée et hors charges en nue)',
        field: 'annualRent',
        flex: 1,
        minWidth: 200,
        editable: true,
        type: 'stringColumn',
      },
      {
        headerName: "Statut Juridique d'exploitation",
        field: 'legalStatus',
        flex: 1,
        minWidth: 200,
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: JURIDIC_STATUS_OPTIONS.map(option => option.label),
        },
        // Custom cell renderer to display the label instead of the key
        cellRenderer: (params: { value: string }) => {
          // Handle both key and label formats for backward compatibility
          const option = JURIDIC_STATUS_OPTIONS.find(opt =>
            opt.key === params.value || opt.label === params.value
          );
          return option ? option.label : params.value;
        },
      },
      {
        headerName: 'Statut fiscal particulier',
        field: 'specialTaxStatus',
        flex: 1,
        minWidth: 200,
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: FISCAL_STATUS_OPTIONS.map(option => option.label),
        },
        // Custom cell renderer to display the label instead of the key
        cellRenderer: (params: { value: string }) => {
          // Handle both key and label formats for backward compatibility
          const option = FISCAL_STATUS_OPTIONS.find(opt =>
            opt.key === params.value || opt.label === params.value
          );
          return option ? option.label : params.value;
        },
      },
      {
        headerName: 'Régime Fiscal Applicable',
        field: 'applicableTaxRegime',
        flex: 1,
        minWidth: 200,
        editable: true,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: APPLICABLE_FISCAL_OPTIONS.map(option => option.label),
        },
        // Custom cell renderer to display the label instead of the key
        cellRenderer: (params: { value: string }) => {
          // Handle both key and label formats for backward compatibility
          const option = APPLICABLE_FISCAL_OPTIONS.find(opt =>
            opt.key === params.value || opt.label === params.value
          );
          return option ? option.label : params.value;
        },
      },
      {
        field: 'button',
        flex: 1,
        minWidth: 150,
        headerName: 'Actions',
        cellRenderer: (params: { data: { number: number } }) => {
          const handleDelete = () => {
            const numberToDelete = params.data.number;
            deleteRow(numberToDelete);
          };
          return (
            <Button
              onClick={handleDelete}
              _hover={{ bg: 'none' }}
              variant="ghost"
              leftIcon={<DeleteIcon />}
            />
          );
        },
      },
    ];
    // Column Types
    const columnTypes = {
      stringColumn: {
        editable: true,
        filter: 'agTextColumnFilter',
        cellEditor: 'agTextCellEditor',
      },
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      rowData,
      taxRegimeTotals,
      calculateTaxRegimeTotals,
    }));
    return (
      <>
        <Heading noOfLines={3} size="md" fontSize="16px" mt="2%">
          Pour chacun des biens immobiliers locatifs détenus, nous vous
          remercions de renseigner le mode de location ainsi que le régime
          fiscal correspondant ci-dessous :{' '}
        </Heading>
        <VStack align="stretch" flexWrap="wrap" flexDirection="row-reverse">
          <Button
            colorScheme="teal"
            leftIcon={<PlusSquareIcon />}
            onClick={addRow}
          >
            Ajouter un bien
          </Button>
          <div
            className="ag-theme-alpine"
            style={{
              width: '100%',
            }}
          >
            <AgGridReact
              ref={gridRef}
              rowData={rowData}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              columnTypes={columnTypes}
              onCellValueChanged={onCellValueChanged}
              domLayout="autoHeight"
              localeText={localeText}
              animateRows
            />
          </div>
        </VStack>

        {/* Asset Form Modal */}
        <AssetFormModal
          isOpen={isOpen}
          onClose={onClose}
          onSubmit={handleAddAsset}
        />
      </>
    );
  }
);

export default AssetsInfoTable;
