import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  FormErrorMessage,
  Button,
  Text,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  VStack,
  Heading,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import type { ITableAssetsInfo } from '~/lib/types/buyer-assessment';
import {
  RENTAL_MODE_OPTIONS,
  JURIDIC_STATUS_OPTIONS,
  FISCAL_STATUS_OPTIONS,
  APPLICABLE_FISCAL_OPTIONS,
} from '../constants/assetOptions';

// Form validation interface
interface FormErrors {
  rentalMode: string;
  annualRent: string;
  legalStatus: string;
  specialTaxStatus: string;
  applicableTaxRegime: string;
}

interface AssetFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (asset: Omit<ITableAssetsInfo, 'number'>) => void;
}

const AssetFormModal = ({ isOpen, onClose, onSubmit }: AssetFormModalProps) => {
  // Form state for the modal
  const [formData, setFormData] = useState<Omit<ITableAssetsInfo, 'number'>>({
    rentalMode: '',
    annualRent: '',
    legalStatus: '',
    specialTaxStatus: '',
    applicableTaxRegime: '',
  });

  // Form validation state
  const [errors, setErrors] = useState<FormErrors>({
    rentalMode: '',
    annualRent: '',
    legalStatus: '',
    specialTaxStatus: '',
    applicableTaxRegime: '',
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        rentalMode: '',
        annualRent: '',
        legalStatus: '',
        specialTaxStatus: '',
        applicableTaxRegime: '',
      });
      setErrors({
        rentalMode: '',
        annualRent: '',
        legalStatus: '',
        specialTaxStatus: '',
        applicableTaxRegime: '',
      });
    }
  }, [isOpen]);

  // Note: We're storing keys in the form data but displaying labels in the UI
  // This approach allows us to have consistent internal data while providing
  // user-friendly display values

  // Handle form input changes
  const handleInputChange = (
    field: keyof Omit<ITableAssetsInfo, 'number'>,
    value: string
  ) => {
    // Store the key value in the form data
    setFormData({
      ...formData,
      [field]: value,
    });

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: '',
      });
    }
  };

  // Validate form data
  const validateForm = (): boolean => {
    const newErrors = { ...errors };
    let isValid = true;

    // Check each field
    (Object.keys(formData) as Array<keyof typeof formData>).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = 'Ce champ est requis';
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Submit form
  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
      onClose();
    }
  };

  // UI colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const headerBgColor = useColorModeValue('teal.50', 'teal.900');

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay backdropFilter="blur(2px)" />
      <ModalContent borderRadius="md" boxShadow="xl">
        <ModalHeader
          bg={headerBgColor}
          borderTopRadius="md"
          color="teal.700"
          py={4}
        >
          <Heading size="md">Ajouter un bien immobilier</Heading>
        </ModalHeader>
        <ModalCloseButton color="teal.700" />

        <ModalBody py={6}>
          <VStack spacing={6} align="stretch">
            {/* Mode de location */}
            <FormControl isRequired isInvalid={!!errors.rentalMode}>
              <FormLabel fontWeight="medium" color="gray.700">Mode de location</FormLabel>
              <Select
                placeholder="Sélectionner un mode de location"
                value={formData.rentalMode}
                onChange={(e) => handleInputChange('rentalMode', e.target.value)}
                focusBorderColor="teal.400"
                bg={bgColor}
                borderColor={borderColor}
                _hover={{ borderColor: 'teal.300' }}
              >
                {RENTAL_MODE_OPTIONS.map((option) => (
                  <option key={option.key} value={option.key}>
                    {option.label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.rentalMode}</FormErrorMessage>
            </FormControl>

            {/* Montant loyers annuels */}
            <FormControl isRequired isInvalid={!!errors.annualRent}>
              <FormLabel fontWeight="medium" color="gray.700">
                Montant loyers annuels
              </FormLabel>
              <Text fontSize="xs" color="gray.500" mt="-1" mb="2">
                (charges comprises en meublée et hors charges en nue)
              </Text>
              <InputGroup>
                <InputLeftElement
                  pointerEvents="none"
                  color="gray.500"
                  fontSize="1.2em"
                  children="€"
                />
                <Input
                  placeholder="Montant des loyers annuels"
                  value={formData.annualRent}
                  onChange={(e) => handleInputChange('annualRent', e.target.value)}
                  focusBorderColor="teal.400"
                  bg={bgColor}
                  borderColor={borderColor}
                  _hover={{ borderColor: 'teal.300' }}
                  pl="8"
                />
              </InputGroup>
              <FormErrorMessage>{errors.annualRent}</FormErrorMessage>
            </FormControl>

            {/* Statut Juridique d'exploitation */}
            <FormControl isRequired isInvalid={!!errors.legalStatus}>
              <FormLabel fontWeight="medium" color="gray.700">Statut Juridique d'exploitation</FormLabel>
              <Select
                placeholder="Sélectionner un statut juridique"
                value={formData.legalStatus}
                onChange={(e) => handleInputChange('legalStatus', e.target.value)}
                focusBorderColor="teal.400"
                bg={bgColor}
                borderColor={borderColor}
                _hover={{ borderColor: 'teal.300' }}
              >
                {JURIDIC_STATUS_OPTIONS.map((option) => (
                  <option key={option.key} value={option.key}>
                    {option.label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.legalStatus}</FormErrorMessage>
            </FormControl>

            {/* Statut fiscal particulier */}
            <FormControl isRequired isInvalid={!!errors.specialTaxStatus}>
              <FormLabel fontWeight="medium" color="gray.700">Statut fiscal particulier</FormLabel>
              <Select
                placeholder="Sélectionner un statut fiscal"
                value={formData.specialTaxStatus}
                onChange={(e) => handleInputChange('specialTaxStatus', e.target.value)}
                focusBorderColor="teal.400"
                bg={bgColor}
                borderColor={borderColor}
                _hover={{ borderColor: 'teal.300' }}
              >
                {FISCAL_STATUS_OPTIONS.map((option) => (
                  <option key={option.key} value={option.key}>
                    {option.label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.specialTaxStatus}</FormErrorMessage>
            </FormControl>

            {/* Régime Fiscal Applicable */}
            <FormControl isRequired isInvalid={!!errors.applicableTaxRegime}>
              <FormLabel fontWeight="medium" color="gray.700">Régime Fiscal Applicable</FormLabel>
              <Select
                placeholder="Sélectionner un régime fiscal"
                value={formData.applicableTaxRegime}
                onChange={(e) => handleInputChange('applicableTaxRegime', e.target.value)}
                focusBorderColor="teal.400"
                bg={bgColor}
                borderColor={borderColor}
                _hover={{ borderColor: 'teal.300' }}
              >
                {APPLICABLE_FISCAL_OPTIONS.map((option) => (
                  <option key={option.key} value={option.key}>
                    {option.label}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>{errors.applicableTaxRegime}</FormErrorMessage>
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter bg="gray.50" borderBottomRadius="md">
          <Button
            variant="outline"
            mr={3}
            onClick={onClose}
            _hover={{ bg: 'gray.100' }}
          >
            Annuler
          </Button>
          <Button
            colorScheme="teal"
            onClick={handleSubmit}
            _hover={{ bg: 'teal.600' }}
            boxShadow="sm"
          >
            Ajouter
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AssetFormModal;
