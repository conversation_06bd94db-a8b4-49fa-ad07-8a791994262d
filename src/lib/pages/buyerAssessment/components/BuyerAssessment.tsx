import { Box, Progress, useSteps } from '@chakra-ui/react';
import { useRef, useState } from 'react';

import { BuyingOptions } from '~/lib/enums/BuyingOptions';
import type { FormDataBuyerAssessmentSteps } from '~/lib/types/buyer-assessment';

import { FormWrapper } from './couple/FormWrapper';
import { StepperComponent } from '~/lib/components/commonComponents/multistep/StepperComponent';
import { StepperNavigationButtons } from '~/lib/components/commonComponents/multistep/StepperNavigationButtons';

export default function Multistep() {
  const steps = [
    { title: 'PROFIL INVESTISSEUR' },
    { title: 'SITUATION PROFESSIONNELLE ET FAMILIALE' },
    { title: 'SITUATION ECONOMIQUE ET FISCALE' },
    { title: 'FINANCEMENTS ET PATRIMOINE' },
    { title: 'MOTIVATIONS ET OBJECTIFS' },
    { title: 'BUDGET & ENDETTEMENT' },
  ];

  const [formData, setFormData] = useState<FormDataBuyerAssessmentSteps>({
    profileInvestorData: {
      buyingOption: '',
      isMarried: 'false',
      regimeMatrimonial: '',
      numberOfPerson: '',
      ownsRealEstate: 'true',
      numberOfRealEstate: '',
      assetsInfoTable: [],
    },
    professionalFamilyData: {
      lastName: '',
      firstName: '',
      profession: '',
      email: '',
      phone: '',
      gender: 'M',
      marriageStatus: '',
      statutProfessionnel: '',
      typeContrat: '',
      statutProfessionnelConjoint: '',
      typeContratConjoint: '',
      celibataire: '',
      marriageDate: '',
      regime: '',
      livingTogether: '',
      previousMarriage: '',
      pacsStatus: '',
      pacsDate: '',
      veufStatus: '',
      veufDate: '',
      concubinageStatus: '',
      concubinageAmount: '',
      alimonyStatus: '',
      alimonyAmount: '',
      numberOfChildren: '',
      fiscallyChargedChildren: '',
      successionsDetails: '',
    },
    financeAndHeritageData: {
      patrimoineForm: {
        ownsPatrimoine: 'non',
        patrimoineTable: [],
        totalRevenue: '',
      },
      financeForm: {
        outstandingCredit: 'false',
        savings: 'false',
        savingsAmount: '0',
        contribution: 'false',
        contributionAmount: '0',
        monthlyPaymentsTotal: '0',
        amountBorrowedTotal: '0',
        credits: [],
      },
    },
    motivationsObjectivesData: {
      priorities: {
        patrimony: '',
        diversify: '',
        additionalIncome: '',
        debtRepayment: '',
        investmentCapacity: '',
        organizeTransmission: '',
        taxReduction: '',
        resaleValue: '',
      },
      objectives: {
        transmit: 'false',
        resale: 'false',
        conserve: 'false',
        usagePersonal: 'false',
        researchZone: 'false',
      },
      propertyDetails: {
        acquisitionDuration: '',
        buyWith: 'false',
        maritalStatus: '',
        numberOfPeople: '',
        propertyType: '',
        numberOfRooms: '',
        furnished: '',
        rentalType: '',
        rentalDuration: '',
        researchLocation: '',
        searchThrough: '',
        propertyManagement: '',
        additionalDescription: '',
      },
    },
    economicData: {
      status: '',
      homeownerStatus: 'true',
      rowDataTable: [],
      financialDetails: {
        numOfParts: '',
        fiscalIncome: '',
        incomeTax: '',
        salaryOtherIncome: '',
        bicIncome: false,
        bncIncome: false,
        otherIncome: false,
        capitalIncome: false,
        propertyIncome: false,
        isfLiable: 'false',
        isfNet: '',
        futurePartsThreeYears: '',
        futurePartsFiveYears: '',
      },
    },
  });
  const formRefs = steps.map(() => useRef<any>(null));

  const { activeStep, setActiveStep } = useSteps({
    index: 0,
    count: steps.length,
  });
  const isLastStep = activeStep === steps.length - 1;

  const onNextStep = async () => {
    const currentFormRef = formRefs[activeStep];
    const isValid = await currentFormRef.current?.onSubmit();
    if (isValid && activeStep + 1 < steps.length) setActiveStep(activeStep + 1);
  };
  const validateBeforeSetActiveStep = async (nextStep: number) => {
    const currentFormRef = formRefs[activeStep];
    const isValid = await currentFormRef.current?.onSubmit();
    if (isValid) setActiveStep(nextStep);
  };

  const previousStep = () => {
    setActiveStep(Math.max(activeStep - 1, 0));
    formRefs[activeStep - 1].current?.setDataFromParent(formData);
  };
  const handleFormDataChange = (key, data) => {
    setFormData({ ...formData, [key]: data });

    if (activeStep === 0) {
      formData.professionalFamilyData.marriageStatus = (
        data.isMarried === 'true' ||
        data.buyingOption === BuyingOptions.EN_COUPLE
      ).toString();
      formData.motivationsObjectivesData.propertyDetails.maritalStatus =
        data.regimeMatrimonial;
    }
  };
  return (
    <Box
      borderWidth="1px"
      rounded="lg"
      shadow="1px 1px 3px rgba(0,0,0,0.3)"
      p={8}
      bg="white"
      m="10px auto"
      as="form"
      width="100%"
      maxWidth="1200px"
      mx="auto"
      boxShadow="lg"
      borderRadius="md"
    >
      <Progress
        value={(activeStep / (steps.length - 1)) * 100}
        size="xs"
        colorScheme="teal"
        mb={6}
      />
      <StepperComponent
        steps={steps}
        activeStep={activeStep}
        setActiveStep={validateBeforeSetActiveStep}
      />
      <FormWrapper
        activeStep={activeStep}
        formRefs={formRefs}
        formData={formData}
        handleFormDataChange={handleFormDataChange}
      />
      <StepperNavigationButtons
        isLastStep={isLastStep}
        onNextStep={onNextStep}
        previousStep={previousStep}
        activeStep={activeStep}
      />
    </Box>
  );
}
