import {
  Box,
  FormControl,
  FormLabel,
  Heading,
  Radio,
  RadioGroup,
  Stack,
  VStack,
} from '@chakra-ui/react';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

import type {
  IEconomicData,
  IProfileInvestor,
  SetParentFormDataType,
} from '~/lib/types/buyer-assessment';
import {
  getColDefForEconomicTable,
  getDataForEconomicTable,
} from '~/lib/utils/economicDataTransform';

import EditableBudgetTable from './EditableBudgetTable';
import FiscalSituationForm from './FiscalSituationForm';

type FiscalSituationFormRef = {
  onSubmit: () => void;
  formData: IEconomicData;
};

type EditableBudgetTableRef = {
  rowData: object;
};
const EconomicForm = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      data: IEconomicData;
      profileInvestorData: IProfileInvestor;
    },
    ref
  ) => {
    const [formData, setFormData] = useState({
      homeownerStatus: props.data.homeownerStatus,
      status: props.data.status,
    });

    const rowData = props.data.rowDataTable.length
      ? props.data.rowDataTable
      : getDataForEconomicTable(props.profileInvestorData.buyingOption);
    const columnDefs = getColDefForEconomicTable(
      props.profileInvestorData.buyingOption
    );
    // Create refs with the defined types
    const fiscalSituationFormRef = useRef<FiscalSituationFormRef | null>(null);
    const editableBudgetTableRef = useRef<EditableBudgetTableRef | null>(null);

    const onSubmit = () => {
      fiscalSituationFormRef.current?.onSubmit();
      props.setParentFormData({
        ...formData,
        financialDetails: fiscalSituationFormRef.current?.formData,
        rowDataTable: editableBudgetTableRef.current?.rowData,
      });
      return true;
    };
    const handleRadioChange = (value: string, name: string) => {
      setFormData({ ...formData, [name]: value });
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box p={8} borderWidth={1} borderRadius="md">
        <VStack spacing={6}>
          <Box p={8} borderWidth={1} borderRadius="md" w="full">
            <Heading
              as="h3"
              size="lg"
              mb={4}
              mt={4}
              display="flex"
              justifyContent="center"
            >
              Situation Economique
            </Heading>
            {/* Homeownership Status */}
            <FormControl mb={2}>
              <FormLabel>
                Etes-vous propriétaire de votre résidence principale ?
              </FormLabel>
              <RadioGroup
                value={formData.homeownerStatus}
                name="homeownerStatus"
                onChange={(value) =>
                  handleRadioChange(value, 'homeownerStatus')
                }
              >
                <Stack direction="row">
                  <Radio value="true">Oui</Radio>{' '}
                  <Radio value="false">Non</Radio>
                </Stack>
              </RadioGroup>
            </FormControl>
            {/* Tenant Status */}
            {formData.homeownerStatus === 'false' && (
              <FormControl>
                <FormLabel>Auquel cas, êtes-vous :</FormLabel>
                <RadioGroup
                  value={formData.status}
                  name="status"
                  onChange={(value) => handleRadioChange(value, 'status')}
                >
                  <Stack direction="row">
                    <Radio value="gratuit">Logé à titre gratuit</Radio>
                    <Radio value="Locataire">Locataire</Radio>
                    <Radio value="Autre">Autre</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>
            )}
            <Heading
              as="h3"
              size="md"
              mb={4}
              mt={4}
              display="flex"
              justifyContent="center"
            >
              Budget Mensuel Du Foyer Fiscal Mensuel (Approximatif)
            </Heading>
            <EditableBudgetTable
              ref={editableBudgetTableRef}
              rowData={rowData}
              columnDefs={columnDefs}
            />
          </Box>
          <FiscalSituationForm
            ref={fiscalSituationFormRef}
            data={props.data.financialDetails}
            profileInvestorData={props.profileInvestorData}
          />
        </VStack>
      </Box>
    );
  }
);

export default EconomicForm;
