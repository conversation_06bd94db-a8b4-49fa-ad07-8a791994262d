import {
  Box,
  FormControl,
  FormLabel,
  Input,
  RadioGroup,
  Stack,
  Radio,
  Select,
  Divider,
  Heading,
  Checkbox,
  Collapse,
  VStack,
} from '@chakra-ui/react';
import type { ChangeEvent } from 'react';
import { forwardRef, useImperativeHandle, useState } from 'react';

import type {
  IFinancialDetails,
  IProfileInvestor,
} from '~/lib/types/buyer-assessment';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

import { calculateIncomeTax } from './TaxCalculation';

const FiscalSituationForm = forwardRef(
  (
    props: { data: IFinancialDetails; profileInvestorData: IProfileInvestor },
    ref
  ) => {
    const [formData, setFormData] = useState(props.data);

    const handleInputChange = (
      e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
      const { name, value } = e.target;
      setFormData({ ...formData, [name]: value });
    };
    const calulateNet = () => {
      if (formData.fiscalIncome) {
        const numberOfParts = parseInt(formData.numOfParts) ?? 1;
        const incomeTax = calculateIncomeTax(
          parseInt(formData.fiscalIncome) / numberOfParts
        );

        setFormData({ ...formData, incomeTax: incomeTax.toString() });
      }
    };
    const handleCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = e.target;
      setFormData({ ...formData, [name]: checked });
    };

    const handleRadioChange = (value: string, name: string) => {
      setFormData({ ...formData, [name]: value });
    };

    const onSubmit = () => {};

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
      formData,
    }));
    return (
      <Box p={8} borderWidth={1} borderRadius="md" w="full">
        <Heading
          as="h3"
          size="lg"
          mb={4}
          display="flex"
          justifyContent="center"
        >
          Situation Fiscale
        </Heading>
        <VStack spacing={6} alignItems="flex-start">
          <FormControl>
            <FormLabel fontStyle="italic" fontWeight="bold">
              N.B. : Les renseignements fiscaux sont ceux de la dernière année
              civile (tous membres du foyer fiscal réunis)
            </FormLabel>
          </FormControl>

          {/* Number of Parts */}
          <FormControl isRequired>
            <FormLabel>Nombre de Parts</FormLabel>
            <Select
              placeholder="Sélectionnez le nombre de parts"
              name="numOfParts"
              value={formData.numOfParts}
              onChange={handleInputChange}
            >
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
            </Select>
          </FormControl>
          {/* Evolution Parts Prévisible */}
          <FormControl>
            <FormLabel>Evolution Parts Prévisible (À trois ans)</FormLabel>
            <Input
              placeholder="Nombre"
              name="futurePartsThreeYears"
              type="number"
              onWheel={disableWheelOnInputNumber}
              value={formData.futurePartsThreeYears}
              onChange={handleInputChange}
            />
          </FormControl>
          <FormControl>
            <FormLabel>Evolution Parts Prévisible (À cinq ans)</FormLabel>
            <Input
              placeholder="Nombre"
              name="futurePartsFiveYears"
              type="number"
              onWheel={disableWheelOnInputNumber}
              value={formData.futurePartsFiveYears}
              onChange={handleInputChange}
            />
          </FormControl>
          {/* Revenu Fiscal de Référence */}
          <FormControl isRequired>
            <FormLabel>Revenu Fiscal de Référence</FormLabel>
            <Input
              placeholder="Revenu Fiscal"
              name="fiscalIncome"
              type="number"
              onWheel={disableWheelOnInputNumber}
              value={formData.fiscalIncome}
              onChange={handleInputChange}
              onBlur={calulateNet}
            />
          </FormControl>
          {/* Impôts sur le Revenu */}
          <FormControl isRequired>
            <FormLabel>Impôt sur le Revenu</FormLabel>
            <Input
              placeholder="Montant des impôts"
              name="incomeTax"
              type="number"
              onWheel={disableWheelOnInputNumber}
              value={formData.incomeTax}
              onChange={handleInputChange}
            />
          </FormControl>
          {/* Other Salary Income */}
          <FormControl isRequired>
            <FormLabel>
              Avez-vous des revenus autres que des salaires ?
            </FormLabel>
            <RadioGroup
              name="salaryOtherIncome"
              defaultValue={props.profileInvestorData.ownsRealEstate}
              onChange={(value) =>
                handleRadioChange(value, 'salaryOtherIncome')
              }
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>
          <Collapse
            in={formData.salaryOtherIncome === 'true'}
            animateOpacity
            style={{ width: '100%' }}
          >
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md" width="100%">
              <FormControl>
                <FormLabel>Revenus déclarés dans la catégorie BIC</FormLabel>
                <Checkbox
                  name="bicIncome"
                  isChecked={formData.bicIncome}
                  onChange={handleCheckboxChange}
                >
                  Oui
                </Checkbox>
              </FormControl>

              <FormControl>
                <FormLabel>Revenus déclarés dans la catégorie BNC</FormLabel>
                <Checkbox
                  name="bncIncome"
                  isChecked={formData.bncIncome}
                  onChange={handleCheckboxChange}
                >
                  Oui
                </Checkbox>
              </FormControl>

              <FormControl>
                <FormLabel>Autres revenus</FormLabel>
                <Checkbox
                  name="otherIncome"
                  isChecked={formData.otherIncome}
                  onChange={handleCheckboxChange}
                >
                  Oui
                </Checkbox>
              </FormControl>

              <FormControl>
                <FormLabel>Revenus de Capitaux Mobiliers</FormLabel>
                <Checkbox
                  name="capitalIncome"
                  isChecked={formData.capitalIncome}
                  onChange={handleCheckboxChange}
                >
                  Oui
                </Checkbox>
              </FormControl>

              <FormControl>
                <FormLabel>Revenus Fonciers</FormLabel>
                <Checkbox
                  name="propertyIncome"
                  isChecked={formData.propertyIncome}
                  onChange={handleCheckboxChange}
                >
                  Oui
                </Checkbox>
              </FormControl>
            </Box>
          </Collapse>
          {/* ISF Liability */}
          <FormControl isRequired>
            <FormLabel>
              Êtes-vous redevable de l'Impôt Sur la Fortune (ISF) ?
            </FormLabel>
            <RadioGroup
              name="isfLiable"
              defaultValue="false"
              value={formData.isfLiable}
              onChange={(value) => handleRadioChange(value, 'isfLiable')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>
          <Collapse
            in={formData.isfLiable === 'true'}
            animateOpacity
            style={{ width: '100%' }}
          >
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md" width="100%">
              <FormControl>
                <FormLabel>Patrimoine Taxable Net Déclaré</FormLabel>
                <Input
                  placeholder="ISF Net"
                  name="isfNet"
                  type="number"
                  onWheel={disableWheelOnInputNumber}
                  value={formData.isfNet}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Box>
          </Collapse>
          <Divider mt={6} />
        </VStack>
      </Box>
    );
  }
);

export default FiscalSituationForm;
