const TAX_BRACKETS = [
  { limit: 11498, rate: 0 },
  { limit: 29316, rate: 0.11 },
  { limit: 83824, rate: 0.3 },
  { limit: 180295, rate: 0.41 },
  { limit: Infinity, rate: 0.45 },
];
/**
 * Calculate the taxable income
 * @param income
 * @returns
 */
export const calculateIncomeTax = (income: number): number => {
  let tax = 0;
  let previousLimit = 0;
  for (const bracket of TAX_BRACKETS) {
    if (income > bracket.limit) {
      tax += (bracket.limit - previousLimit) * bracket.rate;
      previousLimit = bracket.limit;
    } else {
      tax += (income - previousLimit) * bracket.rate;
      break;
    }
  }
  return parseFloat(tax.toFixed(2));
};
export const getTaxBracket = (income: number) => {
  for (const bracket of TAX_BRACKETS) {
    if (income <= bracket.limit) {
      return bracket.rate;
    }
  }
  return 0;
};
