import { AgGridReact } from 'ag-grid-react';
import { useState, useRef, forwardRef, useImperativeHandle } from 'react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import type { CellValueChangedEvent, ColDef } from 'ag-grid-community';

import type { IEconomicRowDataTable } from '~/lib/types/buyer-assessment';

const EditableBudgetTable = forwardRef(
  (props: { rowData: IEconomicRowDataTable[]; columnDefs: ColDef[] }, ref) => {
    const gridRef = useRef<AgGridReact>(null);

    // Row data for the table
    const [rowData, setRowData] = useState(props.rowData);

    const onCellValueChange = (params: CellValueChangedEvent) => {
      // When any cell changes, update the state (for controlled data updates)
      const updatedRows = [...rowData];
      if (params.node.rowIndex)
        updatedRows[params.node.rowIndex] = { ...params.data };
      setRowData(updatedRows);
    };

    const defaultColDef = {
      flex: 1,
      minWidth: 150,
      editable: true,
      resizable: true,
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      rowData,
    }));
    // Column Types
    const columnTypes = {
      stringColumn: {
        editable: true,
        filter: 'agTextColumnFilter',
        cellEditor: 'agTextCellEditor',
      },
    };
    return (
      <div
        className="ag-theme-alpine"
        style={{ height: '100%', width: '100%' }}
      >
        <AgGridReact
          ref={gridRef}
          rowData={rowData}
          columnTypes={columnTypes}
          columnDefs={props.columnDefs}
          domLayout="autoHeight"
          defaultColDef={defaultColDef}
          onCellValueChanged={onCellValueChange}
        />
      </div>
    );
  }
);

export default EditableBudgetTable;
