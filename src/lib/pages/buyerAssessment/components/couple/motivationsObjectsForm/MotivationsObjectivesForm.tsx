import {
  Box,
  VStack,
  <PERSON>ing,
  FormControl,
  FormLabel,
  Input,
  RadioGroup,
  Stack,
  Radio,
} from '@chakra-ui/react';
import type { ChangeEvent } from 'react';
import { forwardRef, useImperativeHandle, useState } from 'react';

import type {
  IMotivationsObjectivesData,
  SetParentFormDataType,
} from '~/lib/types/buyer-assessment';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

import MotivationSelector from './MotivationSelector'; // Importing the MotivationSelector component

const MotivationsObjectivesForm = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      data: IMotivationsObjectivesData;
    },
    ref
  ) => {
    const [priorities, setPriorities] = useState(props.data.priorities);
    const [objectives, setObjectives] = useState(props.data.objectives);
    const [propertyDetails, setPropertyDetails] = useState(
      props.data.propertyDetails
    );

    const handlePrioritiesChange = (e: ChangeEvent<HTMLSelectElement>) => {
      const { name, value } = e.target;
      setPriorities({ ...priorities, [name]: value });
    };

    const handleObjectivesChange = (value: string, name: string) => {
      setObjectives({ ...objectives, [name]: value });
    };

    const handlePropertyDetailsChange = (
      e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
      const { name, value } = e.target;
      setPropertyDetails({ ...propertyDetails, [name]: value });
    };

    const onSubmit = () => {
      props.setParentFormData({
        priorities,
        objectives,
        propertyDetails,
      });
      return true;
    };

    // Options for Select components
    const selectOptions = {
      acquisitionDuration: [
        { key: 'moins_de_20_ans', value: 'Moins de 20 ans' },
        { key: 'plus_de_20_ans', value: 'Plus de 20 ans' },
      ],
      propertyType: [
        { key: 'appartement', value: 'Appartement' },
        { key: 'maison', value: 'Maison' },
        { key: 'immeuble_de_rapport', value: 'Immeuble de rapport' },
        { key: 'peu_importe', value: 'Peu Importe' },
      ],
      furnished: [
        { key: 'nue', value: 'Nue' },
        { key: 'meublee', value: 'Meublée' },
        { key: 'peu_importe', value: 'Peu Importe' },
      ],
      rentalType: [
        { key: 'ancien', value: 'Ancien' },
        { key: 'neuf', value: 'Neuf' },
      ],
      rentalDuration: [
        { key: 'longue_duree', value: 'Longue durée' },
        { key: 'courte_duree', value: 'Courte durée' },
        { key: 'peu_importe', value: 'Peu importe' },
      ],
      searchThrough: [
        { key: 'seul', value: 'Seul' },
        { key: 'intermediaire', value: "Par le biais d'un intermédiaire" },
      ],
      propertyManagement: [
        { key: 'gestion_directe_totale', value: 'Gestion directe totale' },
        {
          key: 'gestion_externalisee_totale',
          value: 'Gestion externalisée totale',
        },
        { key: 'gestion_partielle', value: 'Gestion partielle' },
      ],
      numberOfRooms: [
        { key: 'studio', value: 'Studio' },
        { key: '2_pieces', value: '2 pièces' },
        { key: '3_pieces', value: '3 pièces' },
        { key: '4_pieces', value: '4 pièces' },
        { key: '5_pieces', value: '5 pièces' },
      ],
      commonOptions: [
        { key: '1', value: '1' },
        { key: '2', value: '2' },
        { key: '3', value: '3' },
      ],
    };

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box p={8} borderWidth={1} borderRadius="md">
        <VStack spacing={6} alignItems="flex-start">
          <Heading as="h3" size="lg" mb={4}>
            Motivations et Objectifs
          </Heading>

          {/* Section 1: Priorities for Investing */}
          <Heading as="h4" size="md" mb={2}>
            Si vous deviez sélectionner 3 raisons qui vous incitent à investir
            dans un bien immobilier locatif :
          </Heading>

          <MotivationSelector
            name="patrimony"
            value={priorities.patrimony}
            onChange={handlePrioritiesChange}
            options={selectOptions.commonOptions}
            label="Me constituer un patrimoine progressivement"
          />

          <MotivationSelector
            name="diversify"
            value={priorities.diversify}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Diversifier mon patrimoine"
          />

          <MotivationSelector
            name="additionalIncome"
            value={priorities.additionalIncome}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Obtenir des revenus complémentaires"
          />

          <MotivationSelector
            name="debtRepayment"
            value={priorities.debtRepayment}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Vivre de cette activité"
          />

          <MotivationSelector
            name="investmentCapacity"
            value={priorities.investmentCapacity}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Moyen de placement de ma capacité d’épargne"
          />

          <MotivationSelector
            name="organizeTransmission"
            value={priorities.organizeTransmission}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Organiser et optimiser la transmission de mon patrimoine"
          />

          <MotivationSelector
            name="taxReduction"
            value={priorities.taxReduction}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Obtenir des réductions d’impôts"
          />

          <MotivationSelector
            name="resaleValue"
            value={priorities.resaleValue}
            options={selectOptions.commonOptions}
            onChange={handlePrioritiesChange}
            label="Réduire la plus-value à la revente"
          />

          {/* Section 2: Long-term Objectives */}
          <Heading as="h4" size="md" mb={2}>
            Citez à présent vos objectifs à long terme :
          </Heading>

          <FormControl as="fieldset">
            <FormLabel>Transmettre le patrimoine immobilier locatif</FormLabel>
            <RadioGroup
              name="transmit"
              value={objectives.transmit}
              onChange={(value) => {
                handleObjectivesChange(value, 'transmit');
              }}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <FormControl as="fieldset">
            <FormLabel>
              Vendre le patrimoine immobilier pour dégager une plus-value
            </FormLabel>
            <RadioGroup
              name="resale"
              value={objectives.resale}
              onChange={(value) => {
                handleObjectivesChange(value, 'resale');
              }}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <FormControl as="fieldset">
            <FormLabel>
              Conserver le patrimoine pour préparer sa retraite
            </FormLabel>
            <RadioGroup
              name="conserve"
              value={objectives.conserve}
              onChange={(value) => {
                handleObjectivesChange(value, 'conserve');
              }}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <FormControl as="fieldset">
            <FormLabel>
              Conserver le patrimoine dans le cadre d’un usage personnel
            </FormLabel>
            <RadioGroup
              name="usagePersonal"
              value={objectives.usagePersonal}
              onChange={(value) => {
                handleObjectivesChange(value, 'usagePersonal');
              }}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          {/* Section 3: Property Acquisition Details */}
          <Heading as="h4" size="md" mb={2}>
            Détails de l'acquisition
          </Heading>

          <MotivationSelector
            name="acquisitionDuration"
            value={propertyDetails.acquisitionDuration}
            options={selectOptions.acquisitionDuration}
            onChange={handlePropertyDetailsChange}
            label="Pendant quelle durée désirez-vous conserver le bien ?"
          />

          <FormControl>
            <FormLabel>Achetez-vous avec conjoint(e) ?</FormLabel>
            <RadioGroup
              name="buyWith"
              value={propertyDetails.buyWith}
              onChange={(value) => {
                handlePropertyDetailsChange({
                  target: { value, name: 'buyWith' },
                });
              }}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <FormControl>
            <FormLabel>Rappel régime matrimonial</FormLabel>
            <Input
              name="maritalStatus"
              value={propertyDetails.maritalStatus}
              readOnly
              onChange={handlePropertyDetailsChange}
            />
          </FormControl>

          <FormControl>
            <FormLabel>
              Combien serez-vous à investir dans le projet ?
            </FormLabel>
            <Input
              name="numberOfPeople"
              type="number"
              onWheel={disableWheelOnInputNumber}
              value={propertyDetails.numberOfPeople}
              onChange={handlePropertyDetailsChange}
            />
          </FormControl>

          <MotivationSelector
            name="propertyType"
            value={propertyDetails.propertyType}
            options={selectOptions.propertyType}
            onChange={handlePropertyDetailsChange}
            label="Quel type de bien recherchez-vous ?"
          />

          <MotivationSelector
            name="furnished"
            value={propertyDetails.furnished}
            options={selectOptions.furnished}
            onChange={handlePropertyDetailsChange}
            label="Vous envisagez la location ?"
          />

          <MotivationSelector
            name="numberOfRooms"
            value={propertyDetails.numberOfRooms}
            options={selectOptions.numberOfRooms}
            onChange={handlePropertyDetailsChange}
            label="Nombre de pièces recherché ?"
          />

          <MotivationSelector
            name="rentalType"
            value={propertyDetails.rentalType}
            options={selectOptions.rentalType}
            onChange={handlePropertyDetailsChange}
            label="Quelle catégorie de bien recherchez-vous ?"
          />

          <MotivationSelector
            name="rentalDuration"
            value={propertyDetails.rentalDuration}
            options={selectOptions.rentalDuration}
            onChange={handlePropertyDetailsChange}
            label="Catégorie de location"
          />

          <FormControl>
            <FormLabel>
              Avez-vous ciblé une zone de recherche du bien ?
            </FormLabel>
            <RadioGroup
              name="researchZone"
              value={objectives.researchZone}
              onChange={(value) => {
                handleObjectivesChange(value, 'researchZone');
              }}
            >
              <Stack direction="row">
                {' '}
                <Radio value="true">Oui</Radio> <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <FormControl>
            <FormLabel>
              Si oui, dans quelle(s) ville(s) et/ou quelle(s) région(s)?
            </FormLabel>
            <Input
              name="researchLocation"
              value={propertyDetails.researchLocation}
              onChange={handlePropertyDetailsChange}
            />
          </FormControl>

          <MotivationSelector
            name="searchThrough"
            value={propertyDetails.searchThrough}
            options={selectOptions.searchThrough}
            onChange={handlePropertyDetailsChange}
            label="Comment comptez-vous rechercher le bien à investir ?"
          />

          <MotivationSelector
            name="propertyManagement"
            value={propertyDetails.propertyManagement}
            options={selectOptions.propertyManagement}
            onChange={handlePropertyDetailsChange}
            label="Comment comptez-vous gérer le futur bien à acquérir ?"
          />

          <FormControl>
            <FormLabel>Autres précisions déterminantes à ajouter :</FormLabel>
            <Input
              name="additionalDescription"
              value={propertyDetails.additionalDescription}
              onChange={handlePropertyDetailsChange}
            />
          </FormControl>
        </VStack>
      </Box>
    );
  }
);

export default MotivationsObjectivesForm;
