import { FormControl, FormLabel, Select } from '@chakra-ui/react';
import type React from 'react';
import type { ChangeEvent } from 'react';

interface MotivationSelectorProps {
  label: string;
  name: string;
  value: string;
  options: { value: string; key: string }[];
  onChange: (e: ChangeEvent<HTMLSelectElement>) => void;
}

const MotivationSelector: React.FC<MotivationSelectorProps> = ({
  label,
  name,
  value,
  options,
  onChange,
}) => {
  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <Select
        name={name}
        value={value}
        onChange={onChange}
        placeholder="Selectionner"
      >
        {options.map((option) => (
          <option key={option.key} value={option.value}>
            {option.value}
          </option>
        ))}
      </Select>
    </FormControl>
  );
};

export default MotivationSelector;
