import {
  Box,
  FormControl,
  FormLabel,
  Input,
  RadioGroup,
  Stack,
  Radio,
  Select,
  Divider,
  Heading,
  Collapse,
  FormErrorMessage,
} from '@chakra-ui/react';
import type { ChangeEvent } from 'react';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { BuyingOptions } from '~/lib/enums/BuyingOptions';

import type {
  SetParentFormDataType,
  FormDataBuyerAssessmentSteps,
  IProfessionalFamilyData,
} from '~/lib/types/buyer-assessment';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

const regimeOptions = [
  'Séparation de biens',
  'Marié sans contrat - Communauté universelle',
  'Participation aux acquêts',
];

const ProfessionalFamilyForm = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      buyingOption: string;
      data: IProfessionalFamilyData;
    },
    ref
  ) => {
    const [formData, setFormData] = useState(props.data);

    const generateFiscallyChargedOptions = (numChildren: number) => {
      const options = [];
      for (let i = 0; i <= numChildren; i += 1) {
        options.push(
          <option key={i} value={i}>
            {i}
          </option>
        );
      }
      return options;
    };

    const handleInputChange = (
      e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
      const { name, value } = e.target;
      setFormData({ ...formData, [name]: value });
      setErrors({ ...errors, [name]: '' });
    };
    const handleRadioChange = (value: string, name: string) => {
      setFormData({ ...formData, [name]: value });
    };
    interface Errors {
      lastName?: string;
      firstName?: string;
      profession?: string;
      email?: string;
      phone?: string;
      numberOfChildren?: string;
    }

    const [errors, setErrors] = useState<Errors>({
      lastName: '',
      firstName: '',
      profession: '',
      email: '',
      phone: '',
      numberOfChildren: '',
    });

    const validateForm = () => {
      const newErrors: Errors = {};
      if (!formData.lastName) newErrors.lastName = 'Nom est requis';
      if (!formData.firstName) newErrors.firstName = 'Prénom est requis';
      if (!formData.profession) newErrors.profession = 'Profession est requise';
      if (!formData.email) newErrors.email = 'Adresse Courriel est requise';
      if (!formData.phone) newErrors.phone = 'Téléphone Portable est requis';
      if (!formData.numberOfChildren)
        newErrors.numberOfChildren = "Nombre d'enfants est requis";
      setErrors({ ...errors, ...newErrors });
      return Object.keys(newErrors).length === 0;
    };

    const onSubmit = () => {
      if (validateForm()) {
        props.setParentFormData(formData);
        return true;
      }
      return false;
    };
    const setDataFromParent = (data: FormDataBuyerAssessmentSteps) => {
      setFormData(data.professionalFamilyData);
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
      setDataFromParent,
    }));
    return (
      <Box p={8} borderWidth={1} borderRadius="md">
        <Stack spacing={6}>
          <Heading
            as="h3"
            size="lg"
            display="flex"
            justifyContent="center"
            mb={4}
          >
            Situation Professionnelle et Familiale
          </Heading>

          {/* Name and First Name */}
          <Stack direction="row" spacing={4}>
            <FormControl isRequired isInvalid={errors.lastName !== ''}>
              <FormLabel>Nom</FormLabel>
              <Input
                placeholder="Nom"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
              />
              <FormErrorMessage>{errors.lastName}</FormErrorMessage>
            </FormControl>
            <FormControl isRequired isInvalid={errors.firstName !== ''}>
              <FormLabel>Prénom</FormLabel>
              <Input
                placeholder="Prénom"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
              />
              <FormErrorMessage>{errors.firstName}</FormErrorMessage>
            </FormControl>
          </Stack>

          {/* Profession */}
          <FormControl isRequired isInvalid={errors.profession !== ''}>
            <FormLabel>Profession</FormLabel>
            <Input
              placeholder="Profession"
              name="profession"
              value={formData.profession}
              onChange={handleInputChange}
            />
            <FormErrorMessage>{errors.profession}</FormErrorMessage>
          </FormControl>

          {/* Email and Phone */}
          <Stack direction="row" spacing={4}>
            <FormControl isRequired isInvalid={errors.email !== ''}>
              <FormLabel>Adresse Courriel</FormLabel>
              <Input
                type="email"
                placeholder="Adresse Courriel"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
              />
              <FormErrorMessage>{errors.email}</FormErrorMessage>
            </FormControl>
            <FormControl isRequired isInvalid={errors.phone !== ''}>
              <FormLabel>Téléphone Portable</FormLabel>
              <Input
                placeholder="Téléphone Portable"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
              />
              <FormErrorMessage>{errors.phone}</FormErrorMessage>
            </FormControl>
          </Stack>

          {/* Sexe */}
          <FormControl isRequired>
            <FormLabel>Sexe</FormLabel>
            <RadioGroup
              name="gender"
              value={formData.gender}
              onChange={(value) => handleRadioChange(value, 'gender')}
            >
              <Stack direction="row">
                <Radio value="M">M</Radio>
                <Radio value="F">F</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>
          {/* Celibataire */}
          <FormControl>
            <FormLabel>Celibataire ?</FormLabel>
            <RadioGroup
              name="celibataire"
              defaultValue="false"
              onChange={(value) => handleRadioChange(value, 'celibataire')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          {/* Marital Status */}
          <FormControl isRequired>
            <FormLabel>Marié(e) ?</FormLabel>
            <RadioGroup
              name="marriageStatus"
              defaultValue={formData.marriageStatus}
              onChange={(value) => handleRadioChange(value, 'marriageStatus')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <Collapse in={formData.marriageStatus === 'true'} animateOpacity>
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md">
              <Heading as="h4" size="md" mb={4}>
                Détails du Mariage
              </Heading>
              <FormControl>
                <FormLabel>Date de Mariage</FormLabel>
                <Input
                  type="date"
                  name="marriageDate"
                  value={formData.marriageDate}
                  onChange={handleInputChange}
                />
              </FormControl>
              <FormControl mt={4}>
                <FormLabel>Régime</FormLabel>
                <Select
                  name="regime"
                  value={formData.regime}
                  onChange={handleInputChange}
                >
                  {regimeOptions.map((regime, index) => (
                    <option key={index} value={regime}>
                      {regime}
                    </option>
                  ))}
                </Select>
              </FormControl>
              <FormControl mt={4}>
                <FormLabel>Vivez-vous sous le même toit ?</FormLabel>
                <RadioGroup
                  name="livingTogether"
                  defaultValue="false"
                  onChange={(value) =>
                    handleRadioChange(value, 'livingTogether')
                  }
                >
                  <Stack direction="row">
                    <Radio value="true">Oui</Radio>
                    <Radio value="false">Non</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>
            </Box>
          </Collapse>

          {/* Statut Professionnel */}
          <FormControl mt={4}>
            <FormLabel>Statut Professionnel ?</FormLabel>
            <RadioGroup
              name="statutProfessionnel"
              defaultValue="A son compte"
              onChange={(value) =>
                handleRadioChange(value, 'statutProfessionnel')
              }
            >
              <Stack direction="row">
                <Radio value="A son compte">A son compte</Radio>
                <Radio value="Salarié(e)">Salarié(e)</Radio>
                <Radio value="En formation">En formation</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          {/* Type de Contrat */}
          {formData.statutProfessionnel === 'Salarié(e)' && (
            <FormControl mt={4}>
              <FormLabel>Type de Contrat ?</FormLabel>
              <RadioGroup
                name="typeContrat"
                defaultValue="CDD"
                onChange={(value) => handleRadioChange(value, 'typeContrat')}
              >
                <Stack direction="row">
                  <Radio value="CDD">CDD</Radio>
                  <Radio value="CDI">CDI</Radio>
                  <Radio value="Temps partiel">Temps partiel</Radio>
                </Stack>
              </RadioGroup>
            </FormControl>
          )}

          {/* Statut Professionnel du Conjoint */}
          {props.buyingOption === BuyingOptions.EN_COUPLE && (
            <>
              <FormControl mt={4}>
                <FormLabel>Statut Professionnel du Conjoint ?</FormLabel>
                <RadioGroup
                  name="statutProfessionnelConjoint"
                  defaultValue="A son compte"
                  onChange={(value) =>
                    handleRadioChange(value, 'statutProfessionnelConjoint')
                  }
                >
                  <Stack direction="row">
                    <Radio value="A son compte">A son compte</Radio>
                    <Radio value="Salarié(e)">Salarié(e)</Radio>
                    <Radio value="En formation">En formation</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>

              {/* Type de Contrat du Conjoint */}
              {formData.statutProfessionnelConjoint === 'Salarié(e)' && (
                <FormControl mt={4}>
                  <FormLabel>Type de Contrat du Conjoint ?</FormLabel>
                  <RadioGroup
                    name="typeContratConjoint"
                    defaultValue="CDD"
                    onChange={(value) =>
                      handleRadioChange(value, 'typeContratConjoint')
                    }
                  >
                    <Stack direction="row">
                      <Radio value="CDD">CDD</Radio>
                      <Radio value="CDI">CDI</Radio>
                      <Radio value="Temps partiel">Temps partiel</Radio>
                    </Stack>
                  </RadioGroup>
                </FormControl>
              )}
            </>
          )}

          {/* PACS Section */}
          <FormControl>
            <FormLabel>PACS ?</FormLabel>
            <RadioGroup
              name="pacsStatus"
              defaultValue="false"
              onChange={(value) => handleRadioChange(value, 'pacsStatus')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <Collapse in={formData.pacsStatus === 'true'} animateOpacity>
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md">
              <Heading as="h4" size="md" mb={4}>
                Détails du PACS
              </Heading>
              <FormControl>
                <FormLabel>Date du PACS</FormLabel>
                <Input
                  type="date"
                  name="pacsDate"
                  value={formData.pacsDate}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Box>
          </Collapse>

          {/* Veuf Section */}
          <FormControl>
            <FormLabel>Veuf ?</FormLabel>
            <RadioGroup
              name="veufStatus"
              defaultValue="false"
              onChange={(value) => handleRadioChange(value, 'veufStatus')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <Collapse in={formData.veufStatus === 'true'} animateOpacity>
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md">
              <Heading as="h4" size="md" mb={4}>
                Détails du Veuf
              </Heading>
              <FormControl>
                <FormLabel>Date de décès du conjoint</FormLabel>
                <Input
                  type="date"
                  name="veufDate"
                  value={formData.veufDate}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Box>
          </Collapse>

          {/* Concubinage Section */}
          <FormControl>
            <FormLabel>Concubinage ?</FormLabel>
            <RadioGroup
              name="concubinageStatus"
              defaultValue="false"
              onChange={(value) =>
                handleRadioChange(value, 'concubinageStatus')
              }
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <Collapse in={formData.concubinageStatus === 'true'} animateOpacity>
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md">
              <FormControl>
                <FormLabel>Montant concubinage</FormLabel>
                <Input
                  placeholder="Montant concubinage"
                  name="concubinageAmount"
                  value={formData.concubinageAmount}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Box>
          </Collapse>

          {/* Children and Fiscally Charged */}
          <FormControl isRequired isInvalid={errors.numberOfChildren !== ''}>
            <FormLabel>Nombre d'enfants</FormLabel>
            <Input
              placeholder="Nombre d'enfants"
              type="number"
              name="numberOfChildren"
              onWheel={disableWheelOnInputNumber}
              value={formData.numberOfChildren}
              onChange={handleInputChange}
            />
            <FormErrorMessage>{errors.numberOfChildren}</FormErrorMessage>
          </FormControl>

          <FormControl>
            <FormLabel>Nombre d’enfants fiscalement à charge</FormLabel>
            <Select
              name="fiscallyChargedChildren"
              value={formData.fiscallyChargedChildren}
              onChange={handleInputChange}
            >
              {generateFiscallyChargedOptions(
                Number(formData.numberOfChildren)
              )}
            </Select>
          </FormControl>

          {/* Alimony */}
          <FormControl>
            <FormLabel>Pension alimentaire ?</FormLabel>
            <RadioGroup
              name="alimonyStatus"
              defaultValue="false"
              onChange={(value) => handleRadioChange(value, 'alimonyStatus')}
            >
              <Stack direction="row">
                <Radio value="true">Oui</Radio>
                <Radio value="false">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          <Collapse in={formData.alimonyStatus === 'true'} animateOpacity>
            <Box mt={4} p={4} borderWidth="1px" borderRadius="md">
              <FormControl>
                <FormLabel>Montant pension alimentaire</FormLabel>
                <Input
                  placeholder="Montant pension alimentaire"
                  name="alimonyAmount"
                  value={formData.alimonyAmount}
                  onChange={handleInputChange}
                />
              </FormControl>
            </Box>
          </Collapse>

          {/* Successions */}
          <FormControl>
            <FormLabel>
              Détails des successions effectuées ou préparées
            </FormLabel>
            <Input
              placeholder=""
              name="successionsDetails"
              value={formData.successionsDetails}
              onChange={handleInputChange}
            />
          </FormControl>

          <Divider mt={6} />
        </Stack>
      </Box>
    );
  }
);

export default ProfessionalFamilyForm;
