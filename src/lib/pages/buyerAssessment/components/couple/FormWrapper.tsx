import ProfileInvestor from '../profileInvestor/ProfileInvestor';
import type { FormDataBuyerAssessmentSteps } from '~/lib/types/buyer-assessment';

import { calculateBudgetData } from './buget/BudgetCalculation';
import FullBudgetForm from './buget/FullBudgetForm';
import EconomicForm from './enconomicAndFiscal/EconomicForm';
import FinanceAndHeritageForm from './financingAndHeritage/FinanceAndHeritageForm';
import MotivationsObjectivesForm from './motivationsObjectsForm/MotivationsObjectivesForm';
import ProfessionalFamilyForm from './proffesionalSituation/ProfessionalFamilyForm';

interface FormWrapperProps {
  activeStep: number;
  formRefs: Array<React.RefObject<any>>;
  formData: FormDataBuyerAssessmentSteps;
  handleFormDataChange: <T extends keyof FormDataBuyerAssessmentSteps>(
    formName: T,
    newData: FormDataBuyerAssessmentSteps[T]
  ) => void;
}

export function FormWrapper({
  activeStep,
  formRefs,
  formData,
  handleFormDataChange,
}: FormWrapperProps) {
  return (
    <>
      {activeStep === 0 && (
        <ProfileInvestor
          ref={formRefs[0]}
          data={formData.profileInvestorData}
          setParentFormData={(data) =>
            handleFormDataChange('profileInvestorData', data)
          }
        />
      )}
      {activeStep === 1 && (
        <ProfessionalFamilyForm
          ref={formRefs[1]}
          data={formData.professionalFamilyData}
          buyingOption={formData.profileInvestorData.buyingOption}
          setParentFormData={(data) =>
            handleFormDataChange('professionalFamilyData', data)
          }
        />
      )}
      {activeStep === 2 && (
        <EconomicForm
          ref={formRefs[2]}
          data={formData.economicData}
          profileInvestorData={formData.profileInvestorData}
          setParentFormData={(data) =>
            handleFormDataChange('economicData', data)
          }
        />
      )}
      {activeStep === 3 && (
        <FinanceAndHeritageForm
          data={formData.financeAndHeritageData}
          ref={formRefs[3]}
          buyingOption={formData.profileInvestorData.buyingOption}
          setParentFormData={(data) =>
            handleFormDataChange('financeAndHeritageData', data)
          }
        />
      )}
      {activeStep === 4 && (
        <MotivationsObjectivesForm
          data={formData.motivationsObjectivesData}
          ref={formRefs[4]}
          setParentFormData={(data) =>
            handleFormDataChange('motivationsObjectivesData', data)
          }
        />
      )}
      {activeStep === 5 && (
        <FullBudgetForm data={calculateBudgetData(formData)} />
      )}
    </>
  );
}
