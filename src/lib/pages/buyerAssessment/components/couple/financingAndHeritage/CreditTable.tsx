import { But<PERSON>, <PERSON>lex, <PERSON>ack, Text } from '@chakra-ui/react';
import { AgGridReact } from 'ag-grid-react';
import type React from 'react';

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import type { ICreditTable } from '~/lib/types/buyer-assessment';

import type { CellValueChangedEvent } from 'ag-grid-community';

interface CreditTableProps {
  data: {
    credits: ICreditTable[];
    amountBorrowedTotal: string;
    monthlyPaymentsTotal: string;
  };
  onCreditChange: (id: number, field: string, value: string) => void;
  onAddRow: () => void;
}

const CreditTable: React.FC<CreditTableProps> = ({
  data,
  onCreditChange,
  onAddRow,
}) => {
  const { credits, amountBorrowedTotal, monthlyPaymentsTotal } = data;
  const columnDefs = [
    { headerName: 'Type de Crédit', field: 'type', editable: true },
    {
      headerName: 'Date de début',
      field: 'startDate',
      editable: true,
      cellEditor: 'agTextCellEditor',
      cellEditorParams: { useFormatter: true, type: 'date' },
    },
    {
      headerName: 'Montant Emprunté',
      field: 'amount',
      editable: true,
      cellEditor: 'agTextCellEditor',
    },
    {
      headerName: 'Mensualité',
      field: 'monthlyPayment',
      editable: true,
      cellEditor: 'agTextCellEditor',
    },
    {
      headerName: 'Crédit contracté seul ou avec conjoint',
      field: 'contractType',
      editable: true,
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: { values: ['Seul', 'A deux'] },
    },
  ];
  const onCellValueChange = (params: CellValueChangedEvent) => {
    // When any cell changes, update the state (for controlled data updates)
    if (params.colDef.field)
      onCreditChange(params.data.id, params.colDef.field, params.newValue);
  };
  // Column Types
  const columnTypes = {
    stringColumn: {
      editable: true,
      filter: 'agTextColumnFilter',
      cellEditor: 'agTextCellEditor',
    },
  };
  return (
    <>
      <Button colorScheme="teal" onClick={onAddRow} mb={4}>
        Ajouter une ligne
      </Button>
      <div className="ag-theme-alpine" style={{ height: 200, width: '100%' }}>
        <AgGridReact
          rowData={credits}
          columnDefs={columnDefs}
          columnTypes={columnTypes}
          defaultColDef={{ editable: true, resizable: true }}
          onCellValueChanged={onCellValueChange}
        />
      </div>
      {/* Total Row */}
      <Flex alignItems="center" justifyContent="space-between">
        <Text fontWeight="bold">Total:</Text>
        <Stack direction="row" spacing={4}>
          <Text>{`Montant Emprunté: ${amountBorrowedTotal}€`}</Text>
          <Text>{`Mensualité: ${monthlyPaymentsTotal}€`}</Text>
        </Stack>
      </Flex>
    </>
  );
};

export default CreditTable;
