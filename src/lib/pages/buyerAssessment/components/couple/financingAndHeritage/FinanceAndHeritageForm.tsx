import { forwardRef, useImperativeHandle, useRef } from 'react';

import type {
  IFinanceAndHeritage,
  SetParentFormDataType,
} from '~/lib/types/buyer-assessment';

import FinanceForm from './FinanceForm';
import PatrimoineForm from './PatrimoineForm';

const FinanceAndHeritageForm = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      data: IFinanceAndHeritage;
      buyingOption: string;
    },
    ref
  ) => {
    const financeFormRef = useRef<any>(null);
    const patrimoineFormRef = useRef<any>(null);
    const onSubmit = () => {
      props.setParentFormData({
        patrimoineForm: patrimoineFormRef.current.patrimoineForm,
        financeForm: financeFormRef.current.formData,
      });
      return true;
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <>
        <FinanceForm ref={financeFormRef} data={props.data.financeForm} />
        <PatrimoineForm
          ref={patrimoineFormRef}
          data={props.data.patrimoineForm}
          buyingOption={props.buyingOption}
        />
      </>
    );
  }
);

export default FinanceAndHeritageForm;
