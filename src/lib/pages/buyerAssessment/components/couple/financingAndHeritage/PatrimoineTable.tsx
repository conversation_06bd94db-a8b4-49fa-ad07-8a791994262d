import { Box, Button } from '@chakra-ui/react';
import { AgGridReact } from 'ag-grid-react';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import type { CellValueChangedEvent, ColDef } from 'ag-grid-community';

import type { IPatrimoineTable } from '~/lib/types/buyer-assessment';

interface PatrimoineTableProps {
  gridData: IPatrimoineTable[];
  columnDefs: ColDef[];
  isCouple: boolean;
  setGridData: (data: IPatrimoineTable[]) => void;
}

const PatrimoineTable = forwardRef(
  (
    { gridData, setGridData, columnDefs, isCouple }: PatrimoineTableProps,
    ref
  ) => {
    const [totalMonsieur, setTotalMonsieur] = useState(0);
    const [totalMadame, setTotalMadame] = useState(0);
    const [totalRevenue, settotalRevenue] = useState(0);
    const [totalPatrimoineImmo, setTotalPatrimoineImmo] = useState(0);

    const defaultColDef = {
      sortable: true,
      filter: true,
      flex: 1,
      resizable: true,
    };

    // Function to calculate totals
    // Refactore !!!

    useEffect(() => {
      if (!gridData || gridData.length === 0) return;

      let totalMonsieur = 0;
      let totalMadame = 0;
      let totalRevenue = 0;
      let totalPatrimoineImmo = 0;

      gridData.forEach((row) => {
        totalMonsieur += Number(row.monsieur) || 0;
        if (isCouple) {
          totalMadame += Number(row.madame) || 0;
        }
        totalRevenue += Number(row.revenu) || 0;
        totalPatrimoineImmo += Number(row.total);
      });

      // Update state
      setTotalMonsieur(totalMonsieur);
      if (isCouple) {
        setTotalMadame(totalMadame);
      }
      settotalRevenue(totalRevenue);
      setTotalPatrimoineImmo(totalPatrimoineImmo);
    }, [
      gridData,
      isCouple,
      setTotalMonsieur,
      setTotalMadame,
      settotalRevenue,
      setTotalPatrimoineImmo,
    ]);

    // Function to add a new row
    const addRow = () => {
      const newRow = {
        bien: `Bien N°${gridData.length + 1}`,
        monsieur: 0,
        madame: 0,
        nature: '',
        revenu: 0,
        patrimoineImmobilier: 0,
      };
      setGridData([...gridData, newRow]);
    };

    const onCellValueChange = (params: CellValueChangedEvent) => {
      const updatedData = gridData.map((row, index) =>
        index === params.rowIndex && params.colDef.field
          ? { ...row, [params.colDef.field]: params.newValue }
          : row
      );
      setGridData(updatedData);
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      totalRevenue,
    }));
    // Column Types
    const columnTypes = {
      stringColumn: {
        editable: true,
        filter: 'agTextColumnFilter',
        cellEditor: 'agTextCellEditor',
      },
    };
    return (
      <>
        {/* Button to add a new row */}
        <Button onClick={addRow} colorScheme="green" mb={4}>
          Ajouter une ligne
        </Button>

        {/* Ag-Grid Table */}
        <div className="ag-theme-alpine" style={{ height: 200, width: '100%' }}>
          <AgGridReact
            rowData={gridData}
            columnDefs={columnDefs}
            columnTypes={columnTypes}
            defaultColDef={defaultColDef}
            onCellValueChanged={onCellValueChange}
          />
        </div>

        {/* Total row */}
        <Box
          mt={4}
          p={4}
          bg="gray.50"
          borderRadius="md"
          borderWidth={1}
          w="full"
        >
          <strong>Total:</strong>
          <Box display="flex" mt={2}>
            <Box flex={1}>Total Monsieur: {totalMonsieur}</Box>
            {isCouple && <Box flex={1}>Total Madame: {totalMadame}</Box>}
            <Box flex={1}>Total Revenu Annuel: {totalRevenue}</Box>
            <Box flex={1}>
              Total Patrimoine Immobilier: {totalPatrimoineImmo}
            </Box>
          </Box>
        </Box>
      </>
    );
  }
);

export default PatrimoineTable;
