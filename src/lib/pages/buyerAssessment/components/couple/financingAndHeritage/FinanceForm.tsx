import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Heading,
} from '@chakra-ui/react';
import type { ChangeEvent } from 'react';
import { forwardRef, useImperativeHandle, useState } from 'react';

import type { ICreditTable, IFinanceForm } from '~/lib/types/buyer-assessment';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

import CreditTable from './CreditTable'; // Adjust the import based on your file structure

const FinanceForm = forwardRef((props: { data: IFinanceForm }, ref: any) => {
  const [formData, setFormData] = useState(props.data);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleGridChange = (id: number, field: string, value: string) => {
    const updatedCredits = formData.credits.map((credit) => {
      if (credit.id === id) {
        return { ...credit, [field]: value };
      }
      return credit;
    });
    const amountBorrowedTotal = calculateTotal(updatedCredits, 'amount');
    const monthlyPaymentsTotal = calculateTotal(
      updatedCredits,
      'monthlyPayment'
    );
    setFormData({
      ...formData,
      credits: updatedCredits,
      monthlyPaymentsTotal,
      amountBorrowedTotal,
    });
  };

  const addRow = () => {
    const newId = formData.credits.length + 1;
    const newCredit = {
      id: newId,
      type: '',
      startDate: '',
      amount: '',
      monthlyPayment: '',
      contractType: '',
    };
    setFormData({ ...formData, credits: [...formData.credits, newCredit] });
  };
  const calculateTotal = (
    credits: ICreditTable[],
    field: 'monthlyPayment' | 'amount'
  ) => {
    return credits
      .reduce((total, credit) => {
        const value = parseFloat(credit[field]) || 0;
        return total + value;
      }, 0)
      .toFixed(2); // Convert to a string with 2 decimal places
  };
  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    formData,
  }));
  return (
    <Box p={8} borderWidth={1} borderRadius="md" w="full">
      <Heading as="h3" size="lg" display="flex" justifyContent="center" mb={4}>
        Informations Financement
      </Heading>

      {/* Outstanding Credit */}
      <FormControl as="fieldset" mb={6}>
        <FormLabel as="legend">
          Avez-vous contracté un crédit qui n'est pas encore totalement
          remboursé ?
        </FormLabel>
        <RadioGroup
          name="outstandingCredit"
          value={formData.outstandingCredit}
          onChange={(value) =>
            setFormData({ ...formData, outstandingCredit: value })
          }
        >
          <Stack direction="row">
            <Radio value="true">Oui</Radio>
            <Radio value="false">Non</Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {/* Credit Details Grid */}
      {formData.outstandingCredit === 'true' && (
        <CreditTable
          data={formData}
          onCreditChange={handleGridChange}
          onAddRow={addRow}
        />
      )}

      {/* Savings */}
      <FormControl as="fieldset" mb={6}>
        <FormLabel as="legend">Disposez-vous d'une épargne ?</FormLabel>
        <RadioGroup
          name="savings"
          value={formData.savings}
          onChange={(value) => setFormData({ ...formData, savings: value })}
        >
          <Stack direction="row">
            <Radio value="true">Oui</Radio>
            <Radio value="false">Non</Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {formData.savings === 'true' && (
        <FormControl mb={6}>
          <FormLabel>Si oui, quel est le montant de votre épargne ?</FormLabel>
          <Input
            type="number"
            name="savingsAmount"
            onWheel={disableWheelOnInputNumber}
            value={formData.savingsAmount}
            onChange={handleInputChange}
          />
        </FormControl>
      )}

      {/* Contribution */}
      <FormControl as="fieldset" mb={6}>
        <FormLabel as="legend">
          Envisagez-vous d'effectuer un apport ?
        </FormLabel>
        <RadioGroup
          name="contribution"
          value={formData.contribution}
          onChange={(value) =>
            setFormData({ ...formData, contribution: value })
          }
        >
          <Stack direction="row">
            <Radio value="true">Oui</Radio>
            <Radio value="false">Non</Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {formData.contribution === 'true' && (
        <FormControl mb={6}>
          <FormLabel>
            Si oui, quel montant maximal pourriez-vous apporter ?
          </FormLabel>
          <Input
            type="number"
            name="contributionAmount"
            value={formData.contributionAmount}
            onWheel={disableWheelOnInputNumber}
            onChange={handleInputChange}
          />
        </FormControl>
      )}
    </Box>
  );
});

export default FinanceForm;
