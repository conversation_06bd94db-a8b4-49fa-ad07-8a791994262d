import {
  Box,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  VStack,
  Heading,
} from '@chakra-ui/react';
import type { ColDef } from 'ag-grid-community';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

import { BuyingOptions } from '~/lib/enums/BuyingOptions';
import type { IPatrimoineForm } from '~/lib/types/buyer-assessment';
import { getColDefForPatrimoineTable } from '~/lib/utils/patrimoineTable';

import PatrimoineTable from './PatrimoineTable';

const PatrimoineForm = forwardRef(
  (props: { data: IPatrimoineForm; buyingOption: string }, ref) => {
    const patrimoineTableRef = useRef<any>(null);
    const [ownsPatrimoine, setOwnsPatrimoine] = useState(
      props.data.ownsPatrimoine
    );
    const [gridData, setGridData] = useState(props.data.patrimoineTable);
    const columnDefs: ColDef[] = getColDefForPatrimoineTable(
      props.buyingOption
    );
    const handleRadioChange = (value: string) => {
      setOwnsPatrimoine(value);
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      patrimoineForm: {
        ownsPatrimoine,
        patrimoineTable: gridData,
        totalRevenue: patrimoineTableRef.current?.totalRevenue,
      },
    }));
    return (
      <Box p={8} borderWidth={1} borderRadius="md" w="full">
        <Heading
          as="h3"
          size="lg"
          display="flex"
          justifyContent="center"
          mb={4}
        >
          Situation Patrimoine
        </Heading>
        <VStack spacing={6} alignItems="flex-start">
          {/* Disposition de Patrimoine */}
          <FormControl>
            <FormLabel>Disposez-vous d'un patrimoine ?</FormLabel>
            <RadioGroup value={ownsPatrimoine} onChange={handleRadioChange}>
              <Stack direction="row">
                <Radio value="oui">Oui</Radio>
                <Radio value="non">Non</Radio>
              </Stack>
            </RadioGroup>
          </FormControl>

          {/* Patrimoine Table */}
          {ownsPatrimoine === 'oui' && (
            <PatrimoineTable
              gridData={gridData}
              columnDefs={columnDefs}
              isCouple={props.buyingOption === BuyingOptions.EN_COUPLE}
              ref={patrimoineTableRef}
              setGridData={setGridData}
            />
          )}
        </VStack>
      </Box>
    );
  }
);

export default PatrimoineForm;
