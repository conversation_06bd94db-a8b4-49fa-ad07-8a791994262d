import type {
  FormDataBuyerAssessmentSteps,
  ICalculatedBudgetData,
} from '~/lib/types/buyer-assessment';

export const calculateBudgetData = (
  data: FormDataBuyerAssessmentSteps
): ICalculatedBudgetData => {
  const { economicData, financeAndHeritageData, profileInvestorData } = data;
  const homeownerStatus =
    economicData.homeownerStatus === 'true'
      ? 'Propriétaire'
      : economicData.homeownerStatus;

  const annualNetIncome =
    parseFloat(economicData.financialDetails.fiscalIncome) -
    parseFloat(economicData.financialDetails.incomeTax);

  const roundNumber = (value, decimals = 0) => {
    const factor = 10 ** decimals;
    return Math.round(value * factor) / factor;
  };

  const monthlyNetIncome = annualNetIncome / 12;
  const monthlyPaymentsTotal = parseInt(
    financeAndHeritageData.financeForm.monthlyPaymentsTotal
  );

  const monthlytotalRevenue =
    parseInt(financeAndHeritageData.patrimoineForm.totalRevenue) / 12;

  const getFieldById = (id: string, field: 'total'): number => {
    const item = economicData.rowDataTable.find((row) => row.id === id);
    return item ? item[field] : 0;
  };

  const rowData = [
    { category: '', value: profileInvestorData.buyingOption },
    { category: 'LOGEMENTS', value: homeownerStatus },
    {
      category: 'REVENUS NETS ANNUELS APRES IMPOTS',
      value: roundNumber(annualNetIncome, 2),
    },
    {
      category: 'AUTRES REVENUS (revenus locatifs, placements…)',
      value: financeAndHeritageData.patrimoineForm.totalRevenue,
    },
    {
      category: 'AUTRES REVENUS MENSUELS (revenus locatifs, placements…)',
      value: monthlytotalRevenue,
    },
    {
      category: 'REVENUS NETS MENSUELS APRES IMPOTS',
      value: roundNumber(monthlyNetIncome, 2),
    },
    {
      category: "REMBOURSEMENTS D'EMPRUNTS",
      value: monthlyPaymentsTotal,
    },
    {
      category: 'DEPENSES MENSUELLES RECURRENTES',
      value: getFieldById('monthly_expenses', 'total'),
    },
    {
      category: 'DEPENSES PERSONNELLES DIVERSES',
      value: getFieldById('personal_expenses', 'total'),
    },
    {
      category: 'EPARGNE',
      value: financeAndHeritageData.financeForm.savingsAmount,
    },
    {
      category: 'APPORT ENVISAGE',
      value: financeAndHeritageData.financeForm.contributionAmount,
    },
  ];

  const debtConclusion =
    monthlyPaymentsTotal === 0
      ? { title: "Pas d'emprunt", color: 'green' }
      : { title: 'Emprunt en cours', color: 'red' };

  const depositConclusion =
    parseInt(financeAndHeritageData.financeForm.savingsAmount) > 10000
      ? { title: "Disposant d'une Epargne non-négligeable", color: 'green' }
      : { title: "Ne disposant pas d'une Epargne significative", color: 'red' };

  const adjustedRevenue = monthlyNetIncome + monthlytotalRevenue * 0.7;

  const debtToIncomeRatio = monthlyPaymentsTotal / adjustedRevenue;
  const creditStatus =
    debtToIncomeRatio >= 0.3
      ? { title: 'Grande difficulté pour obtenir un crédit', color: 'red' }
      : debtToIncomeRatio >= 0.15
        ? { title: 'Difficulté pour obtenir un crédit', color: 'orange' }
        : debtToIncomeRatio <= 0.1
          ? { title: "Possibilité d'obtenir un crédit", color: 'green' }
          : { title: 'Facilité pour obtenir un crédit', color: 'green' };

  const maxAcquisitionValue =
    (parseInt(financeAndHeritageData.financeForm.contributionAmount) * 100) /
    7.5;
  const taxRatio = 3.5;
  const interestRate = taxRatio / 100;
  const annuityPayment = Math.floor(
    (maxAcquisitionValue * interestRate) / (1 - (1 + interestRate) ** -25)
  );
  const getCreditByDebtRatioPerYears = (years: number) =>
    debtToIncomeRatio >= 0.3
      ? 0
      : Math.floor(annualNetIncome * years * (0.3 - debtToIncomeRatio));
  const monthlyLoanRepayment = Math.floor(annuityPayment / 12);

  const livingAllowance = Math.floor(
    monthlyNetIncome - monthlyPaymentsTotal - monthlyLoanRepayment
  );

  const remainingLivingAllowanceAfterExpenses = Math.floor(
    livingAllowance - getFieldById('personal_expenses', 'total')
  );

  const creditRowData = [
    {
      category:
        "Hypothèse crédit sur 25 ans d'un montant de 200 000€ au taux de 1,5%",
      value: annuityPayment,
    },
    { category: 'Remboursement crédit mensuel', value: monthlyLoanRepayment },
    {
      category: 'RESTE A VIVRE (au sens des établissements bancaires)',
      value: livingAllowance,
    },
    {
      category:
        'RESTE A VIVRE (en tenant compte des dépenses personnelles diverses)',
      value: remainingLivingAllowanceAfterExpenses,
    },
  ];

  const notaryFeeContributionStatus =
    parseInt(financeAndHeritageData.financeForm.contributionAmount) >=
    maxAcquisitionValue * 0.075
      ? {
          title: "Disposant d'un apport envisagé couvrant les frais de notaire",
          color: 'green',
        }
      : {
          title:
            "Ne disposant pas d'un apport suffisant pouvant couvrir les frais de notaire",
          color: 'red',
        };

  const livingAllowanceStatus =
    livingAllowance >= 1000
      ? {
          title: 'Reste à vivre jugé suffisant par les banques',
          color: 'green',
        }
      : { title: 'Reste à vivre jugé fragile par les banques', color: 'red' };

  const personalExpenseStatus =
    remainingLivingAllowanceAfterExpenses >= 1000
      ? {
          title:
            'Reste à vivre net des dépenses personnelles diverses acceptables',
          color: 'green',
        }
      : remainingLivingAllowanceAfterExpenses >= 600
        ? {
            title: 'Reste à vivre net des dépenses personnelles tendu',
            color: 'orange',
          }
        : remainingLivingAllowanceAfterExpenses <= 400
          ? {
              title: 'Reste à vivre net des dépenses personnelles critique',
              color: 'red',
            }
          : { title: 'Données insuffisantes', color: 'red' };

  return {
    rowData,
    creditRowData,
    debtToIncomeRatio: roundNumber(debtToIncomeRatio, 2),
    creditStatus,
    maxAcquisitionValue: roundNumber(maxAcquisitionValue, 2),
    taxRatio,
    interestRate,
    annuityPayment,
    notaryFeeContributionStatus,
    getCreditByDebtRatioPerYears,
    livingAllowanceStatus,
    personalExpenseStatus,
    monthlyLoanRepayment,
    livingAllowance,
    debtConclusion,
    depositConclusion,
    remainingLivingAllowanceAfterExpenses,
  };
};
