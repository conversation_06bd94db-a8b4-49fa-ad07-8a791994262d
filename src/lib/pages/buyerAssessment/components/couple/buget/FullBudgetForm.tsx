import {
  Box,
  VStack,
  Heading,
  Text,
  Divider,
  useColorModeValue,
} from '@chakra-ui/react';
import type { ColDef } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import type { ICalculatedBudgetData } from '~/lib/types/buyer-assessment';

const FullBudgetForm = (props: { data: ICalculatedBudgetData }) => {
  const gridBgColor = useColorModeValue('white', 'gray.700');
  const cardBgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    rowData,
    creditRowData,
    debtToIncomeRatio,
    creditStatus,
    maxAcquisitionValue,
    taxRatio,
    debtConclusion,
    depositConclusion,
    notaryFeeContributionStatus,
    livingAllowanceStatus,
    personalExpenseStatus,
    getCreditByDebtRatioPerYears,
  } = props.data;
  const columnDefs: ColDef[] = [
    { headerName: '', field: 'category', flex: 1 },
    { headerName: '', field: 'value', flex: 1 },
  ];

  const creditColumnDefs: ColDef[] = [
    { headerName: 'Category', field: 'category', flex: 1 },
    { headerName: 'Value', field: 'value', flex: 1 },
  ];

  return (
    <VStack
      spacing={8}
      w="full"
      borderColor={borderColor}
      borderWidth="1px"
      maxW="1200px"
      mx="auto"
      px={4}
      py={8}
      bg={gridBgColor}
      borderRadius="lg"
    >
      <Heading as="h3" size="lg" textAlign="center" mb={4}>
        Budget & Endettement
      </Heading>

      {/* Ag-Grid for Budget Data */}
      <Box
        className="ag-theme-alpine"
        style={{ width: '100%', height: 'auto' }}
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <AgGridReact
          columnDefs={columnDefs}
          rowData={rowData}
          headerHeight={0}
          domLayout="autoHeight"
        />
      </Box>

      {/* Conclusion Budget Section */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Conclusion BUDGET
        </Heading>
        <VStack align="start">
          <Text color={debtConclusion.color}> {debtConclusion.title}</Text>
          <Text color={depositConclusion.color}>{depositConclusion.title}</Text>
        </VStack>
      </Box>

      {/* Endettement Section */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Endettement
        </Heading>
        <VStack align="start">
          <Text>Taux d'endettement actuel: {debtToIncomeRatio}</Text>
        </VStack>
      </Box>

      {/* Conclusion Endettement Section */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Conclusion ENDETTEMENT
        </Heading>
        <VStack align="start">
          <Text color={creditStatus.color}>{creditStatus.title}</Text>
        </VStack>
      </Box>

      {/* Montant du Crédit Section (Méthode par le taux d'endettement) */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Montant du Crédit - Méthode par le taux d'endettement
        </Heading>
        <VStack align="start">
          <Text>
            CAPACITE D'ENDETTEMENT théorique sur 20 ans (3):{' '}
            {getCreditByDebtRatioPerYears(20)}
          </Text>
        </VStack>
        <VStack align="start">
          <Text>
            CAPACITE D'ENDETTEMENT théorique sur 25 ans:{' '}
            {getCreditByDebtRatioPerYears(25)}
          </Text>
        </VStack>
        <Text fontSize="sm" mt={4} lineHeight="1.6" color="gray.700">
          <strong>Objectif de cette méthode :</strong>
          <br />
          Évaluer le budget maximal que vous pourriez emprunter, en tenant
          compte d'un taux d'endettement maximal de <strong>30%</strong>.
          <br />
          Cette méthode indique le montant de financement qu'une banque pourrait
          vous accorder en fonction de votre taux d'endettement.
        </Text>
      </Box>

      {/* Montant du Crédit Section (Méthode par l'apport envisagé) */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Montant du Crédit - Méthode par l'apport envisagé
        </Heading>
        <VStack align="start">
          <Text>
            Valeur d'Acquisition Maximum du Bien: {maxAcquisitionValue}
          </Text>
        </VStack>
        <VStack align="start">
          <Text>
            Hypothèse crédit sur 25 ans d'un montant de 240 000€ au taux de 1,5%
            (5): {taxRatio}%
          </Text>
        </VStack>
        <Text fontSize="sm" mt={4} lineHeight="1.6" color="gray.700">
          <strong>Objectif de cette méthode :</strong>
          <br />
          Ainsi, cette méthode permet d'estimer le capital que vous pourriez
          emprunter en fonction de l'apport que vous envisagiez débourser.
        </Text>
      </Box>

      {/* Montant du Crédit Section (Méthode du Reste à Vivre) */}
      <Box
        className="ag-theme-alpine"
        style={{ width: '100%', height: 'auto' }}
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Montant du Crédit - Méthode du Reste à Vivre
        </Heading>
        <AgGridReact
          columnDefs={creditColumnDefs}
          rowData={creditRowData}
          domLayout="autoHeight"
          headerHeight={0}
        />
        <Text fontSize="sm" mt={4} lineHeight="1.6" color="gray.700">
          <strong>Objectif de cette méthode :</strong>
          <br />
          Les banques analysent également le "reste à vivre" des emprunteurs
          afin de décider s'ils octroient ou non un crédit
        </Text>
      </Box>

      {/* Conclusion Section */}
      <Box
        w="full"
        bg={cardBgColor}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading as="h4" size="md" mb={2}>
          Conclusion
        </Heading>
        <VStack align="start">
          <Text color={notaryFeeContributionStatus.color}>
            {notaryFeeContributionStatus.title}
          </Text>
        </VStack>
        <VStack align="start">
          <Text color={creditStatus.color}>{creditStatus.title}</Text>
        </VStack>
        <VStack align="start">
          <Text color={livingAllowanceStatus.color}>
            {livingAllowanceStatus.title}
          </Text>
        </VStack>
        <VStack align="start">
          <Text color={personalExpenseStatus.color}>
            {personalExpenseStatus.title}
          </Text>
        </VStack>
      </Box>

      <Divider />
    </VStack>
  );
};

export default FullBudgetForm;
