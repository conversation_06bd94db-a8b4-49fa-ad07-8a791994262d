'use client';

import {
  Box,
  Container,
  Heading,
  SimpleGrid,
  Icon,
  Center,
  Text,
  Stack,
  HStack,
  VStack,
  ListItem,
  ListIcon,
  List,
} from '@chakra-ui/react';
import { FaCircle, FaRegCircle } from 'react-icons/fa';

import BodyDescriptionData from '../utils/BodyDescriptionData.json';
// Replace test data with your own
// eslint-disable-next-line prefer-spread

export default function GridListWithHeading() {
  return (
    <div>
      {BodyDescriptionData.map((mainInfo) => (
        <Center key={mainInfo.id} py={6}>
          <Box
            maxW="6xl"
            w="full"
            boxShadow="xs"
            p="6"
            rounded="md"
            bg="white"
            display={{ base: 'initial', lg: 'flex' }}
            overflow="hidden"
          >
            <Stack
              spacing={1}
              as={Container}
              maxW="3xl"
              textAlign={{ base: 'center', lg: 'start' }}
              mb={10}
            >
              <Heading fontSize="3xl">{mainInfo.mainTitle}</Heading>
              <Text color="gray.600" fontSize="xl">
                {mainInfo.subTitle}
              </Text>
            </Stack>

            <Container maxW="6xl">
              <SimpleGrid columns={{ base: 1, md: 1, lg: 1 }} spacing={4}>
                {mainInfo.subCategories.map((feature) => (
                  <HStack key={feature.id} align="top">
                    <Box py={0.5}>
                      <Icon w="12px" as={FaCircle} />
                    </Box>
                    <VStack align="start">
                      <Text fontWeight={600}>{feature.title}</Text>
                      <List spacing={2}>
                        {feature.items.map((listElement) => (
                          <ListItem display="flex" key={listElement.id}>
                            <ListIcon w="12px" mt={1} as={FaRegCircle} />
                            <Text fontStyle="italic">{listElement.title}</Text>
                          </ListItem>
                        ))}
                      </List>

                      {/* <Text color="gray.600">{feature.text}</Text> */}
                    </VStack>
                  </HStack>
                ))}
              </SimpleGrid>
            </Container>
          </Box>
        </Center>
      ))}
    </div>
  );
}
