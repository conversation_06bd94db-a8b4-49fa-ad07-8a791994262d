import { useEffect, useState, useMemo } from 'react';
import { Box } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  RENTABILITY_THRESHOLD_HIGH,
  RENTABILITY_THRESHOLD_MEDIUM,
} from './constants';
import { FinanceTable } from './FinanceTable';
import {
  ProjectAnalysisProps,
  FinancialSection,
} from '~/lib/types/project-analysis';

// Wrap Box with motion for animations
const MotionBox = motion(Box);

const ProjectAnalysis: React.FC<ProjectAnalysisProps> = ({
  onInputChange,
  monthlyPayment,
}) => {
  const [data, setData] = useState<{
    [key: string]: number;
    prixAcquisition: number;
    fraisNotaire: number;
    meubles: number;
    estimationRevente: number;
    apport: number;
    montantEmprunt: number;
    insurancePerYear: number;
    ratioDeboursement: number;
    amortissementAnnuel: number;
    loyersEstimes: number;
    chargesCopro: number;
    taxeFonciere: number;
    chargesProp: number;
    loanPrincipal: number;
    estimatedRentIncludingCharges: number;
    estimatedRentExcludingCharges: number;
    totalPropertyCharges: number;
    propertyChargesBorneByTenant: number;
    propertyChargesBorneByOwner: number;
    propertyTaxesExcludingTOM: number;
    netRentAfterDeductions: number;
  }>({
    prixAcquisition: 0,
    fraisNotaire: 0,
    meubles: 0,
    estimationRevente: 0,
    apport: 0,
    montantEmprunt: 0,
    insurancePerYear: 0,
    ratioDeboursement: 0,
    amortissementAnnuel: 0,
    loyersEstimes: 0,
    chargesCopro: 0,
    taxeFonciere: 0,
    chargesProp: 0,
    loanPrincipal: 0,
    estimatedRentIncludingCharges: 0,
    estimatedRentExcludingCharges: 0,
    totalPropertyCharges: 0,
    propertyChargesBorneByTenant: 0,
    propertyChargesBorneByOwner: 0,
    propertyTaxesExcludingTOM: 0,
    netRentAfterDeductions: 0,
  });

  const updateField = (field: string, value: string) => {
    const newValue = parseFloat(value) || 0;
    setData((prev) => {
      const updatedData = { ...prev, [field]: newValue };

      if (field === 'prixAcquisition') {
        updatedData.fraisNotaire = newValue * 0.08;
        onInputChange({ target: { name: field, value: newValue } });
      }
      if (field === 'insurancePerYear') {
        onInputChange({ target: { name: field, value: newValue } });
      }

      return updatedData;
    });
  };

  useEffect(() => {
    if (data.loanPrincipal) {
      onInputChange({
        target: { name: 'loanPrincipal', value: data.loanPrincipal },
      });
    }
  }, [data.loanPrincipal, onInputChange]);

  const calculs = useMemo(
    () => ({
      coutTotal: data.prixAcquisition + data.fraisNotaire + data.meubles,
      prixNetFraisNotaire: data.prixAcquisition + data.fraisNotaire,
      netRentAfterDeductions:
        data.estimatedRentExcludingCharges -
        data.propertyChargesBorneByOwner -
        data.propertyTaxesExcludingTOM,
      estimationRevente: () => {
        data.estimationRevente = parseFloat(
          (data.prixAcquisition * Math.pow(1.02, 25)).toFixed(2)
        );
        return data.estimationRevente;
      },
      loyersNets:
        data.loyersEstimes -
        data.chargesCopro -
        data.chargesProp -
        data.taxeFonciere,
      cash: () => calculs.netRentAfterDeductions - data.amortissementAnnuel,
      totalToFinance: () => {
        data.loanPrincipal = calculs.coutTotal - data.apport;
        return data.loanPrincipal.toFixed(2);
      },
      rentabiliteBrute: () =>
        calculs.coutTotal !== 0
          ? (data.estimatedRentExcludingCharges / calculs.coutTotal) * 100
          : 0,
      rentabiliteNette: () =>
        calculs.coutTotal !== 0
          ? (calculs.netRentAfterDeductions / calculs.coutTotal) * 100
          : 0,
      ratioDeboursement: () => {
        data.ratioDeboursement =
          data.estimationRevente !== 0
            ? ((data.meubles + data.fraisNotaire) / data.estimationRevente) *
              100
            : 0;
        return data.ratioDeboursement;
      },
      amortissementMensuelle: () => (data.amortissementAnnuel / 12).toFixed(2),
      propertyChargesBorneByTenant: () => {
        data.propertyChargesBorneByTenant =
          data.totalPropertyCharges * 0.666666666666667;
        return Math.round(data.propertyChargesBorneByTenant);
      },
      propertyChargesBorneByOwner: () => {
        data.propertyChargesBorneByOwner = data.totalPropertyCharges * 0.333333;
        return Math.round(data.propertyChargesBorneByOwner);
      },
      estimatedRentIncludingCharges: () => {
        data.estimatedRentIncludingCharges =
          data.propertyChargesBorneByTenant +
          data.estimatedRentExcludingCharges;
        return Math.round(data.estimatedRentIncludingCharges);
      },
      amortissementAnnuel: () => {
        const amortissementAnnuel = monthlyPayment * 12 + data.insurancePerYear;
        data.amortissementAnnuel = amortissementAnnuel;
        return amortissementAnnuel.toFixed(2);
      },
      montantDeboursement: () =>
        Math.round(100 - parseFloat(data.ratioDeboursement.toString())),
      amountPaid: () =>
        (
          data.estimationRevente *
          (calculs.montantDeboursement() / 100)
        ).toFixed(2),
      percentagePaidOut: () =>
        (
          (parseFloat(data.ratioDeboursement.toString()) *
            data.estimationRevente) /
          100
        ).toFixed(2),
    }),
    [data, monthlyPayment]
  );

  const getRentabilityResult = useMemo(
    () => () => {
      const rentabilite = calculs.rentabiliteBrute();

      if (rentabilite >= RENTABILITY_THRESHOLD_HIGH) {
        return { text: 'Rentable', colorScheme: 'black' };
      } else if (rentabilite > RENTABILITY_THRESHOLD_MEDIUM) {
        return { text: 'Rentabilité acceptable', colorScheme: 'green' };
      } else {
        return { text: 'Rentabilité insuffisante', colorScheme: 'red' };
      }
    },
    [calculs.rentabiliteBrute]
  );

  const getRentabilityNet = useMemo(
    () => () => {
      const rentabiliteNette = calculs.rentabiliteNette();
      return rentabiliteNette < RENTABILITY_THRESHOLD_MEDIUM
        ? { text: 'Rentabilité insuffisante', colorScheme: 'red' }
        : { text: 'Rentabilité acceptable', colorScheme: 'green' };
    },
    [calculs.rentabiliteNette]
  );

  const getCashResult = useMemo(
    () => () => {
      return calculs.cash() < 0
        ? { text: 'Trésorerie générée négative', colorScheme: 'red' }
        : { text: 'Trésorerie générée positive', colorScheme: 'green' };
    },
    [calculs.cash]
  );

  const financialData: FinancialSection[] = useMemo(
    () => [
      {
        title: "Coût du projet d'acquisition",
        data: [
          {
            label: "Prix d'acquisition",
            value: data.prixAcquisition,
            isEditable: true,
            onChange: (val) => updateField('prixAcquisition', val),
          },
          { label: 'Notaire (8%)', value: data.fraisNotaire.toFixed(2) },
          {
            label: 'Meubles et/ou Travaux',
            value: data.meubles,
            isEditable: true,
            onChange: (val) => updateField('meubles', val),
          },
          {
            label: "Prix d'acquisition du bien net de frais de notaire",
            value: calculs.prixNetFraisNotaire.toFixed(2),
          },
          {
            label: 'Prix estimé de revente dans 25 ans',
            value: calculs.estimationRevente(),
          },
          { label: 'Coût total', value: calculs.coutTotal.toFixed(2) },
        ],
      },
      {
        title: 'Financement',
        data: [
          {
            label: 'Apport',
            value: data.apport,
            isEditable: true,
            onChange: (val) => updateField('apport', val),
          },
          {
            label: 'Assurance emprunteur annuel',
            value: data.insurancePerYear,
            isEditable: true,
            onChange: (val) => updateField('insurancePerYear', val),
          },
          {
            label: 'Montant à financer par Emprunt',
            value: calculs.totalToFinance(),
          },
          {
            label: 'Amortissement annuel emprunt',
            value: calculs.amortissementAnnuel(),
          },
          {
            label: 'Échéance mensuelle emprunt (dont intérêt et assurance)',
            value: calculs.amortissementMensuelle(),
          },
        ],
      },
      {
        title: 'Loyers à percevoir avec prise en compte des charges',
        data: [
          {
            label: 'Loyers estimés hors charges',
            value: data.estimatedRentExcludingCharges,
            isEditable: true,
            onChange: (val) =>
              updateField('estimatedRentExcludingCharges', val),
          },
          {
            label: 'Total charges de copropriété',
            value: data.totalPropertyCharges,
            isEditable: true,
            onChange: (val) => updateField('totalPropertyCharges', val),
          },
          {
            label: 'Charges de copropriété à la charge des locataires',
            value: calculs.propertyChargesBorneByTenant(),
          },
          {
            label: 'Loyers estimés charges comprises',
            value: calculs.estimatedRentIncludingCharges(),
          },
          {
            label: 'Charges de copropriété à la charge du propriétaire',
            value: calculs.propertyChargesBorneByOwner(),
          },
          {
            label: `Taxes Foncières (hors taxe d'enlèvement des ordures ménagères refacturable)`,
            value: data.propertyTaxesExcludingTOM,
            isEditable: true,
            onChange: (val) => updateField('propertyTaxesExcludingTOM', val),
          },
          {
            label:
              'Loyers nets après prise en compte des charges de copropriété et de la taxe foncière hors TOM',
            value: Math.round(calculs.netRentAfterDeductions),
          },
        ],
      },
      {
        title: 'Analyse de la Trésorerie Générée',
        showComments: true,
        data: [
          {
            label: 'Loyers nets après charges',
            value: Math.round(calculs.netRentAfterDeductions),
          },
          {
            label: 'Amortissement annuel emprunt',
            value: Math.round(data.amortissementAnnuel),
          },
          {
            label: 'Trésorerie dégagée',
            value: Math.round(calculs.cash()),
            comment: getCashResult().text,
            colorScheme: getCashResult().colorScheme,
          },
        ],
      },
      {
        title: 'Analyse des Rentabilités du Projet',
        showComments: true,
        data: [
          {
            label: 'Rentabilité brute (%)',
            value: `${calculs.rentabiliteBrute().toFixed(2)}%`,
            comment: getRentabilityResult().text,
            colorScheme: getRentabilityResult().colorScheme,
          },
          {
            label: 'Rentabilité nette (%)',
            value: `${calculs.rentabiliteNette().toFixed(2)}%`,
            comment: getRentabilityNet().text,
            colorScheme: getRentabilityNet().colorScheme,
          },
        ],
      },
      {
        title: 'Analyse du Ratio de Déboursement / Prix de Revente du Bien',
        showComments: true,
        data: [
          {
            label:
              'Ce ratio calcule le pourcentage des sommes déboursées par rapport au montant récupérable dans 25 ans',
            value: '',
          },
          { label: '', value: '' },
          {
            label: 'Commentaire :',
            value: 'Pourcentage',
            comment: 'Montant',
            colorScheme: 'black',
          },
          {
            label: 'Pourcentage déboursé',
            value: `${calculs.ratioDeboursement()}%`,
            comment: calculs.percentagePaidOut(),
            colorScheme: 'black',
          },
          {
            label: 'Montant déboursé après 25 ans',
            value: `${calculs.montantDeboursement()}%`,
            comment: calculs.amountPaid(),
            colorScheme: 'black',
          },
        ],
      },
    ],
    [data, calculs]
  );

  const conclusionData: FinancialSection = useMemo(
    () => ({
      title: 'Conclusion',
      showComments: false,
      data: [
        {
          label:
            'Le projet d’investissement immobilier locatif présente une rentabilité brute de',
          value: `${calculs.rentabiliteBrute().toFixed(2)}%`,
        },
        {
          label: 'et une rentabilité nette de',
          value: `${calculs.rentabiliteNette().toFixed(2)}%`,
        },
        {
          label: 'Ainsi, il s’agit d’un projet présentant une',
          value: getRentabilityResult().text,
        },
        {
          label: 'et une',
          value: getCashResult().text,
        },
      ],
    }),
    [calculs]
  );

  return (
    <Box
      p={{ base: 2, md: 6 }}
      borderRadius={8}
      role="main"
      aria-label="Analyse financière du projet"
    >
      {financialData.map((section, index) => (
        <MotionBox
          key={index}
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: index * 0.1 }}
          aria-label={`Section financière : ${section.title}`}
          role="region"
        >
          <FinanceTable
            title={section.title}
            data={section.data}
            showComments={section.showComments}
          />
        </MotionBox>
      ))}
      <MotionBox
        mb={6}
        bg="white"
        borderRadius={8}
        boxShadow="sm"
        overflow="hidden"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{
          duration: 0.5,
          ease: 'easeOut',
          delay: financialData.length * 0.1,
        }}
        aria-label="Section de conclusion"
        role="region"
      >
        <FinanceTable
          title={conclusionData.title}
          data={conclusionData.data}
          showComments={conclusionData.showComments}
        />
      </MotionBox>
    </Box>
  );
};

export default ProjectAnalysis;
