import { useState, useEffect, ChangeEvent } from 'react';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Grid,
  GridItem,
  Input,
  Heading,
  Center,
  useColorModeValue,
  useBreakpointValue,
} from '@chakra-ui/react';

interface LoanScheduleEntry {
  N_PAIEMENT: number;
  DATE_ECHEANCE: string;
  SOLDE_DE_DEPART: string;
  PROGRAMME: string;
  SUPPLEMENTAIRE: string;
  PAIEMENT_TOTAL: string;
  PRINCIPAL: string;
  INTERETS: string;
  SOLDE_FINAL: string;
  INTERETS_CUMULES: string;
  ASSURANCE: string;
  TOTAL_MENSUAL: string;
  TOTAL_CHARGE: string;
}

interface LoanDetailsProps {
  loanPrincipal: number;
  rate: number;
  termYears: number;
  totalPayments: number;
  isActive: boolean;
  monthlyPayment: number;
  paymentsPerYear: number;
  insurancePerYear: number;
}

const LoanDetails: React.FC<LoanDetailsProps> = ({
  loanPrincipal,
  rate,
  termYears,
  totalPayments,
  isActive,
  monthlyPayment,
  paymentsPerYear,
  insurancePerYear,
}) => {
  const [rowData, setRowData] = useState<LoanScheduleEntry[]>([]);
  const [startDate, setStartDate] = useState(new Date());
  const [totalInterest, setTotalInterest] = useState(0);
  const [ratePercentage, setRatePercentage] = useState(rate);

  const columnDefs = [
    { field: 'N_PAIEMENT', headerName: 'N° PAIEMENT', width: 100 },
    { field: 'DATE_ECHEANCE', headerName: "DATE D'ECHEANCE", width: 150 },
    { field: 'SOLDE_DE_DEPART', headerName: 'SOLDE DE DÉPART', width: 150 },
    { field: 'PROGRAMME', headerName: 'PAIEMENT PROGRAMME', width: 150 },
    {
      field: 'SUPPLEMENTAIRE',
      headerName: 'PAIEMENT SUPPLÉMENTAIRE',
      width: 150,
    },
    { field: 'PAIEMENT_TOTAL', headerName: 'PAIEMENT TOTAL', width: 150 },
    { field: 'PRINCIPAL', headerName: 'PRINCIPAL', width: 100 },
    { field: 'INTERETS', headerName: 'INTÉRÊTS', width: 100 },
    { field: 'SOLDE_FINAL', headerName: 'SOLDE FINAL', width: 150 },
    { field: 'INTERETS_CUMULES', headerName: 'INTÉRÊTS CUMULÉS', width: 150 },
    { field: 'ASSURANCE', headerName: 'ASSURANCE', width: 100 },
    { field: 'TOTAL_MENSUAL', headerName: 'TOTAL MENSUEL', width: 150 },
    { field: 'TOTAL_CHARGE', headerName: 'TOTAL CHARGE', width: 150 },
  ];

  useEffect(() => {
    if (isActive) {
      setRowData(generateLoanSchedule(startDate));
    } else {
      setRatePercentage(rate);
    }
  }, [isActive, startDate, ratePercentage]);

  const generateLoanSchedule = (currentDate: Date): LoanScheduleEntry[] => {
    let date = new Date(currentDate);
    let loanBalance = loanPrincipal;
    let schedule: LoanScheduleEntry[] = [];
    let calculateTotalInterest = 0;
    const monthlyRate = ratePercentage / 100 / paymentsPerYear;

    for (let i = 1; i <= totalPayments; i++) {
      let interest = loanBalance * monthlyRate;
      let principalPayment = Math.min(monthlyPayment - interest, loanBalance);
      let newBalance = loanBalance - principalPayment;
      calculateTotalInterest += interest;

      schedule.push({
        N_PAIEMENT: i,
        DATE_ECHEANCE: date.toLocaleDateString('fr-FR'),
        SOLDE_DE_DEPART: `${loanBalance.toFixed(2)} €`,
        PROGRAMME: `${monthlyPayment.toFixed(2)} €`,
        SUPPLEMENTAIRE: '0.00 €',
        PAIEMENT_TOTAL: `${monthlyPayment.toFixed(2)} €`,
        PRINCIPAL: `${principalPayment.toFixed(2)} €`,
        INTERETS: `${interest.toFixed(2)} €`,
        SOLDE_FINAL: newBalance > 0 ? `${newBalance.toFixed(2)} €` : '0.00 €',
        INTERETS_CUMULES: `${calculateTotalInterest.toFixed(2)} €`,
        ASSURANCE: `${(insurancePerYear / 12).toFixed(2)} €`,
        TOTAL_MENSUAL: `${(monthlyPayment + insurancePerYear / 12).toFixed(2)} €`,
        TOTAL_CHARGE: `${(interest + insurancePerYear / 12).toFixed(2)} €`,
      });
      setTotalInterest(calculateTotalInterest);
      loanBalance = newBalance;
      date.setMonth(date.getMonth() + 1);
      if (loanBalance <= 0) break;
    }
    return schedule;
  };
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setRatePercentage(parseFloat(value));
  };
  // Dynamic color based on the theme (light/dark mode)
  const bgColor = useColorModeValue('white', 'gray.800');

  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const LoanTable = ({ title, bgColor, data }) => (
    <TableContainer
      borderWidth="1px"
      borderColor={borderColor}
      borderRadius="lg"
      p="4"
      boxShadow="md"
      overflowX="auto"
    >
      <Table variant="simple">
        <Thead display={['block', 'table-header-group']}>
          <Tr display={['flex', 'table-row']} flexDirection={['column', 'row']}>
            <Th
              colSpan="2"
              textAlign="center"
              bg={bgColor}
              color="black"
              p={['2', '4']}
            >
              {title}
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map(([label, value], index) => (
            <Tr key={index} display={['block', 'table-row']}>
              <Td
                display="flex"
                flexDirection={['column', 'row']}
                fontWeight="bold"
                whiteSpace={['normal', 'nowrap']}
              >
                {label}
              </Td>
              <Td
                display="flex"
                flexDirection={['column', 'row']}
                whiteSpace={['normal', 'nowrap']}
              >
                {value}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
  const headingSize = useBreakpointValue({ base: 'lg', md: 'xl', lg: '2xl' });

  return (
    <Box p={{ base: 3, md: 5 }} bg={bgColor}>
      <Center mb={{ base: 4, md: 6 }}>
        <Heading
          as="h1"
          fontSize={headingSize}
          color="teal.500"
          textAlign="center"
        >
          PLAN D'AMORTISSEMENT
        </Heading>
      </Center>
      <Grid
        templateColumns={{
          base: '1fr', // 1 column for small screens
          md: 'repeat(2, 1fr)', // 2 columns for medium screens and up
        }}
        gap={6}
        mb={6}
      >
        {' '}
        <GridItem>
          <LoanTable
            title="ENTRER DES VALEURS"
            bgColor="orange.200"
            data={[
              ['Montant du prêt', `${loanPrincipal} €`],
              [
                "Taux d'intérêt annuel %",
                <Input
                  placeholder="Taux %"
                  name="rate"
                  type="number"
                  value={ratePercentage}
                  onWheel={disableWheelOnInputNumber}
                  onChange={handleInputChange}
                />,
              ],
              ['Période du prêt en année', termYears],
              ['Nombre de remboursements par an', paymentsPerYear],
              [
                'Date de début du prêt',
                <DatePicker selected={startDate} onChange={setStartDate} />,
              ],
            ]}
          />
        </GridItem>
        <GridItem>
          <LoanTable
            title="RELEVE DES PRETS"
            bgColor="red.200"
            data={[
              ['Paiement programmé', `${monthlyPayment.toFixed(2)} €`],
              ['Nombre de paiements prévu', 300],
              ['Nombre réel de paiements', 10],
              ['Total des intérêts', `${totalInterest.toFixed(2)} €`],
              ['Nom du preteur', <Input placeholder="Nom" size="sm" />],
            ]}
          />
        </GridItem>
      </Grid>

      <div
        style={{
          width: '100%',
          overflowX: 'auto',
          margin: '0 auto',
          border: '1px solid #d3d3d3',
          borderRadius: '4px',
          maxWidth: window.innerWidth < 768 ? '77vw' : '100%',
        }}
      >
        <div
          className="ag-theme-alpine"
          style={{
            height: '300px',
            minWidth: '600px',
            width: '100%',
            boxShadow:
              '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            rowData={rowData}
            pagination={true}
            defaultColDef={{ resizable: true }}
          />
        </div>
      </div>
    </Box>
  );
};

export default LoanDetails;
