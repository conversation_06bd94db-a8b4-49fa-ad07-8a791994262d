import { FC } from 'react';
import {
  Box,
  Text,
  Input,
  Table,
  Tbody,
  Tr,
  Td,
  useBreakpointValue,
  VStack,
  Tooltip,
  Badge,
  <PERSON>ing,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FinanceTableProps } from '~/lib/types/project-analysis';

// Wrap Box with motion for animations
const MotionBox = motion(Box);

export const FinanceTable: FC<FinanceTableProps> = ({
  title,
  data,
  showComments,
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <MotionBox
      mb={8}
      overflowX="auto"
      borderWidth={1}
      borderRadius={8}
      boxShadow="md"
      bg="white"
      p={4}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
      aria-label={`Tableau financier : ${title}`}
      role="region"
    >
      <Heading
        size={{ base: 'md', md: 'lg' }}
        fontWeight="bold"
        p={4}
        borderBottomWidth={1}
        borderColor="gray.100"
        textAlign="center"
        color="gray.700"
        fontSize={{ base: 'lg', md: 'xl' }}
      >
        {title}
      </Heading>
      {isMobile ? (
        <VStack spacing={4} align="stretch">
          {data.map((item, index) => (
            <Box
              key={index}
              p={4}
              borderWidth={1}
              borderRadius={8}
              boxShadow="sm"
              bg="gray.50"
              aria-label={`${item.label} entrée`}
              role="article"
            >
              <Text mb={2} fontWeight="medium" color="gray.700">
                {item.label}
              </Text>
              {item.isEditable ? (
                <Input
                  value={item.value.toString()}
                  onChange={(e) => item.onChange?.(e.target.value)}
                  size="sm"
                  bg="white"
                  borderColor="gray.300"
                  _focus={{ borderColor: 'blue.500', boxShadow: 'outline' }}
                  aria-label={`${item.label} entrée`}
                  isInvalid={isNaN(parseFloat(item.value.toString()))}
                  errorBorderColor="red.500"
                />
              ) : (
                <Tooltip
                  label={item.label}
                  placement="top"
                  aria-label={`${item.label} infobulle`}
                >
                  <Text fontWeight="semibold" color="gray.800">
                    {item.value}
                  </Text>
                </Tooltip>
              )}
              {showComments && item.comment && (
                <Box mt={2}>
                  <Text fontSize="sm" fontWeight="medium" color="gray.600">
                    Commentaires:
                  </Text>
                  <Badge
                    colorScheme={item.colorScheme || 'gray'}
                    variant="subtle"
                    fontSize="sm"
                    aria-label={`${item.label} commentaire`}
                  >
                    {item.comment}
                  </Badge>
                </Box>
              )}
              {item.isEditable && isNaN(parseFloat(item.value.toString())) && (
                <Alert
                  status="error"
                  mt={2}
                  fontSize="xs"
                  borderRadius={4}
                  role="alert"
                >
                  <AlertIcon />
                  Veuillez entrer un nombre valide.
                </Alert>
              )}
            </Box>
          ))}
        </VStack>
      ) : (
        <Table
          variant="simple"
          width="100%"
          aria-label={`Tableau financier détaillé : ${title}`}
        >
          <Tbody>
            {data.map((item, index) => (
              <Tr
                key={index}
                _hover={{ bg: 'gray.50', transition: 'background 0.2s' }}
                aria-label={`${item.label} ligne`}
                role="row"
              >
                <Td fontSize="sm" color="gray.700" py={2}>
                  {item.label}
                </Td>
                <Td py={2}>
                  {item.isEditable ? (
                    <Input
                      value={item.value.toString()}
                      onChange={(e) => item.onChange?.(e.target.value)}
                      size="sm"
                      bg="white"
                      borderColor="gray.300"
                      _focus={{ borderColor: 'blue.500', boxShadow: 'outline' }}
                      w="150px"
                      aria-label={`${item.label} entrée`}
                      isInvalid={isNaN(parseFloat(item.value.toString()))}
                      errorBorderColor="red.500"
                    />
                  ) : (
                    <Tooltip
                      label={item.label}
                      placement="top"
                      aria-label={`${item.label} infobulle`}
                    >
                      <Text fontWeight="semibold" color="gray.800">
                        {item.value}
                      </Text>
                    </Tooltip>
                  )}
                </Td>
                {showComments && (
                  <Td py={2}>
                    {item.comment && (
                      <Badge
                        colorScheme={item.colorScheme || 'gray'}
                        variant="subtle"
                        fontSize="sm"
                        aria-label={`${item.label} commentaire`}
                      >
                        {item.comment}
                      </Badge>
                    )}
                  </Td>
                )}
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}
    </MotionBox>
  );
};
