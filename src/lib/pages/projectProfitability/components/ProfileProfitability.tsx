import { Box, Progress, useSteps } from '@chakra-ui/react';
import { useRef, useState } from 'react';
import { StepperComponent } from '~/lib/components/commonComponents/multistep/StepperComponent';
import { StepperNavigationButtons } from '~/lib/components/commonComponents/multistep/StepperNavigationButtons';
import ProjectAnalysis from './ProjectAnalysis';
import LoanSheet from './LoanSheet';

export default function Multistep() {
  const steps = [{ title: 'Rentabilité du projet' }, { title: 'Prêt TTC' }];

  const formRefs = steps.map(() => useRef<any>(null));

  const { activeStep, setActiveStep } = useSteps({
    index: 0,
    count: steps.length,
  });
  const isLastStep = activeStep === steps.length - 1;
  const [formData] = useState({});

  const onNextStep = async () => {
    if (activeStep + 1 < steps.length) setActiveStep(activeStep + 1);
  };
  const validateBeforeSetActiveStep = async (nextStep: number) => {
    setActiveStep(nextStep);
  };
  const [loanParams, setLoanParams] = useState({
    loanPrincipal: 0,
    rate: 4,
    termYears: 25,
    insurancePerYear: 0,
    paymentsPerYear: 12,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLoanParams((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };
  const previousStep = () => {
    setActiveStep(Math.max(activeStep - 1, 0));
    formRefs[activeStep - 1].current?.setDataFromParent(formData);
  };
  // PMT calculation function
  const calculatePMT = (rate, nper, pv) => {
    if (rate === 0) return -(pv / nper);

    let pvif = Math.pow(1 + rate, nper);
    let pmt = (rate / (pvif - 1)) * -(pv * pvif);
    return pmt;
  };
  // Monthly rate
  const monthlyRate = loanParams.rate / 100 / loanParams.paymentsPerYear;
  // Total number of payments
  const totalPayments = loanParams.termYears * loanParams.paymentsPerYear;
  // Calculate monthly payment
  const monthlyPayment = Math.abs(
    calculatePMT(monthlyRate, totalPayments, loanParams.loanPrincipal)
  );
  return (
    <Box
      borderWidth="1px"
      rounded="lg"
      shadow="1px 1px 3px rgba(0,0,0,0.3)"
      p={8}
      bg="white"
      m="10px auto"
      as="form"
      width="100%"
      maxWidth="1200px"
      mx="auto"
      boxShadow="lg"
      borderRadius="md"
    >
      <Progress
        value={(activeStep / (steps.length - 1)) * 100}
        size="xs"
        colorScheme="teal"
        mb={6}
      />
      <StepperComponent
        steps={steps}
        activeStep={activeStep}
        setActiveStep={validateBeforeSetActiveStep}
      />
      <div style={{ display: activeStep === 0 ? 'block' : 'none' }}>
        <ProjectAnalysis
          onInputChange={handleInputChange}
          monthlyPayment={monthlyPayment}
        />
      </div>
      <div style={{ display: activeStep === 1 ? 'block' : 'none' }}>
        <LoanSheet
          loanPrincipal={loanParams.loanPrincipal}
          rate={loanParams.rate}
          isActive={activeStep === 1}
          monthlyPayment={monthlyPayment}
          totalPayments={totalPayments}
          insurancePerYear={loanParams.insurancePerYear}
          termYears={loanParams.termYears}
          paymentsPerYear={loanParams.paymentsPerYear}
        />
      </div>
      <StepperNavigationButtons
        isLastStep={isLastStep}
        onNextStep={onNextStep}
        previousStep={previousStep}
        activeStep={activeStep}
      />
    </Box>
  );
}
