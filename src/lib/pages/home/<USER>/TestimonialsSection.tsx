'use client';

import {
  Box,
  Flex,
  Heading,
  Text,
  Stack,
  Container,
  Avatar,
  useColorModeValue,
} from '@chakra-ui/react';

interface Props {
  children: React.ReactNode;
}

const Testimonial = (props: Props) => {
  const { children } = props;

  return <Box>{children}</Box>;
};

const TestimonialContent = (props: Props) => {
  const { children } = props;

  return (
    <Stack
      bg={useColorModeValue('white', 'gray.800')}
      boxShadow="xl"
      p={8}
      rounded="xl"
      align="center"
      pos="relative"
      border="1px solid"
      borderColor={useColorModeValue('teal.100', 'teal.900')}
      _hover={{
        transform: 'translateY(-5px)',
        transition: 'all 0.3s ease',
        boxShadow: '2xl',
      }}
      _after={{
        content: `""`,
        w: 0,
        h: 0,
        borderLeft: 'solid transparent',
        borderLeftWidth: 16,
        borderRight: 'solid transparent',
        borderRightWidth: 16,
        borderTop: 'solid',
        borderTopWidth: 16,
        borderTopColor: useColorModeValue('white', 'gray.800'),
        pos: 'absolute',
        bottom: '-16px',
        left: '50%',
        transform: 'translateX(-50%)',
      }}
    >
      {children}
    </Stack>
  );
};

const TestimonialText = (props: Props) => {
  const { children } = props;

  return (
    <Text
      textAlign="center"
      color={useColorModeValue('gray.600', 'gray.400')}
      fontSize="md"
      lineHeight="tall"
    >
      {children}
    </Text>
  );
};

const TestimonialAvatar = ({
  src,
  name,
  title,
}: {
  src: string;
  name: string;
  title: string;
}) => {
  return (
    <Flex align="center" mt={8} direction="column">
      <Avatar
        src={src}
        mb={2}
        size="lg"
        border="2px solid"
        borderColor="teal.500"
      />
      <Stack spacing={-1} align="center">
        <Text fontWeight={600} color="teal.500">
          {name}
        </Text>
        <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
          {title}
        </Text>
      </Stack>
    </Flex>
  );
};

export default function WithSpeechBubbles() {
  return (
    <Box>
      <Container maxW="5xl" py={16} as={Stack} spacing={12}>
        <Stack spacing={4} align="center">
          <Heading
            fontSize={{ base: '3xl', sm: '4xl' }}
            fontWeight="bold"
            bgGradient="linear(to-r, teal.400, teal.600)"
            bgClip="text"
          >
            Témoignages de nos clients
          </Heading>

          <Text
            color={useColorModeValue('gray.600', 'gray.400')}
            fontSize="lg"
            textAlign="center"
            maxW="2xl"
          >
            Découvrez ce que nos clients disent de leur expérience avec Mon
            Partenaire Renta
          </Text>
        </Stack>
        <Stack
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 10, md: 4, lg: 10 }}
        >
          <Testimonial>
            <TestimonialContent>
              <TestimonialText>
                « Mon Partenaire Renta m&apos;a accompagné dans mon projet
                d&apos;investissement locatif. Leur expertise et leur
                professionnalisme m&apos;ont permis de trouver le bien idéal
                pour maximiser ma rentabilité. »
              </TestimonialText>
            </TestimonialContent>
            <TestimonialAvatar
              src=""
              name="Marie Dupont"
              title="Investisseur immobilier"
            />
          </Testimonial>
          <Testimonial>
            <TestimonialContent>
              <TestimonialText>
                « Mon Partenaire Renta m&apos;a accompagné dans mon projet
                d&apos;investissement locatif. Leur expertise et leur
                professionnalisme m&apos;ont permis de trouver le bien idéal
                pour maximiser ma rentabilité. »
              </TestimonialText>
            </TestimonialContent>
            <TestimonialAvatar
              src=""
              name="Lucie Dubois"
              title="Investisseur immobilier"
            />
          </Testimonial>
          <Testimonial>
            <TestimonialContent>
              <TestimonialText>
                « Mon Partenaire Renta m&apos;a accompagné dans mon projet
                d&apos;investissement locatif. Leur expertise et leur
                professionnalisme m&apos;ont permis de trouver le bien idéal
                pour maximiser ma rentabilité. »
              </TestimonialText>
            </TestimonialContent>
            <TestimonialAvatar
              src=""
              name="Jean Martin"
              title="Investisseur immobilier"
            />
          </Testimonial>
        </Stack>
      </Container>
    </Box>
  );
}
