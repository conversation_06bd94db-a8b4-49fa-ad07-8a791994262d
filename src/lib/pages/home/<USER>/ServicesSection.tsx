'use client';

import {
  <PERSON>,
  But<PERSON>,
  Container,
  Flex,
  <PERSON>ing,
  Icon,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import type { ReactElement } from 'react';
import { FcCollaboration, FcDonate, FcHome, Fc<PERSON>ullish } from 'react-icons/fc';

interface CardProps {
  heading: string;
  description: string;
  icon: ReactElement;
}

const Card = ({ heading, description, icon }: CardProps) => {
  return (
    <Box
      maxW={{ base: 'full', md: '400px' }}
      w={{ base: 'full', md: '400px' }}
      h={{ base: 'auto', md: '300px' }}
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      p={6}
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-5px)',
        shadow: 'xl',
        borderColor: 'teal.200',
      }}
      shadow="md"
      bg={useColorModeValue('white', 'gray.800')}
      m={4}
      display="flex"
      flexDirection="column"
    >
      <Stack align="center" spacing={4} flex="1" textAlign="center">
        <Flex
          w={16}
          h={16}
          align="center"
          justify="center"
          color="white"
          rounded="full"
          bg={useColorModeValue('teal.50', 'teal.900')}
          transition="all 0.3s"
          _hover={{
            transform: 'scale(1.1)',
            bg: useColorModeValue('teal.100', 'teal.800'),
          }}
        >
          {icon}
        </Flex>
        <Box mt={2} flex="1" w="full">
          <Heading size="md" color={useColorModeValue('gray.700', 'white')}>
            {heading}
          </Heading>
          <Text
            mt={2}
            fontSize="sm"
            color={useColorModeValue('gray.600', 'gray.300')}
            lineHeight="tall"
            textAlign="center"
            maxW="90%"
            mx="auto"
            css={{
              textAlign: 'justify',
              '&:last-child': {
                textAlign: 'center',
              },
            }}
          >
            {description}
          </Text>
        </Box>
        <Button
          variant="link"
          colorScheme="teal"
          size="sm"
          fontWeight="semibold"
          _hover={{
            textDecoration: 'none',
            color: 'teal.500',
          }}
          mt="auto"
        >
          En savoir plus →
        </Button>
      </Stack>
    </Box>
  );
};

export default function ServicesSection() {
  return (
    <Container maxW="5xl">
      <Stack
        textAlign="center"
        p={5}
        align="center"
        spacing={{ base: 8, md: 10 }}
      >
        <Heading
          fontSize={{ base: '3xl', sm: '4xl' }}
          fontWeight="bold"
          bgGradient="linear(to-r, teal.400, teal.600)"
          bgClip="text"
        >
          Nos services
        </Heading>
        <Text
          color={useColorModeValue('gray.600', 'gray.400')}
          fontSize={{ base: 'md', sm: 'lg' }}
          maxW="3xl"
          mx="auto"
        >
          Chez Mon Partenaire Renta, nous nous engageons à vous fournir des
          services personnalisés pour répondre à vos besoins
          d&apos;investissement locatif.
        </Text>

        <Flex flexWrap="wrap" justify="center" gap={8}>
          <Card
            heading="Recherche de biens immobiliers"
            icon={<Icon as={FcHome} w={10} h={10} style={{ color: 'black' }} />}
            description="Analysez votre patrimoine et définissez des objectifs d'investissement clairs. Trouvez ensuite les biens les plus adaptés et évaluez leur pertinence grâce à nos conseils personnalisés."
          />
          <Card
            heading="Optimisation de la rentabilité"
            icon={<Icon as={FcBullish} w={10} h={10} />}
            description="Nous utilisons des outils de calcul de rentabilité avancés pour vous aider à maximiser la rentabilité de votre investissement locatif."
          />
          <Card
            heading="Acquisition & Recherche de crédit"
            icon={<Icon as={FcDonate} w={10} h={10} />}
            description="Nous vous donnons tous les outils pour maximiser vos chances d'obtenir un crédit, avec les meilleures conditions possibles."
          />
          <Card
            heading="Aide à la gestion de vos locations"
            icon={<Icon as={FcCollaboration} w={10} h={10} />}
            description="Tous les conseils, outils et documents pour gérer sereinement vos locations."
          />
        </Flex>
      </Stack>
    </Container>
  );
}
