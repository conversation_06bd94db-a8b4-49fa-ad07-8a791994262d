'use client';

import {
  <PERSON>,
  Container,
  SimpleGrid,
  Center,
  Text,
  Stack,
  HStack,
  VStack,
  Avatar,
  Image,
  Button,
} from '@chakra-ui/react';
import { keyframes } from '@emotion/react';

const floatAnimation = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
`;

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

export default function GridListWithHeading() {
  return (
    <Center>
      <Box
        maxW="5xl"
        w="full"
        rounded="xl"
        bgGradient="linear(to-br, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2))"
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
        boxShadow="2xl"
        animation={`${fadeIn} 1s ease-out`}
      >
        <Stack
          spacing={15}
          as={Container}
          maxW="xl"
          p={8}
          justifyContent="center"
          textAlign={{ base: 'center', lg: 'start' }}
        >
          <Text color="gray.800" fontSize="xl" fontWeight="medium">
            Chez <b>Mon-partenaire-renta.fr</b> nous avons conscience du travail
            et des difficultés que représentent la <b>planification</b> et la{' '}
            <b>mise en place</b> d&apos;un investissement locatif.
          </Text>
          <Text color="gray.800" fontSize="xl" fontWeight="medium">
            Nous mettons donc à votre disposition l&apos;ensemble des outils
            nécessaires au <b>bon déroulement serein</b> de vos investissements.
          </Text>
        </Stack>
        <Stack
          spacing={15}
          as={Container}
          maxW="xl"
          p={8}
          justifyContent="center"
          textAlign={{ base: 'center', lg: 'start' }}
        >
          <SimpleGrid columns={{ base: 1, md: 1, lg: 1 }} spacing={4}>
            <HStack>
              <VStack align="center" spacing={6}>
                <Text
                  fontWeight={600}
                  fontSize="2xl"
                  fontFamily="'Playfair Display', serif"
                  color="gray.700"
                  textAlign="center"
                  letterSpacing="tight"
                  lineHeight="1.3"
                  mb={2}
                >
                  Une plateforme conçue par des investisseurs passionnés, pour
                  accompagner votre succès immobilier
                </Text>
                <Button
                  h={10}
                  fontSize="md"
                  alignItems="center"
                  m={1}
                  fontWeight={500}
                  colorScheme="teal"
                  variant="solid"
                  bg="teal.500"
                  color="white"
                  _hover={{
                    bg: 'teal.600',
                    transform: 'translateY(-2px)',
                    boxShadow: 'lg',
                  }}
                  transition="all 0.2s"
                >
                  Découvrir l&apos;équipe
                </Button>
                <Box
                  padding="6"
                  borderRadius="2xl"
                  borderWidth={3}
                  borderStyle="dashed"
                  borderColor="teal.300"
                  color="gray.700"
                  bg="white"
                  boxShadow="xl"
                  animation={`${floatAnimation} 3s ease-in-out infinite`}
                  _hover={{
                    transform: 'scale(1.02)',
                    boxShadow: '2xl',
                  }}
                  transition="all 0.3s"
                  w="full"
                  overflow="hidden"
                >
                  <HStack spacing={6} align="start" w="full">
                    <VStack spacing={4} align="center">
                      <Image
                        src="/icon/livre.webp"
                        alt="Book icon"
                        flexShrink={0}
                      />
                      <Button
                        w="fit-content"
                        h={{ base: '8', lg: '10' }}
                        fontSize={['sm', 'md']}
                        fontWeight={500}
                        colorScheme="teal"
                        bg="teal.500"
                        variant="solid"
                        borderWidth={1}
                        borderColor="teal.600"
                        color="white"
                        _hover={{
                          bg: 'teal.600',
                          transform: 'translateY(-2px)',
                          boxShadow: 'lg',
                        }}
                        transition="all 0.2s"
                      >
                        Découvrir le livre
                      </Button>
                    </VStack>
                    <VStack
                      align="start"
                      spacing={4}
                      w="full"
                      overflow="hidden"
                    >
                      <Text
                        fontSize={['sm', 'md', 'lg']}
                        fontWeight="medium"
                        wordBreak="break-word"
                        w="full"
                      >
                        Avec les <b>conseils personnalisés</b> de{' '}
                        <b>Daniel VU</b>, auteur du livre best-seller{' '}
                        <Text
                          display="inline"
                          fontWeight={600}
                          fontStyle="italic"
                          color="teal.500"
                        >
                          &quot;Comment Investir en Immobilier Locatif&quot;
                        </Text>
                      </Text>
                      <Container
                        display={{ base: 'initial', lg: 'flex' }}
                        gap={4}
                      >
                        <Avatar
                          name="Daniel VU"
                          size={{ base: 'lg', lg: '2xl' }}
                          src="/icon/daniel-vu.webp"
                          borderWidth={3}
                          borderColor="teal.300"
                          boxShadow="md"
                        />
                      </Container>
                    </VStack>
                  </HStack>
                </Box>
              </VStack>
            </HStack>
          </SimpleGrid>
        </Stack>
      </Box>
    </Center>
  );
}
