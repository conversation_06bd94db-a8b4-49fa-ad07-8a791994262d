'use client';

import { <PERSON><PERSON>, Container, <PERSON>ack, Text, But<PERSON>, Box } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { Illustration } from '~/lib/components/commonComponents/Illustration';

const MotionStack = motion(Stack);
const MotionText = motion(Text);
const MotionButton = motion(Button);
const MotionBox = motion(Box);

export default function CallToActionWithIllustration() {
  return (
    <Box position="relative" overflow="hidden">
      <Container maxW="5xl">
        <MotionStack
          textAlign="center"
          p={4}
          align="center"
          spacing={{ base: 6, md: 8 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <MotionText
            bgClip="text"
            color="black"
            fontSize={{ base: 'xl', sm: '2xl', md: '3xl' }}
            maxW="3xl"
            fontWeight="bold"
            lineHeight="1.3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            textShadow="0 2px 4px rgba(0,0,0,0.1)"
          >
            <Text
              fontWeight={600}
              fontSize="2xl"
              color="gray.700"
              textAlign="center"
              fontFamily="'Playfair Display', serif"
              letterSpacing="tight"
              lineHeight="1.3"
              mb={2}
            >
              Chez Mon Partenaire Renta, nous mettons à votre disposition des
              outils de calcul de rentabilité avancés pour maximiser la
              rentabilité de votre investissement locatif.
            </Text>
          </MotionText>
          <Stack spacing={4} direction="row">
            <MotionButton
              as="a"
              rounded="md"
              px={6}
              py={4}
              colorScheme="teal"
              bgGradient="linear(to-r, teal.600, teal.400)"
              href="/pricing"
              _hover={{
                transform: 'translateY(-1px)',
                boxShadow: 'md',
                bg: 'teal.600',
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Je découvre
            </MotionButton>
          </Stack>
          <Flex w="full" justify="center">
            <MotionBox
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Illustration height={{ sm: '10rem', lg: '18rem' }} mt={2} />
            </MotionBox>
          </Flex>
        </MotionStack>
      </Container>
    </Box>
  );
}
