'use client';

import {
  Container,
  SimpleGrid,
  Image,
  Flex,
  Text,
  Stack,
  StackDivider,
  Icon,
  useColorModeValue,
  Button,
  Box,
  Heading,
} from '@chakra-ui/react';
import { GrOptimize } from 'react-icons/gr';
import { IoAnalyticsSharp, IoHelpBuoy } from 'react-icons/io5';
import { TbCreditCard } from 'react-icons/tb';
import { motion } from 'framer-motion';

import LabelWithIcon from '~/lib/components/commonComponents/LabelWithIcon';

const MotionBox = motion(Box);

export default function SplitWithImage() {
  const gradientText = useColorModeValue(
    'linear(to-r, teal.600, teal.400)',
    'linear(to-r, teal.400, teal.200)'
  );

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.100', 'gray.700');

  return (
    <Container maxW="5xl" p={0}>
      <SimpleGrid
        columns={{ base: 1, md: 2 }}
        spacing={8}
        p={0}
        alignItems="center"
        width="100%"
      >
        <Stack
          spacing={6}
          bg={cardBg}
          p={8}
          rounded="xl"
          shadow="lg"
          borderWidth="1px"
          borderColor={borderColor}
          width="100%"
        >
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Heading
              fontSize={{ base: '3xl' }}
              fontWeight={700}
              lineHeight="1.2"
              fontFamily="'Playfair Display', serif"
              letterSpacing="tight"
              mb={4}
            >
              Le meilleur outil digital{' '}
              <Text
                as="span"
                bgGradient={gradientText}
                bgClip="text"
                fontWeight={800}
              >
                tout-en-un
              </Text>{' '}
              pour obtenir de façon{' '}
              <Text
                as="span"
                bgGradient={gradientText}
                bgClip="text"
                fontWeight={800}
              >
                instantanée
              </Text>{' '}
              les données essentielles pour votre projet locatif.
            </Heading>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Text
              color={useColorModeValue('gray.600', 'gray.300')}
              fontSize="xl"
              lineHeight="1.6"
            >
              <b>Mon-partenaire-renta.fr</b> votre partenaire vous un
              accompagnement{' '}
              <Text
                as="span"
                bgGradient={gradientText}
                bgClip="text"
                fontWeight={700}
              >
                sur-mesure
              </Text>{' '}
              sur <b>toutes les phases</b> de votre projet{' '}
              <b>d&apos;investissement immobilier locatif.</b>
            </Text>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Text
              color={useColorModeValue('gray.600', 'gray.300')}
              fontSize="lg"
              lineHeight="1.6"
            >
              Nous mettons également à votre disposition des{' '}
              <b>outils de calcul</b> de <b>rentabilité avancés</b>, afin
              d&apos;optimiser votre rendement et éviter des pertes.
            </Text>
          </MotionBox>

          <Stack
            spacing={4}
            divider={
              <StackDivider
                borderColor={useColorModeValue('gray.100', 'gray.700')}
                opacity={0.8}
              />
            }
          >
            <MotionBox
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              width="100%"
            >
              <LabelWithIcon
                icon={
                  <Icon as={IoAnalyticsSharp} color="yellow.500" w={6} h={6} />
                }
                iconBg={useColorModeValue('yellow.100', 'yellow.900')}
                text="Planification de l'acquisition"
              />
            </MotionBox>
            <MotionBox
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              width="100%"
            >
              <LabelWithIcon
                icon={<Icon as={TbCreditCard} color="green.500" w={6} h={6} />}
                iconBg={useColorModeValue('green.100', 'green.900')}
                text="Conseils sur l'obtention du crédit"
              />
            </MotionBox>

            <MotionBox
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              width="100%"
            >
              <LabelWithIcon
                icon={<Icon as={GrOptimize} color="purple.500" w={6} h={6} />}
                iconBg={useColorModeValue('purple.100', 'purple.900')}
                text="Optimisation de la fiscalité"
              />
            </MotionBox>
            <MotionBox
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
              width="100%"
            >
              <LabelWithIcon
                icon={<Icon as={IoHelpBuoy} color="blue.500" w={6} h={6} />}
                iconBg={useColorModeValue('blue.100', 'blue.900')}
                text="Aide à la gestion locative"
              />
            </MotionBox>

            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <Button
                as="a"
                size="lg"
                fontSize="lg"
                fontWeight={600}
                bgGradient="linear(to-r, teal.600, teal.400)"
                color="white"
                _hover={{
                  bgGradient: 'linear(to-r, teal.700, teal.500)',
                  transform: 'translateY(-2px)',
                  boxShadow: 'xl',
                }}
                _active={{
                  bgGradient: 'linear(to-r, teal.800, teal.600)',
                }}
                transition="all 0.3s ease"
                href="/pricing"
              >
                Commencer
              </Button>
            </MotionBox>
          </Stack>
        </Stack>
        <Flex
          justify="center"
          align="center"
          position="relative"
          width="100%"
          _before={{
            content: '""',
            position: 'absolute',
            width: 'full',
            height: 'full',
            bgGradient: 'linear(to-br, teal.100, teal.50)',
            rounded: '2xl',
            transform: 'rotate(-3deg)',
            zIndex: 0,
            opacity: 0.8,
          }}
          _after={{
            content: '""',
            position: 'absolute',
            width: 'full',
            height: 'full',
            bgGradient: 'linear(to-br, teal.200, teal.100)',
            rounded: '2xl',
            transform: 'rotate(3deg)',
            zIndex: 0,
            opacity: 0.4,
          }}
        >
          <MotionBox
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            position="relative"
            zIndex={1}
          >
            <Image
              rounded="2xl"
              alt="feature image"
              src="https://images.unsplash.com/photo-1554200876-56c2f25224fa?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
              objectFit="cover"
              boxShadow="2xl"
              transform="rotate(3deg)"
              transition="transform 0.3s ease-in-out"
              _hover={{ transform: 'rotate(0deg)' }}
            />
          </MotionBox>
        </Flex>
      </SimpleGrid>
    </Container>
  );
}
