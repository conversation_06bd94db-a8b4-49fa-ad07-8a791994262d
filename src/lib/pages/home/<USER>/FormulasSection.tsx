'use client';

import {
  Box,
  Button,
  Center,
  Flex,
  Heading,
  HStack,
  List,
  ListIcon,
  ListItem,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';
import { FaCheckCircle } from 'react-icons/fa';

import PriceWrapper from '~/lib/components/commonComponents/PriceWrapper';

export default function SplitScreen() {
  return (
    <Center position="relative">
      <Box maxW="5xl" w="full" overflow="hidden">
        <Stack
          minH="45vh"
          direction={{ base: 'column', md: 'row' }}
          align="center"
          spacing={{ base: 8, md: 12 }}
        >
          <Flex p={8} flex={1} align="center" justify="center">
            <Stack spacing={8} w="full" maxW="xl">
              <Heading
                fontSize={{ base: '2xl', md: '3xl', lg: '4xl' }}
                fontWeight="bold"
                bgGradient="linear(to-r, teal.800,black)"
                bgClip="text"
              >
                Nos formules
              </Heading>
              <Text
                fontSize={{ base: 'md', lg: 'lg' }}
                color="gray.600"
                lineHeight="tall"
                textAlign={{ base: 'center', md: 'left' }}
                maxW="2xl"
              >
                Chez Mon Partenaire Renta, nous proposons des tarifs adaptés à
                vos besoins d&apos;investissement locatif.
              </Text>
              <Stack
                direction="column"
                spacing={4}
                align={{ base: 'center', md: 'flex-start' }}
                alignSelf={{ base: 'center', md: 'flex-start' }}
                position="relative"
                w="full"
              >
                <Button
                  as="a"
                  size="lg"
                  fontSize="lg"
                  fontWeight={600}
                  bgGradient="linear(to-r, teal.600, teal.400)"
                  color="white"
                  _hover={{
                    bgGradient: 'linear(to-r, teal.700, teal.500)',
                    transform: 'translateY(-2px)',
                    boxShadow: 'xl',
                  }}
                  _active={{
                    bgGradient: 'linear(to-r, teal.800, teal.600)',
                  }}
                  transition="all 0.3s ease"
                  href="/pricing"
                >
                  Voir nos formules
                </Button>
              </Stack>
            </Stack>
          </Flex>
          <Flex
            flex={1}
            minH="500px"
            py={8}
            justifyContent="center"
            alignItems="center"
          >
            <PriceWrapper>
              <Box
                position="relative"
                transition="all 0.3s"
                bg="white"
                borderRadius="3xl"
                boxShadow="xl"
                overflow="visible"
                _hover={{
                  transform: 'translateY(-5px)',
                  boxShadow: '2xl',
                }}
              >
                <Box
                  position="absolute"
                  top="-16px"
                  left="50%"
                  style={{ transform: 'translate(-50%)' }}
                  zIndex={1}
                >
                  <Text
                    textTransform="uppercase"
                    bgGradient="linear(to-r, teal.400, teal.500)"
                    px={4}
                    py={1}
                    width="max-content"
                    color="white"
                    fontSize="sm"
                    fontWeight="600"
                    rounded="xl"
                    boxShadow="md"
                  >
                    LE PLUS POPULAIRE
                  </Text>
                </Box>
                <Box
                  py={8}
                  px={10}
                  bgGradient="linear(to-b, teal.50, white)"
                  borderBottom="2px"
                  borderColor="gray.100"
                >
                  <Text fontWeight="600" fontSize="2xl" color="gray.700" mb={3}>
                    Standard
                  </Text>
                  <HStack
                    justifyContent="center"
                    spacing={1}
                    alignItems="baseline"
                  >
                    <Text fontSize="3xl" fontWeight="600" color="gray.700">
                      €
                    </Text>
                    <Text
                      fontSize="5xl"
                      fontWeight="900"
                      bgGradient="linear(to-r, teal.600, teal.400)"
                      bgClip="text"
                      lineHeight="1"
                    >
                      50
                    </Text>
                    <Text fontSize="2xl" color="gray.500">
                      /mois
                    </Text>
                  </HStack>
                </Box>
                <VStack bg="white" py={8} spacing={6}>
                  <List spacing={4} textAlign="start" px={10} w="full">
                    <ListItem
                      display="flex"
                      alignItems="center"
                      py={2}
                      borderBottom="1px"
                      borderColor="gray.100"
                    >
                      <ListIcon
                        as={FaCheckCircle}
                        color="teal.500"
                        boxSize={5}
                        mr={4}
                      />
                      <Text fontSize="md" color="gray.600" fontWeight="500">
                        Valable 12 mois
                      </Text>
                    </ListItem>
                    <ListItem
                      display="flex"
                      alignItems="center"
                      py={2}
                      borderBottom="1px"
                      borderColor="gray.100"
                    >
                      <ListIcon
                        as={FaCheckCircle}
                        color="teal.500"
                        boxSize={5}
                        mr={4}
                      />
                      <Text fontSize="md" color="gray.600" fontWeight="500">
                        Je suis un avantage
                      </Text>
                    </ListItem>
                    <ListItem display="flex" alignItems="center" py={2}>
                      <ListIcon
                        as={FaCheckCircle}
                        color="teal.500"
                        boxSize={5}
                        mr={4}
                      />
                      <Text fontSize="md" color="gray.600" fontWeight="500">
                        Je suis un avantage
                      </Text>
                    </ListItem>
                  </List>
                  <Box w="80%" pt={4}>
                    <Button
                      w="full"
                      colorScheme="teal"
                      size="lg"
                      rounded="full"
                      fontWeight="semibold"
                      py={6}
                      _hover={{
                        transform: 'translateY(-2px)',
                        boxShadow: 'lg',
                        bg: 'teal.600',
                      }}
                      transition="all 0.2s"
                    >
                      Commencer
                    </Button>
                  </Box>
                </VStack>
              </Box>
            </PriceWrapper>
          </Flex>
        </Stack>
      </Box>
    </Center>
  );
}
