import { Grid, Box } from '@chakra-ui/react';
import CardsBody from './components/CardsBody';
import CTASection from './components/CTASection';
import DiscoverSection from './components/DiscoverSection';
import FormulasSection from './components/FormulasSection';
import PartnersSection from './components/PartnersSection';
import ServicesSecondPart from './components/ServicesSecondPart';
import ServicesSection from './components/ServicesSection';
import TestimonialsSection from './components/TestimonialsSection';

import { motion } from 'framer-motion';

const AnimatedBox = motion(Box);

const Home = () => {
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: 'easeInOut' },
    },
  };

  return (
    <Grid gap={6}>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <CTASection />
      </AnimatedBox>

      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <CardsBody />
      </AnimatedBox>

      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <ServicesSection />
      </AnimatedBox>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <ServicesSecondPart />
      </AnimatedBox>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <PartnersSection />
      </AnimatedBox>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <TestimonialsSection />
      </AnimatedBox>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <FormulasSection />
      </AnimatedBox>
      <AnimatedBox
        variants={sectionVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <DiscoverSection />
      </AnimatedBox>
    </Grid>
  );
};

export default Home;
