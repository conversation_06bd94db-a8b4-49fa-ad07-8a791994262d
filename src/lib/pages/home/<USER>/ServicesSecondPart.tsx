'use client';

import {
  Box,
  Center,
  Container,
  Flex,
  Heading,
  Icon,
  Image,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { IoAnalyticsSharp, IoHelpBuoy } from 'react-icons/io5';
import { TbCreditCard } from 'react-icons/tb';

import LabelWithIcon from '~/lib/components/commonComponents/LabelWithIcon';

export default function SplitScreen() {
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const iconHoverStyle = { transform: 'scale(1.1)' };

  return (
    <Center py={8}>
      <Box maxW="4xl" w="full" overflow="hidden">
        <Stack
          minH="30vh"
          direction={{ base: 'column', md: 'row' }}
          spacing={6}
        >
          <Flex flex={1} p={1} height={300} justify="center" align="center">
            <Image
              alt="Login Image"
              objectFit="cover"
              borderRadius="md"
              src="https://images.unsplash.com/photo-1527689368864-3a821dbccc34?ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
            />
          </Flex>
          <Flex p={2} flex={1} align="center" justify="center">
            <Stack spacing={6} as={Container} maxW="3xl" textAlign="start">
              <Heading
                fontSize={{ base: 'xl', md: '2xl', lg: '3xl' }}
                fontWeight="700"
                color="teal.500"
                lineHeight="1.3"
                letterSpacing="tight"
              >
                Investir dans l&apos;immobilier avec Mon Partenaire Renta
              </Heading>
              <Text
                color={textColor}
                fontSize={{ base: 'xs', md: 'sm', lg: 'md' }}
                lineHeight="1.8"
                fontWeight="400"
                letterSpacing="0.3px"
              >
                Chez Mon Partenaire Renta, nous sommes convaincus que
                l&apos;immobilier est un investissement rentable à long terme.
                Nous sommes là pour vous aider à réaliser votre projet
                d&apos;investissement locatif.
              </Text>
              <Stack spacing={4} w="full">
                <LabelWithIcon
                  icon={
                    <Icon
                      as={IoAnalyticsSharp}
                      color="yellow.500"
                      w={7}
                      h={7}
                      transition="transform 0.2s"
                      _groupHover={iconHoverStyle}
                    />
                  }
                  iconBg={useColorModeValue('yellow.100', 'yellow.900')}
                  text="Analyse patrimoniale"
                  fontSize="sm"
                  fontWeight="500"
                />
                <LabelWithIcon
                  icon={
                    <Icon
                      as={TbCreditCard}
                      color="green.500"
                      w={7}
                      h={7}
                      transition="transform 0.2s"
                      _groupHover={iconHoverStyle}
                    />
                  }
                  iconBg={useColorModeValue('green.100', 'green.900')}
                  text="Conseils personnalisés"
                  fontSize="sm"
                  fontWeight="500"
                />
                <LabelWithIcon
                  icon={
                    <Icon
                      as={IoHelpBuoy}
                      color="purple.500"
                      w={7}
                      h={7}
                      transition="transform 0.2s"
                      _groupHover={iconHoverStyle}
                    />
                  }
                  iconBg={useColorModeValue('purple.100', 'purple.900')}
                  text="Aide à la gestion de vos locations"
                  fontSize="sm"
                  fontWeight="500"
                />
              </Stack>
            </Stack>
          </Flex>
        </Stack>
      </Box>
    </Center>
  );
}
