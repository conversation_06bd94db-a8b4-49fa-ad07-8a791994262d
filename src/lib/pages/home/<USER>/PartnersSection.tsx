'use client';

import {
  Box,
  Container,
  Heading,
  Image,
  Stack,
  Text,
  useColorModeValue,
  SimpleGrid,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

const partners = [
  { id: 'avi', src: '/icon/avi.webp', alt: 'AVI Logo' },
  {
    id: 'prosper',
    src: '/icon/prosper-conseil.webp',
    alt: 'Prosper Conseil Logo',
  },
  { id: 'cafpi', src: '/icon/cafpi.webp', alt: 'CAFPI Logo' },
  { id: 'era', src: '/icon/era.webp', alt: 'ERA Logo' },
  {
    id: 'strongest',
    src: '/icon/strongest-link.webp',
    alt: 'Strongest Link Logo',
  },
  { id: 'cog', src: '/icon/cog-industry.webp', alt: 'Cog Industry Logo' },
  { id: 'at', src: '/icon/at-motion.webp', alt: 'AT Motion Logo' },
  { id: 'bond', src: '/icon/bond.webp', alt: 'Bond Logo' },
];

export default function PartnersSection() {
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const cardBg = useColorModeValue('white', 'gray.800');
  const cardBorderColor = useColorModeValue('gray.200', 'gray.700');
  const accentColor = useColorModeValue('teal.500', 'teal.300');

  return (
    <MotionBox
      py={5}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <Container maxW="5xl">
        <Stack spacing={6} align="center" mb={12}>
          <Heading
            fontSize={{ base: '3xl', sm: '4xl' }}
            fontWeight="bold"
            bgGradient="linear(to-r, teal.400, teal.600)"
            bgClip="text"
          >
            Nos Partenaires
          </Heading>
          <Text
            color={textColor}
            fontSize={{ base: 'md', sm: 'lg' }}
            textAlign="center"
            maxW="2xl"
          >
            Nous sommes fiers de travailler avec les plus grands acteurs de ce
            marché:
          </Text>
        </Stack>

        <SimpleGrid
          columns={{ base: 2, md: 3, lg: 4 }}
          spacing={{ base: 8, md: 10 }}
          px={{ base: 4, md: 8 }}
        >
          {partners.map((partner) => (
            <MotionBox
              key={partner.id}
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.2 }}
              p={6}
              borderRadius="xl"
              bg={cardBg}
              border="1px solid"
              borderColor={cardBorderColor}
              boxShadow="sm"
              _hover={{
                boxShadow: 'lg',
                borderColor: accentColor,
              }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              minH="120px"
            >
              <Image
                src={partner.src}
                alt={partner.alt}
                maxH="60px"
                objectFit="contain"
                filter="grayscale(100%)"
                _hover={{ filter: 'grayscale(0%)' }}
                transition="all 0.3s ease"
              />
            </MotionBox>
          ))}
        </SimpleGrid>
      </Container>
    </MotionBox>
  );
}
