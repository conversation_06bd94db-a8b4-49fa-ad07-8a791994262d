import React from 'react';
import {
  Box,
  Flex,
  Link,
  Text,
  Input,
  Image,
  Heading,
  Container,
  useColorModeValue,
  InputGroup,
  InputLeftElement,
  Stack,
  Center,
  SimpleGrid,
  Divider,
} from '@chakra-ui/react';
import siteData from '~/data/siteData.json';
import { FaSearch } from 'react-icons/fa';

const App = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const borderColor = useColorModeValue('gray.300', 'gray.600');

  const [searchTerm, setSearchTerm] = React.useState('');

  const filteredSites = siteData.filter((site) =>
    site.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const placeholderImage = '/placeholder_image.png';
  const inputBg = useColorModeValue('white', 'gray.700');
  const inputBorderColor = useColorModeValue('gray.300', 'gray.600');
  const inputFocusBorderColor = useColorModeValue('blue.500', 'blue.300');

  return (
    <Container maxW="container.lg" py={10}>
      <Stack spacing={6} as={Center} direction="column">
        <Heading as="h1" textAlign="center" size="lg" color={textColor}>
          Sites d'annonces et d'évaluation immobilière
        </Heading>
        <InputGroup w={{ base: '100%', md: 'md' }}>
          <InputLeftElement>
            <FaSearch color={useColorModeValue('gray.500', 'gray.400')} />
          </InputLeftElement>
          <Input
            placeholder="Recherche..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            bg={inputBg}
            borderColor={inputBorderColor}
            focusBorderColor={inputFocusBorderColor}
            _hover={{ borderColor: useColorModeValue('gray.400', 'gray.500') }}
            borderRadius="md"
            boxShadow="sm"
          />
        </InputGroup>
      </Stack>

      <Divider my={6} />

      <Box
        maxH="500px"
        overflowY="auto"
        p={4}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        bg={bgColor}
        boxShadow="sm"
      >
        <SimpleGrid columns={{ base: 1, sm: 2, md: 3 }} spacing={4}>
          {filteredSites.map((site) => (
            <Box key={site.url} w="full">
              <Link
                href={site.url}
                isExternal
                _hover={{ textDecoration: 'none' }}
              >
                <Box
                  p={4}
                  borderWidth="1px"
                  borderRadius="md"
                  borderColor={borderColor}
                  bg={useColorModeValue('gray.50', 'gray.700')}
                  _hover={{ bg: useColorModeValue('gray.200', 'gray.600') }}
                  transition="background 0.2s ease-in-out"
                >
                  <Flex alignItems="center">
                    <Image
                      src={site.icon}
                      alt={`${site.name} icon`}
                      boxSize="32px"
                      borderRadius="full"
                      boxShadow="full"
                      mr={3}
                      fallback={
                        <Box w={8} h={8} bg="gray.400" borderRadius="full" />
                      }
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = placeholderImage;
                      }}
                    />
                    <Text color={textColor} fontWeight="medium">
                      {site.name}
                    </Text>
                  </Flex>
                </Box>
              </Link>
            </Box>
          ))}
        </SimpleGrid>
      </Box>
    </Container>
  );
};

export default App;
