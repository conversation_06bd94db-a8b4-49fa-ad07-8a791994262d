'use client';

import {
  Box,
  Center,
  Container,
  Stack,
  Text,
  Link,
  Image,
  IconButton,
  useBreakpointValue,
  Heading,
} from '@chakra-ui/react';
import React from 'react';
// Here we have used react-icons package for the icons
import { BiLeftArrowAlt, BiRightArrowAlt } from 'react-icons/bi';
// And react-slick as our Carousel Lib
import Slider from 'react-slick';

// Settings for the slider
const settings = {
  dots: true,
  arrows: false,
  fade: true,
  infinite: true,
  autoplay: false,
  speed: 500,
  autoplaySpeed: 5000,
  slidesToShow: 1,
  slidesToScroll: 1,
};

export function CaptionCarousel() {
  // As we have used custom buttons, we need a reference variable to
  // change the state
  const [slider, setSlider] = React.useState<Slider | null>(null);

  // These are the breakpoints which changes the position of the
  // buttons as the screen size changes
  const top = useBreakpointValue({ base: '90%', md: '50%' });
  const side = useBreakpointValue({ base: '30%', md: '40px' });
  const bilanPatrimonial = 'Bilan patrimonial';
  // This list contains all the data for carousels
  // This can be static or loaded from a server
  const cards = [
    {
      title: bilanPatrimonial,
      text: 'Définir sa tranche de prix',
      image: '/icon/tranche-prix.webp',
      url: '/table-of-contents/budget-definition',
    },
    {
      title: bilanPatrimonial,
      text: "Quelle-est votre capacité d'emprunt ?",
      image: '/icon/money.webp',
      url: '/table-of-contents/borrow-capacity',
    },
    {
      title: bilanPatrimonial,
      text: "Le Taux d'endettement",
      image: '/icon/taux-debit.webp',
      url: '/table-of-contents/depth-rate',
    },
    {
      title: bilanPatrimonial,
      text: `Votre "Reste à vivre`,
      image: '/icon/rest-a-vivre.webp',
      url: '/table-of-contents/remain-to-survive',
    },
    {
      title: bilanPatrimonial,
      text: `Mesurer le risque pesant sur une opération`,
      image: '/icon/mesure-risque.webp',
      url: '/',
    },
    {
      title: bilanPatrimonial,
      text: `Diversifier ses investissements et établir une réserve de sécurité`,
      image: '/icon/investissements.webp',
      url: '/',
    },
  ];

  return (
    <Box
      height={{ base: 'md', lg: 'xl' }}
      position="relative"
      width="full"
      overflow="hidden"
    >
      {/* CSS files for react-slick */}
      <link
        rel="stylesheet"
        type="text/css"
        href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
      />
      <link
        rel="stylesheet"
        type="text/css"
        href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
      />
      {/* Left Icon */}
      <IconButton
        aria-label="left-arrow"
        variant="ghost"
        position="absolute"
        left={side}
        top={top}
        transform="translate(0%, -50%)"
        zIndex={2}
        onClick={() => slider?.slickPrev()}
      >
        <BiLeftArrowAlt size="40px" />
      </IconButton>
      {/* Right Icon */}
      <IconButton
        aria-label="right-arrow"
        variant="ghost"
        position="absolute"
        right={side}
        top={top}
        transform="translate(0%, -50%)"
        zIndex={2}
        onClick={() => slider?.slickNext()}
      >
        <BiRightArrowAlt size="40px" />
      </IconButton>
      {/* Slider */}
      <Slider
        {...settings}
        ref={(sliderInstance: Slider | null) => setSlider(sliderInstance)}
      >
        {cards.map((card, index) => (
          <Box
            key={index as number}
            height={{ base: '350px', lg: 'lg' }}
            position="relative"
          >
            <Container
              size="container.lg"
              height={{ base: 'sm', lg: 'lg' }}
              position="relative"
            >
              <Stack
                spacing={6}
                w={{ base: '2xs', lg: 'full' }}
                maxW="lg"
                border="1px"
                p={2}
                borderRadius={4}
                position="absolute"
                top="50%"
                transform="translate(0, -50%)"
              >
                <Link href={card.url}>
                  <Image w="full" src={card.image} />
                </Link>
                <Heading fontSize={{ base: 'xl', md: '1xl', lg: '2xl' }}>
                  {card.title}
                </Heading>
                <Text fontSize={{ base: 'md', lg: 'lg' }} color="GrayText">
                  {card.text}
                </Text>
              </Stack>
            </Container>
          </Box>
        ))}
      </Slider>
    </Box>
  );
}

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w={{ base: 'xs', lg: 'full' }}
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={{ base: '5', lg: '10' }}
          gap={{ base: '0', lg: '10' }}
          justifyContent="center"
        >
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            Guides méthodologiques :
          </Text>
          <CaptionCarousel />
        </Stack>
      </Box>
    </Center>
  );
}
