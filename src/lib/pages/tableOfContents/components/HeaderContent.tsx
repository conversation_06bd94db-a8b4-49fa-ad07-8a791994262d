'use client';

import {
  Box,
  Center,
  Container,
  Flex,
  Heading,
  Image,
  Stack,
  Text,
} from '@chakra-ui/react';

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Flex flexWrap="wrap" justify="center">
          <Image w="full" src="/icon/bilan-patrimonial.webp" />
        </Flex>
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          p={3}
          justifyContent="center"
        >
          <Heading fontSize={{ base: '2xl', sm: '4xl' }} fontWeight="bold">
            Bilan Patrimonial
          </Heading>
          <Text color="gray.600" fontSize={{ base: 'sm', sm: 'lg' }}>
            Définissez vos objectifs d&apos;investissement.
          </Text>
        </Stack>
      </Box>
    </Center>
  );
}
