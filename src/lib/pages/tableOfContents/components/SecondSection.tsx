'use client';

import { Box, Button, Center, Container, Stack, Text } from '@chakra-ui/react';
import { IoIosArrowForward } from 'react-icons/io';

export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={10}
          gap={10}
          justifyContent="center"
        >
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            Solliciter une synthèse patrimoniale :
          </Text>
          <Text color="gray.600" fontSize={{ base: 'sm', sm: 'lg' }}>
            Remplissez vos informations, nous vous fournissons une analyse
            patrimoniale personnalisée et automatisée.
          </Text>
          <Button
            size="md"
            width="150px"
            border="1px"
            borderRadius={0}
            fontWeight="none"
            rightIcon={<IoIosArrowForward />}
            variant="outline"
          >
            Remplir
          </Button>
        </Stack>
      </Box>
    </Center>
  );
}
