'use client';

import { Box, Center, Container, Text, Stack } from '@chakra-ui/react';

const bilanInfo = {
  title: 'Un bilan patrimonial... pour quoi faire?',
  firstSection: `"Un type d'investissement immobilier locatif adapté à chaque profil d'investisseur", si ce concept est bien connu, beaucoup d'investisseurs peinent à l'appliquer de façon pratique.`,
  secondSection:
    "Le but de cette partie est donc de vous fournir les outils et méthodes adaptées pour mieux apprécier au mieux la typologie de biens que vous rechercherez par la suite, afin qu'elle soit adaptée à votre situation patrimoniale ainsi qu'à vos objectifs futurs.",
  thirdSection:
    'Vous éviterez ainsi de vous disperser ultérieurement dans votre étape de recherche de biens.',
};
export default function gridListWith() {
  return (
    <Center py={6}>
      <Box
        maxW="6xl"
        w="full"
        boxShadow="xs"
        rounded="md"
        bg="white"
        gap={10}
        display={{ base: 'initial', lg: 'flex' }}
        overflow="hidden"
      >
        <Stack
          spacing={2}
          as={Container}
          maxW="5xl"
          py={10}
          gap={10}
          justifyContent="center"
        >
          <Text
            textDecoration="underline"
            fontSize={{ base: '2xl', sm: '4xl' }}
          >
            {bilanInfo.title}
          </Text>
          <Text color="gray.600" fontSize={{ base: 'sm', sm: 'lg' }}>
            {bilanInfo.firstSection}
          </Text>
          <Text color="gray.600" fontSize={{ base: 'sm', sm: 'lg' }}>
            {bilanInfo.secondSection}
          </Text>
          <Text color="gray.600" fontSize={{ base: 'sm', sm: 'lg' }}>
            {bilanInfo.thirdSection}
          </Text>
        </Stack>
      </Box>
    </Center>
  );
}
