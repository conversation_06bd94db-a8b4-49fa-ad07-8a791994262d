import {
  VStack,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Select,
  Text,
  Heading,
} from '@chakra-ui/react';

const AppartementDetails = ({ formData, onChange }) => {
  return (
    <VStack spacing={3} align="stretch">
      {/* Fields with 2 options (Radio Buttons) */}
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Y a-t-il des traces d’humidité ?
        </FormLabel>
        <RadioGroup
          name="humidite"
          value={formData.humidite}
          onChange={(value) =>
            onChange('appartementDetails', 'humidite', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence d’une VMC (ventilation mécanique contrôlée) ?
        </FormLabel>
        <RadioGroup
          name="vmc"
          value={formData.vmc}
          onChange={(value) => onChange('appartementDetails', 'vmc', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          L'appartement est-il lumineux ?
        </FormLabel>
        <RadioGroup
          name="luminosite"
          value={formData.luminosite}
          onChange={(value) =>
            onChange('appartementDetails', 'luminosite', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Appartement traversant ?
        </FormLabel>
        <RadioGroup
          name="traversant"
          value={formData.traversant}
          onChange={(value) =>
            onChange('appartementDetails', 'traversant', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          La place perdue par rapport à la surface annoncée est-elle importante
          (couloirs, dégagements)
        </FormLabel>
        <RadioGroup
          name="placePerdue"
          value={formData.placePerdue}
          onChange={(value) =>
            onChange('appartementDetails', 'placePerdue', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          L’appartement est-il facilement transformable ? (Analyser les cloisons
          et les murs porteurs dans l’appartement)
        </FormLabel>
        <RadioGroup
          name="transformable"
          value={formData.transformable}
          onChange={(value) =>
            onChange('appartementDetails', 'transformable', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          W.C. isolés ou dans la salle de bain ?
        </FormLabel>
        <RadioGroup
          name="wc"
          value={formData.wc}
          onChange={(value) => onChange('appartementDetails', 'wc', value)}
        >
          <Stack direction="row">
            <Radio value="ISOLE" colorScheme="teal" size="md">
              <Text fontSize="md">Isolé</Text>
            </Radio>
            <Radio value="DANS_SALLE_BAIN" colorScheme="teal" size="md">
              <Text fontSize="md">Dans la salle de bain</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence de meubles ?
        </FormLabel>
        <RadioGroup
          name="meubles"
          value={formData.meubles}
          onChange={(value) => onChange('appartementDetails', 'meubles', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence d’une cuisine équipée ?
        </FormLabel>
        <RadioGroup
          name="cuisineEquipee"
          value={formData.cuisineEquipee}
          onChange={(value) =>
            onChange('appartementDetails', 'cuisineEquipee', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Dispose-t-elle d’une arrivée d’eau dédiée à un lave-vaisselle ?
        </FormLabel>
        <RadioGroup
          name="arriveeLaveVaisselle"
          value={formData.arriveeLaveVaisselle}
          onChange={(value) =>
            onChange('appartementDetails', 'arriveeLaveVaisselle', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Y a-t-il des lampes au plafond ?
        </FormLabel>
        <RadioGroup
          name="lampesPlafond"
          value={formData.lampesPlafond}
          onChange={(value) =>
            onChange('appartementDetails', 'lampesPlafond', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          S’agit-il de lampadaires ?
        </FormLabel>
        <RadioGroup
          name="lampadaires"
          value={formData.lampadaires}
          onChange={(value) =>
            onChange('appartementDetails', 'lampadaires', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {/* Fields with Satisfaisant/Insuffisant (now Dropdowns with Moyen) */}
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Hauteur sous plafond (est-elle bien suffisante ?)
        </FormLabel>
        <Select
          name="hauteurPlafond"
          value={formData.hauteurPlafond}
          onChange={(e) =>
            onChange('appartementDetails', 'hauteurPlafond', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SATISFAISANT">Satisfaisant</option>
          <option value="MOYEN">Moyen</option>
          <option value="INSUFFISANT">Insuffisant</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Planéité du sol
        </FormLabel>
        <Select
          name="planeteSol"
          value={formData.planeteSol}
          onChange={(e) =>
            onChange('appartementDetails', 'planeteSol', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SATISFAISANT">Satisfaisant</option>
          <option value="MOYEN">Moyen</option>
          <option value="INSUFFISANT">Insuffisant</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Insonorisation de l’appartement (par rapport à la rue et aux autres
          appartements dans l’immeuble)
        </FormLabel>
        <Select
          name="insonorisation"
          value={formData.insonorisation}
          onChange={(e) =>
            onChange('appartementDetails', 'insonorisation', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SATISFAISANT">Satisfaisant</option>
          <option value="MOYEN">Moyen</option>
          <option value="INSUFFISANT">Insuffisant</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Isolation de l'appartement
        </FormLabel>
        <Select
          name="isolationAppartement"
          value={formData.isolationAppartement}
          onChange={(e) =>
            onChange(
              'appartementDetails',
              'isolationAppartement',
              e.target.value
            )
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SATISFAISANT">Satisfaisant</option>
          <option value="MOYEN">Moyen</option>
          <option value="INSUFFISANT">Insuffisant</option>
        </Select>
      </FormControl>

      {/* Fields with more than 2 options (Dropdowns) */}
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Fenêtres (simple, double ou triple vitrages)
        </FormLabel>
        <Select
          name="fenetres"
          value={formData.fenetres}
          onChange={(e) =>
            onChange('appartementDetails', 'fenetres', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SIMPLE">Simple</option>
          <option value="DOUBLE">Double</option>
          <option value="TRIPLE">Triple</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Si oui, dans quelles pièces ? (VMC)
        </FormLabel>
        <Select
          name="vmcPieces"
          value={formData.vmcPieces}
          onChange={(e) =>
            onChange('appartementDetails', 'vmcPieces', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
          isDisabled={formData.vmc !== 'Oui'}
        >
          <option value="Cuisine">Cuisine</option>
          <option value="Salle de bain">Salle de bain</option>
          <option value="Salon">Salon</option>
          <option value="Chambres">Chambres</option>
          <option value="Autres">Autres</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Orientation ?
        </FormLabel>
        <Select
          name="orientation"
          value={formData.orientation}
          onChange={(e) =>
            onChange('appartementDetails', 'orientation', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="FAVORABLE, SUD-OUEST">Favorable, Sud-Ouest</option>
          <option value="NORD">Nord</option>
          <option value="EST">Est</option>
          <option value="OUEST">Ouest</option>
          <option value="SUD">Sud</option>
          <option value="AUTRE">Autre</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          L’appartement donne-t-il sur rue ou sur cour/jardin ?
        </FormLabel>
        <Select
          name="vue"
          value={formData.vue}
          onChange={(e) =>
            onChange('appartementDetails', 'vue', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="RUE">Rue</option>
          <option value="COUR/JARDIN">Cour/Jardin</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Qu’en est-il de la ou des pièce(s) de nuit ?
        </FormLabel>
        <Select
          name="pieceNuit"
          value={formData.pieceNuit}
          onChange={(e) =>
            onChange('appartementDetails', 'pieceNuit', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="JARDIN">Jardin</option>
          <option value="RUE">Rue</option>
          <option value="AUTRE">Autre</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Type de chauffage ?
        </FormLabel>
        <Select
          name="chauffage"
          value={formData.chauffage}
          onChange={(e) =>
            onChange('appartementDetails', 'chauffage', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="GAZ">Gaz</option>
          <option value="ELECTRIQUE">Électrique</option>
          <option value="AUTRE">Autre</option>
        </Select>
      </FormControl>

      {formData.chauffage === 'GAZ' && (
        <>
          <FormControl>
            <FormLabel fontWeight="medium" fontSize="md">
              Si gaz, ancienneté de la chaudière ?
            </FormLabel>
            <Select
              name="chaudiereAnciennete"
              value={formData.chaudiereAnciennete}
              onChange={(e) =>
                onChange(
                  'appartementDetails',
                  'chaudiereAnciennete',
                  e.target.value
                )
              }
              size="md"
              focusBorderColor="teal.400"
            >
              <option value="< 5 ans">Moins de 5 ans</option>
              <option value="> 5 ans">Plus de 5 ans</option>
            </Select>
          </FormControl>

          <FormControl>
            <FormLabel fontWeight="medium" fontSize="md">
              A-t-elle été entretenue chaque année ?
            </FormLabel>
            <RadioGroup
              name="chaudiereEntretien"
              value={formData.chaudiereEntretien}
              onChange={(value) =>
                onChange('appartementDetails', 'chaudiereEntretien', value)
              }
            >
              <Stack direction="row">
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">Oui</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">Non</Text>
                </Radio>
              </Stack>
            </RadioGroup>
          </FormControl>
        </>
      )}

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Si ballon d'eau chaude, ancienneté ?
        </FormLabel>
        <Select
          name="ballonEauChaudeAnciennete"
          value={formData.ballonEauChaudeAnciennete}
          onChange={(e) =>
            onChange(
              'appartementDetails',
              'ballonEauChaudeAnciennete',
              e.target.value
            )
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="< 5 ans">Moins de 5 ans</option>
          <option value="> 5 ans">Plus de 5 ans</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          A-t-il été entretenu chaque année ?
        </FormLabel>
        <RadioGroup
          name="ballonEauChaudeEntretien"
          value={formData.ballonEauChaudeEntretien}
          onChange={(value) =>
            onChange('appartementDetails', 'ballonEauChaudeEntretien', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Si oui, sont-ils en bon état ?
        </FormLabel>
        <Select
          name="etatMeubles"
          value={formData.etatMeubles}
          onChange={(e) =>
            onChange('appartementDetails', 'etatMeubles', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
          isDisabled={formData.meubles !== 'Oui'}
        >
          <option value="PLUS OU MOINS">Plus ou moins</option>
          <option value="BON">Bon</option>
          <option value="MAUVAIS">Mauvais</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Si oui, est-elle bien fonctionnelle ?
        </FormLabel>
        <Select
          name="etatCuisine"
          value={formData.etatCuisine}
          onChange={(e) =>
            onChange('appartementDetails', 'etatCuisine', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
          isDisabled={formData.cuisineEquipee !== 'Oui'}
        >
          <option value="PLUS OU MOINS">Plus ou moins</option>
          <option value="BON">Bon</option>
          <option value="MAUVAIS">Mauvais</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Y a-t-il assez de rangements ?
        </FormLabel>
        <Select
          name="rangements"
          value={formData.rangements}
          onChange={(e) =>
            onChange('appartementDetails', 'rangements', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="PLUS OU MOINS">Plus ou moins</option>
          <option value="Oui">Oui</option>
          <option value="Non">Non</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          S’agit-il d’une cuisine séparée ou faisant partie d’une autre pièce ?
        </FormLabel>
        <Select
          name="cuisineType"
          value={formData.cuisineType}
          onChange={(e) =>
            onChange('appartementDetails', 'cuisineType', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="SEMI-OUVERTE">Semi-ouverte</option>
          <option value="SEPAREE">Séparée</option>
          <option value="INTEGREE">Intégrée</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Où est prévue l’arrivée d’eau liée à la machine à laver ?
        </FormLabel>
        <Select
          name="arriveeLaveLinge"
          value={formData.arriveeLaveLinge}
          onChange={(e) =>
            onChange('appartementDetails', 'arriveeLaveLinge', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="CUISINE">Cuisine</option>
          <option value="SALLE_BAIN">Salle de bain</option>
          <option value="AUTRE">Autre</option>
        </Select>
      </FormControl>

      {/* Liste des travaux à envisager (Title Only) */}
      <Heading size="sm" color="teal.700" mt={4} mb={2}>
        Liste des travaux à envisager :
      </Heading>

      {/* Dropdowns for Travaux Fields */}
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Électricité en normes ou nécessité de le remettre en norme ?
        </FormLabel>
        <Select
          name="travauxElectricite"
          value={formData.travauxElectricite}
          onChange={(e) =>
            onChange('appartementDetails', 'travauxElectricite', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="Oui">Oui (en normes)</option>
          <option value="Non">Non (à remettre en norme)</option>
          <option value="MAUVAIS">Mauvais (travaux nécessaires)</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Salle de bain moderne (Meuble sous Vasque) ou à adapter ? État de la
          robinetterie ?
        </FormLabel>
        <Select
          name="travauxSalleBain"
          value={formData.travauxSalleBain}
          onChange={(e) =>
            onChange('appartementDetails', 'travauxSalleBain', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="MODERNE">Moderne (pas de travaux)</option>
          <option value="A_ADAPTER">À adapter (travaux nécessaires)</option>
          <option value="MAUVAIS">Mauvais (travaux majeurs nécessaires)</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État des sols. À refaire ?
        </FormLabel>
        <Select
          name="travauxSols"
          value={formData.travauxSols}
          onChange={(e) =>
            onChange('appartementDetails', 'travauxSols', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="BON">Bon (pas de travaux)</option>
          <option value="A_REFAIRE">À refaire (travaux nécessaires)</option>
          <option value="MAUVAIS">Mauvais (travaux majeurs nécessaires)</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État des murs. À repeindre ?
        </FormLabel>
        <Select
          name="travauxMurs"
          value={formData.travauxMurs}
          onChange={(e) =>
            onChange('appartementDetails', 'travauxMurs', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="BON">Bon (pas de travaux)</option>
          <option value="A_REPEINDRE">À repeindre (travaux nécessaires)</option>
          <option value="MAUVAIS">Mauvais (travaux majeurs nécessaires)</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État des lampes
        </FormLabel>
        <Select
          name="travauxLampes"
          value={formData.travauxLampes}
          onChange={(e) =>
            onChange('appartementDetails', 'travauxLampes', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="BON">Bon (pas de travaux)</option>
          <option value="A_REPARER">À réparer (travaux nécessaires)</option>
          <option value="MAUVAIS">Mauvais (travaux majeurs nécessaires)</option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État du plafond
        </FormLabel>
        <Select
          name="etatPlafond"
          value={formData.etatPlafond}
          onChange={(e) =>
            onChange('appartementDetails', 'etatPlafond', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        >
          <option value="MOYEN">Moyen</option>
          <option value="BON">Bon</option>
          <option value="MAUVAIS">Mauvais</option>
        </Select>
      </FormControl>
    </VStack>
  );
};

export default AppartementDetails;
