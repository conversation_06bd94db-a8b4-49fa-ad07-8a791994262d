import {
  VStack,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Text,
} from '@chakra-ui/react';

const DocumentsForm = ({ formData, onChange }) => {
  return (
    <VStack spacing={3} align="stretch">
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Extrait cadastral
        </FormLabel>
        <RadioGroup
          name="extraitCadastral"
          value={formData.extraitCadastral}
          onChange={(value) => onChange('documents', 'extraitCadastral', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Relevé de propriété
        </FormLabel>
        <RadioGroup
          name="relevePropriete"
          value={formData.relevePropriete}
          onChange={(value) => onChange('documents', 'relevePropriete', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Décompte des charges annuelles
        </FormLabel>
        <RadioGroup
          name="chargesAnnuelles"
          value={formData.chargesAnnuelles}
          onChange={(value) => onChange('documents', 'chargesAnnuelles', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Copie des baux en cours si le bien est vendu loué
        </FormLabel>
        <RadioGroup
          name="bauxEnCours"
          value={formData.bauxEnCours}
          onChange={(value) => onChange('documents', 'bauxEnCours', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Dernier avis de taxe foncière
        </FormLabel>
        <RadioGroup
          name="taxeFonciere"
          value={formData.taxeFonciere}
          onChange={(value) => onChange('documents', 'taxeFonciere', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Diagnostic de performance énergétique
        </FormLabel>
        <RadioGroup
          name="performanceEnergetique"
          value={formData.performanceEnergetique}
          onChange={(value) =>
            onChange('documents', 'performanceEnergetique', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Diagnostic à propos de la présence de plomb/amiante ?
        </FormLabel>
        <RadioGroup
          name="plombAmiante"
          value={formData.plombAmiante}
          onChange={(value) => onChange('documents', 'plombAmiante', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>
    </VStack>
  );
};

export default DocumentsForm;
