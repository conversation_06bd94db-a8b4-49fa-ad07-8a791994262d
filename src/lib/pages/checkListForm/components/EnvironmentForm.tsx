import {
  VStack,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Input,
  Checkbox,
  Text,
} from '@chakra-ui/react';

const EnvironmentForm = ({ formData, onChange }) => {
  const atoutsOptions = [
    'Commerces',
    'Universités',
    'Écoles',
    'Quartier résidentiel',
    'Crêches',
    'Stade',
    'Parcs',
  ];

  return (
    <VStack spacing={3} align="stretch">
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Nuisance
        </FormLabel>
        <RadioGroup
          name="nuisance"
          value={formData.nuisance}
          onChange={(value) => onChange('environment', 'nuisance', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Population
        </FormLabel>
        <Input
          name="population"
          value={formData.population}
          onChange={(e) =>
            onChange('environment', 'population', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        />
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Atouts
        </FormLabel>
        <RadioGroup
          name="atouts"
          value={formData.atouts}
          onChange={(value) => onChange('environment', 'atouts', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {formData.atouts === 'Oui' && (
        <FormControl>
          <FormLabel fontWeight="medium" fontSize="md">
            Principaux atouts (sélectionnez)
          </FormLabel>
          <Stack spacing={2}>
            {atoutsOptions.map((option) => (
              <Checkbox
                key={option}
                isChecked={formData.atoutsList.includes(option)}
                onChange={(e) => {
                  const checked = e.target.checked;
                  onChange(
                    'environment',
                    'atoutsList',
                    checked
                      ? [...formData.atoutsList, option]
                      : formData.atoutsList.filter((item) => item !== option)
                  );
                }}
                colorScheme="teal"
              >
                <Text fontSize="md">{option}</Text>
              </Checkbox>
            ))}
          </Stack>
        </FormControl>
      )}

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Commerces
        </FormLabel>
        <RadioGroup
          name="commerces"
          value={formData.commerces}
          onChange={(value) => onChange('environment', 'commerces', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Universités
        </FormLabel>
        <RadioGroup
          name="universites"
          value={formData.universites}
          onChange={(value) => onChange('environment', 'universites', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Écoles
        </FormLabel>
        <RadioGroup
          name="ecoles"
          value={formData.ecoles}
          onChange={(value) => onChange('environment', 'ecoles', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Quartier résidentiel
        </FormLabel>
        <RadioGroup
          name="quartierResidentiel"
          value={formData.quartierResidentiel}
          onChange={(value) =>
            onChange('environment', 'quartierResidentiel', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Crêches
        </FormLabel>
        <RadioGroup
          name="creches"
          value={formData.creches}
          onChange={(value) => onChange('environment', 'creches', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Stade
        </FormLabel>
        <RadioGroup
          name="stade"
          value={formData.stade}
          onChange={(value) => onChange('environment', 'stade', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Parcs
        </FormLabel>
        <RadioGroup
          name="parcs"
          value={formData.parcs}
          onChange={(value) => onChange('environment', 'parcs', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Facilité ou non de stationnement
        </FormLabel>
        <RadioGroup
          name="parking"
          value={formData.parking}
          onChange={(value) => onChange('environment', 'parking', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Proximité des transports en commun
        </FormLabel>
        <RadioGroup
          name="transports"
          value={formData.transports}
          onChange={(value) => onChange('environment', 'transports', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Loyers pratiqués dans la zone
        </FormLabel>
        <Input
          name="loyerZone"
          value={formData.loyerZone}
          onChange={(e) => onChange('environment', 'loyerZone', e.target.value)}
          size="md"
          focusBorderColor="teal.400"
        />
      </FormControl>
    </VStack>
  );
};

export default EnvironmentForm;
