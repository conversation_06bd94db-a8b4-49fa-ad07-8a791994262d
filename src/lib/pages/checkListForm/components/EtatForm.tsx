import {
  VStack,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Text,
} from '@chakra-ui/react';

const EtatForm = ({ formData, onChange }) => {
  return (
    <VStack spacing={3} align="stretch">
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État de la toiture
        </FormLabel>
        <RadioGroup
          name="toiture"
          value={formData.toiture}
          onChange={(value) => onChange('etat', 'toiture', value)}
        >
          <Stack direction="row">
            <Radio value="BON" colorScheme="teal" size="md">
              <Text fontSize="md">Bon</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md"><PERSON>uva<PERSON></Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État des parties communes
        </FormLabel>
        <RadioGroup
          name="partiesCommunes"
          value={formData.partiesCommunes}
          onChange={(value) => onChange('etat', 'partiesCommunes', value)}
        >
          <Stack direction="row">
            <Radio value="BON" colorScheme="teal" size="md">
              <Text fontSize="md">Bon</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md">Mauvais</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État de la façade
        </FormLabel>
        <RadioGroup
          name="facade"
          value={formData.facade}
          onChange={(value) => onChange('etat', 'facade', value)}
        >
          <Stack direction="row">
            <Radio value="BON" colorScheme="teal" size="md">
              <Text fontSize="md">Bon</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md">Mauvais</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État du parking
        </FormLabel>
        <RadioGroup
          name="parking"
          value={formData.parking}
          onChange={(value) => onChange('etat', 'parking', value)}
        >
          <Stack direction="row">
            <Radio value="BON" colorScheme="teal" size="md">
              <Text fontSize="md">Bon</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md">Mauvais</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          État des équipements communs
        </FormLabel>
        <RadioGroup
          name="equipementsCommuns"
          value={formData.equipementsCommuns}
          onChange={(value) => onChange('etat', 'equipementsCommuns', value)}
        >
          <Stack direction="row">
            <Radio value="BON" colorScheme="teal" size="md">
              <Text fontSize="md">Bon</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md">Mauvais</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Ravalement ?
        </FormLabel>
        <RadioGroup
          name="ravalement"
          value={formData.ravalement}
          onChange={(value) => onChange('etat', 'ravalement', value)}
        >
          <Stack direction="row">
            <Radio value="RECENT" colorScheme="teal" size="md">
              <Text fontSize="md">Récent</Text>
            </Radio>
            <Radio value="ANCIEN" colorScheme="teal" size="md">
              <Text fontSize="md">Ancien</Text>
            </Radio>
            <Radio value="AUCUN" colorScheme="teal" size="md">
              <Text fontSize="md">Aucun</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Isolation Thermique extérieure ?
        </FormLabel>
        <RadioGroup
          name="isolationThermique"
          value={formData.isolationThermique}
          onChange={(value) => onChange('etat', 'isolationThermique', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>
    </VStack>
  );
};

export default EtatForm;
