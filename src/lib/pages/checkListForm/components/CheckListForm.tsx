import { useState } from 'react';
import {
  Box,
  Heading,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import EnvironmentForm from './EnvironmentForm';
import GeneralInfo from './GeneralInfo';
import EtatForm from './EtatForm';
import DocumentsForm from './DocumentsForm';
import AppartementDetails from './AppartementDetails';
import QualityAppartement from './QualityAppartement';

function CheckListForm() {
  const [formData, setFormData] = useState({
    environment: {
      nuisance: 'Oui',
      population: '',
      atouts: 'Oui',
      atoutsList: [],
      commerces: 'Oui',
      universites: 'Oui',
      ecoles: 'Oui',
      quartierResidentiel: 'Oui',
      creches: 'Oui',
      stade: 'Oui',
      parcs: 'Oui',
      parking: 'Oui',
      transports: 'Oui',
      loyerZone: '',
    },
    generalInfo: {
      anneeConstruction: '',
      lotsCopropriete: 'Oui',
      information: 'Oui',
      syndic: 'Oui',
      fibre: 'Oui',
      populationImmeuble: 'Oui',
      ascenseur: 'Oui',
      concierge: 'Oui',
      securite: 'Oui',
      securiteDetails: '',
      saas: 'Oui',
      courPrivative: 'Oui',
      videosurveillance: 'Oui',
      partiesCommunes: 'Oui',
      accessibilite: 'MOYEN',
      espaceAppartements: 'Oui',
    },
    etat: {
      toiture: 'BON',
      partiesCommunes: 'BON',
      facade: 'BON',
      parking: 'BON',
      equipementsCommuns: 'BON',
      ravalement: 'RECENT',
      isolationThermique: 'Oui',
    },
    documents: {
      extraitCadastral: 'Oui',
      relevePropriete: 'Oui',
      chargesAnnuelles: 'Oui',
      bauxEnCours: 'Oui',
      taxeFonciere: 'Oui',
      performanceEnergetique: 'Oui',
      plombAmiante: 'Oui',
    },
    appartementDetails: {
      hauteurPlafond: 'SATISFAISANT',
      planeteSol: 'SATISFAISANT',
      insonorisation: 'SATISFAISANT',
      isolationAppartement: 'SATISFAISANT',
      fenetres: 'SIMPLE',
      humidite: 'Non',
      vmc: 'Oui',
      vmcPieces: 'Cuisine',
      luminosite: 'Oui',
      orientation: 'FAVORABLE, SUD-OUEST',
      traversant: 'Oui',
      vue: 'RUE',
      pieceNuit: 'JARDIN',
      chauffage: 'GAZ',
      chaudiereAnciennete: '< 5 ans',
      chaudiereEntretien: 'Oui',
      ballonEauChaudeAnciennete: '< 5 ans',
      ballonEauChaudeEntretien: 'Oui',
      placePerdue: 'Non',
      transformable: 'Oui',
      wc: 'ISOLE',
      meubles: 'Oui',
      etatMeubles: 'PLUS OU MOINS',
      cuisineEquipee: 'Oui',
      etatCuisine: 'PLUS OU MOINS',
      arriveeLaveVaisselle: 'Oui',
      rangements: 'PLUS OU MOINS',
      cuisineType: 'SEMI-OUVERTE',
      arriveeLaveLinge: 'CUISINE',
      travauxElectricite: '',
      travauxSalleBain: '',
      travauxSols: '',
      travauxMurs: '',
      travauxLampes: '',
      lampesPlafond: 'Non',
      lampadaires: 'Oui',
      etatPlafond: 'MOYEN',
    },
    finalDetails: {
      elementDifferenciant: 'Oui',
      bonus: 'Oui',
      etatGeneral: 'BON ETAT',
      neuf: 'Oui',
    },
  });

  const handleChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const bgColor = useColorModeValue('gray.50', 'gray.800');
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      p={{ base: 4, md: 6 }}
      maxW={{ base: '100%', md: '800px' }}
      mx="auto"
      bg={bgColor}
      minH="100vh"
    >
      <VStack
        spacing={4}
        p={{ base: 4, md: 6 }}
        bg={cardBg}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="md"
        boxShadow="md"
        align="stretch"
      >
        <Heading size="md" textAlign="center" color="teal.600" fontSize="xl">
          FICHE D'ÉVALUATION DU BIEN IMMOBILIER
        </Heading>

        <Tabs variant="enclosed" colorScheme="teal">
          <TabList>
            <Tab>Environnement</Tab>
            <Tab>Informations Générales</Tab>
            <Tab>État</Tab>
            <Tab>Documents</Tab>
            <Tab>Détails Appartement</Tab>
            <Tab>Qualité Appartement</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <EnvironmentForm
                formData={formData.environment}
                onChange={handleChange}
              />
            </TabPanel>
            <TabPanel>
              <GeneralInfo
                formData={formData.generalInfo}
                onChange={handleChange}
              />
            </TabPanel>
            <TabPanel>
              <EtatForm formData={formData.etat} onChange={handleChange} />
            </TabPanel>
            <TabPanel>
              <DocumentsForm
                formData={formData.documents}
                onChange={handleChange}
              />
            </TabPanel>
            <TabPanel>
              <AppartementDetails
                formData={formData.appartementDetails}
                onChange={handleChange}
              />
            </TabPanel>
            <TabPanel>
              <QualityAppartement
                formData={formData.finalDetails}
                onChange={handleChange}
              />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Box>
  );
}

export default CheckListForm;
