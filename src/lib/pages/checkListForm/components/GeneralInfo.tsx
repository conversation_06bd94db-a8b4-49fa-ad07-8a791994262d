import {
  VStack,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Input,
  Text,
} from '@chakra-ui/react';

const GeneralInfo = ({ formData, onChange }) => {
  return (
    <VStack spacing={3} align="stretch">
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Année de construction
        </FormLabel>
        <Input
          name="anneeConstruction"
          value={formData.anneeConstruction}
          onChange={(e) =>
            onChange('generalInfo', 'anneeConstruction', e.target.value)
          }
          size="md"
          focusBorderColor="teal.400"
        />
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Informations récupérées ?
        </FormLabel>
        <RadioGroup
          name="information"
          value={formData.information}
          onChange={(value) => onChange('generalInfo', 'information', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Nombre de lots dans la copropriété
        </FormLabel>
        <RadioGroup
          name="lotsCopropriete"
          value={formData.lotsCopropriete}
          onChange={(value) =>
            onChange('generalInfo', 'lotsCopropriete', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Syndic bénévole ou professionnel
        </FormLabel>
        <RadioGroup
          name="syndic"
          value={formData.syndic}
          onChange={(value) => onChange('generalInfo', 'syndic', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Raccordé à la fibre ou non
        </FormLabel>
        <RadioGroup
          name="fibre"
          value={formData.fibre}
          onChange={(value) => onChange('generalInfo', 'fibre', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Population présente dans l'immeuble
        </FormLabel>
        <RadioGroup
          name="populationImmeuble"
          value={formData.populationImmeuble}
          onChange={(value) =>
            onChange('generalInfo', 'populationImmeuble', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Ascenseur
        </FormLabel>
        <RadioGroup
          name="ascenseur"
          value={formData.ascenseur}
          onChange={(value) => onChange('generalInfo', 'ascenseur', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence de concierge
        </FormLabel>
        <RadioGroup
          name="concierge"
          value={formData.concierge}
          onChange={(value) => onChange('generalInfo', 'concierge', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Immeuble sécurisé
        </FormLabel>
        <RadioGroup
          name="securite"
          value={formData.securite}
          onChange={(value) => onChange('generalInfo', 'securite', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      {formData.securite === 'Oui' && (
        <FormControl>
          <FormLabel fontWeight="medium" fontSize="md">
            Quelles sont les critères
          </FormLabel>
          <Input
            name="securiteDetails"
            value={formData.securiteDetails}
            onChange={(e) =>
              onChange('generalInfo', 'securiteDetails', e.target.value)
            }
            size="md"
            focusBorderColor="teal.400"
          />
        </FormControl>
      )}

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence d'un SAAS
        </FormLabel>
        <RadioGroup
          name="saas"
          value={formData.saas}
          onChange={(value) => onChange('generalInfo', 'saas', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence d'une cour privative
        </FormLabel>
        <RadioGroup
          name="courPrivative"
          value={formData.courPrivative}
          onChange={(value) => onChange('generalInfo', 'courPrivative', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Présence d'un système de vidéosurveillance
        </FormLabel>
        <RadioGroup
          name="videosurveillance"
          value={formData.videosurveillance}
          onChange={(value) =>
            onChange('generalInfo', 'videosurveillance', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Parties communes spacieuses
        </FormLabel>
        <RadioGroup
          name="partiesCommunes"
          value={formData.partiesCommunes}
          onChange={(value) =>
            onChange('generalInfo', 'partiesCommunes', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Accessibilité aux appartements
        </FormLabel>
        <RadioGroup
          name="accessibilite"
          value={formData.accessibilite}
          onChange={(value) => onChange('generalInfo', 'accessibilite', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Espace respectée entre les appartements
        </FormLabel>
        <RadioGroup
          name="espaceAppartements"
          value={formData.espaceAppartements}
          onChange={(value) =>
            onChange('generalInfo', 'espaceAppartements', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>
    </VStack>
  );
};

export default GeneralInfo;
