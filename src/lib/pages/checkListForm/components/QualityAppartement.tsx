import {
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  Text,
  VStack,
} from '@chakra-ui/react';

const QualityAppartement = ({ formData, onChange }) => {
  return (
    <VStack spacing={3} align="stretch">
      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          L’appartement a-t-il un élément différenciant (jardin privatif sur
          cour, etc…) ?
        </FormLabel>
        <RadioGroup
          name="elementDifferenciant"
          value={formData.elementDifferenciant}
          onChange={(value) =>
            onChange('finalDetails', 'elementDifferenciant', value)
          }
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Y a-t-il des bonus ? (Cave, Parking, Local vélo…)
        </FormLabel>
        <RadioGroup
          name="bonus"
          value={formData.bonus}
          onChange={(value) => onChange('finalDetails', 'bonus', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Est-il en très bon état ?
        </FormLabel>
        <RadioGroup
          name="etatGeneral"
          value={formData.etatGeneral}
          onChange={(value) => onChange('finalDetails', 'etatGeneral', value)}
        >
          <Stack direction="row">
            <Radio value="BON ETAT" colorScheme="teal" size="md">
              <Text fontSize="md">Bon état</Text>
            </Radio>
            <Radio value="MOYEN" colorScheme="teal" size="md">
              <Text fontSize="md">Moyen</Text>
            </Radio>
            <Radio value="MAUVAIS" colorScheme="teal" size="md">
              <Text fontSize="md">Mauvais</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel fontWeight="medium" fontSize="md">
          Est-il refait à neuf ?
        </FormLabel>
        <RadioGroup
          name="neuf"
          value={formData.neuf}
          onChange={(value) => onChange('finalDetails', 'neuf', value)}
        >
          <Stack direction="row">
            <Radio value="Oui" colorScheme="teal" size="md">
              <Text fontSize="md">Oui</Text>
            </Radio>
            <Radio value="Non" colorScheme="teal" size="md">
              <Text fontSize="md">Non</Text>
            </Radio>
          </Stack>
        </RadioGroup>
      </FormControl>
    </VStack>
  );
};

export default QualityAppartement;
