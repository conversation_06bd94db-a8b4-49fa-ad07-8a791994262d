import { useState, useEffect } from 'react';

export interface InvestmentInputs {
  // Investissement
  montantInvestissement: number;
  dontTerrain: number;
  dontConstruction: number;
  coutAcquisition: number;
  travaux: number;

  // Fiscalité
  revenuFiscalReference: number;
  tauxMarginalImposition: number;
  nombreParts: number;

  // Loyers
  loyersAnnuelHorsCharges: number;
  chargesComprises: number;

  // Charges fixes
  chargesLocatives: number;
  assurancesNonOccupant: number;
  assurancesEmprunteur: number;
  chargesEntretien: number;
  taxesFoncieres: number;
  fraisAgences: number;
  fraisEmprunts: number;
  CFE: number;
  fraisBancaires: number;
  interetsEmprunt: number;
  travauxDeductibles: number;

  // Amortissements
  quotePartTerrain: number;
  quotePartGrosOeuvre: number;
  quotePartFacade: number;
  quotePartIGT: number;
  quotePartAgencements: number;
  valeurMeubles: number;
  dureeGrosOeuvre: number;
  dureeFacade: number;
  dureeIGT: number;
  dureeAgencements: number;
  dureeMeubles: number;

  // Date
  dateAcquisition: string;
  dureeDetention: number;
  regime?: string;

  // Honoraires
  fraisComptables?: number;
  fraisJuridiques?: number;
  fraisGerant?: number;
}

interface AmortissementItem {
  brut: number;
  amortissement: number;
  cumul: number;
  vnc: number;
}

interface AmortissementYear {
  annee: number;
  dateCloture: string;
  prorata: number;
  grosOeuvre: AmortissementItem;
  facade: AmortissementItem;
  igt: AmortissementItem;
  agencements: AmortissementItem;
  meubles: AmortissementItem;
  total: AmortissementItem;
}

interface InvestmentResults {
  montantTotalInvestissement: number;
  valeurTerrain: number;
  valeurConstruction: number;
  valeurGrosOeuvre: number;
  valeurFacade: number;
  valeurIGT: number;
  valeurAgencements: number;
  amortissementAnnuelGrosOeuvre: number;
  amortissementAnnuelFacade: number;
  amortissementAnnuelIGT: number;
  amortissementAnnuelAgencements: number;
  amortissementAnnuelMeubles: number;
  amortissementTotalAnnuel: number;
  loyersAnnuelTotal: number;
  chargesAnnuelTotal: number;
  chargesDeductiblesTotal: number;
  revenuImposable: number;
  impots: number;
  prelevementsSociaux: number;
  deductionCSG: number;
  revenuNet: number;
  amortissementTable: AmortissementYear[];
  plusValue: number;
  tresorerieCumulee: number;
}

const useInvestmentCalculator = () => {
  // State for all user inputs
  const [inputs, setInputs] = useState<InvestmentInputs>({
    // Investissement
    montantInvestissement: 0,
    dontTerrain: 0.15,
    dontConstruction: 0.85,
    coutAcquisition: 0,
    travaux: 0,

    // Fiscalité
    revenuFiscalReference: 0,
    tauxMarginalImposition: 0,
    nombreParts: 1,

    // Loyers
    loyersAnnuelHorsCharges: 0,
    chargesComprises: 0,

    // Charges fixes
    chargesLocatives: 0,
    assurancesNonOccupant: 0,
    assurancesEmprunteur: 0,
    chargesEntretien: 0,
    taxesFoncieres: 0,
    fraisAgences: 0,
    fraisEmprunts: 0,
    CFE: 400,
    fraisBancaires: 0,
    interetsEmprunt: 0,
    travauxDeductibles: 0,

    // Amortissements
    quotePartTerrain: 0.15,
    quotePartGrosOeuvre: 0.4,
    quotePartFacade: 0.05,
    quotePartIGT: 0.2,
    quotePartAgencements: 0.2,
    valeurMeubles: 0,
    dureeGrosOeuvre: 50,
    dureeFacade: 20,
    dureeIGT: 15,
    dureeAgencements: 8,
    dureeMeubles: 5,

    // Date
    dateAcquisition: '2020-12-31',
    dureeDetention: 20,
    regime: 'reel', // Default to réel regime

    // Honoraires
    fraisComptables: 0,
    fraisJuridiques: 0,
    fraisGerant: 0,
  });

  // Results state
  const [results, setResults] = useState<InvestmentResults>({
    montantTotalInvestissement: 0,
    valeurTerrain: 0,
    valeurConstruction: 0,
    valeurGrosOeuvre: 0,
    valeurFacade: 0,
    valeurIGT: 0,
    valeurAgencements: 0,
    amortissementAnnuelGrosOeuvre: 0,
    amortissementAnnuelFacade: 0,
    amortissementAnnuelIGT: 0,
    amortissementAnnuelAgencements: 0,
    amortissementAnnuelMeubles: 0,
    amortissementTotalAnnuel: 0,
    loyersAnnuelTotal: 0,
    chargesAnnuelTotal: 0,
    chargesDeductiblesTotal: 0,
    revenuImposable: 0,
    impots: 0,
    prelevementsSociaux: 0,
    deductionCSG: 0,
    revenuNet: 0,
    amortissementTable: [],
    plusValue: 0,
    tresorerieCumulee: 0,
  });

  // Handle input changes
  const handleInputChange = <K extends keyof InvestmentInputs>(
    name: K,
    value: InvestmentInputs[K]
  ) => {
    setInputs((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Calculate all results
  useEffect(() => {
    calculateResults();
  }, [inputs]);

  const calculateResults = () => {
    // 1. Calcul des valeurs d'investissement
    const montantTotalInvestissement = inputs.montantInvestissement;
    const valeurTerrain = montantTotalInvestissement * inputs.quotePartTerrain;
    const valeurConstruction = montantTotalInvestissement - valeurTerrain;

    // 2. Calcul des valeurs amortissables
    const valeurGrosOeuvre =
      montantTotalInvestissement * inputs.quotePartGrosOeuvre;
    const valeurFacade = montantTotalInvestissement * inputs.quotePartFacade;
    const valeurIGT = montantTotalInvestissement * inputs.quotePartIGT;
    const valeurAgencements =
      montantTotalInvestissement * inputs.quotePartAgencements;

    // 3. Calcul des amortissements annuels
    const amortissementAnnuelGrosOeuvre =
      valeurGrosOeuvre / inputs.dureeGrosOeuvre;
    const amortissementAnnuelFacade = valeurFacade / inputs.dureeFacade;
    const amortissementAnnuelIGT = valeurIGT / inputs.dureeIGT;
    const amortissementAnnuelAgencements =
      valeurAgencements / inputs.dureeAgencements;
    const amortissementAnnuelMeubles =
      inputs.valeurMeubles / inputs.dureeMeubles;

    const amortissementTotalAnnuel =
      amortissementAnnuelGrosOeuvre +
      amortissementAnnuelFacade +
      amortissementAnnuelIGT +
      amortissementAnnuelAgencements +
      amortissementAnnuelMeubles;

    // 4. Calcul des revenus locatifs
    const loyersAnnuelTotal = inputs.loyersAnnuelHorsCharges;
    const chargesAnnuelTotal = inputs.chargesComprises;

    // 5. Calcul des charges déductibles
    const chargesDeductiblesTotal =
      inputs.chargesLocatives +
      inputs.assurancesNonOccupant +
      inputs.assurancesEmprunteur +
      inputs.chargesEntretien +
      inputs.taxesFoncieres +
      inputs.fraisAgences +
      inputs.fraisEmprunts +
      inputs.CFE +
      inputs.fraisBancaires +
      inputs.interetsEmprunt +
      inputs.travauxDeductibles;

    // 6. Calcul du revenu imposable
    let revenuImposable: number;
    if (inputs.regime === 'micro') {
      revenuImposable = loyersAnnuelTotal * 0.7; // Abattement 30%
    } else {
      revenuImposable =
        loyersAnnuelTotal - chargesDeductiblesTotal - amortissementTotalAnnuel;
    }

    // 7. Calcul des impôts
    let impots = 0;
    if (revenuImposable > 0) {
      impots = revenuImposable * inputs.tauxMarginalImposition;
    }

    // 8. Calcul des prélèvements sociaux
    const prelevementsSociaux =
      revenuImposable > 0 ? revenuImposable * 0.172 : 0;
    const deductionCSG = revenuImposable > 0 ? revenuImposable * -0.068 : 0;

    // 9. Revenu net
    const revenuNet =
      loyersAnnuelTotal -
      chargesDeductiblesTotal -
      impots -
      prelevementsSociaux +
      deductionCSG;

    // 10. Génération du tableau d'amortissement
    const amortissementTable = generateAmortizationTable(
      valeurGrosOeuvre,
      valeurFacade,
      valeurIGT,
      valeurAgencements,
      amortissementAnnuelGrosOeuvre,
      amortissementAnnuelFacade,
      amortissementAnnuelIGT,
      amortissementAnnuelAgencements,
      amortissementAnnuelMeubles
    );

    // 11. Calcul des projections long terme
    const plusValue = calculatePlusValue(
      amortissementTable,
      inputs.montantInvestissement
    );
    const tresorerieCumulee = calculateTresorerieCumulee(
      revenuNet,
      inputs.dureeDetention
    );

    // Mise à jour des résultats
    setResults({
      montantTotalInvestissement,
      valeurTerrain,
      valeurConstruction,
      valeurGrosOeuvre,
      valeurFacade,
      valeurIGT,
      valeurAgencements,
      amortissementAnnuelGrosOeuvre,
      amortissementAnnuelFacade,
      amortissementAnnuelIGT,
      amortissementAnnuelAgencements,
      amortissementAnnuelMeubles,
      amortissementTotalAnnuel,
      loyersAnnuelTotal,
      chargesAnnuelTotal,
      chargesDeductiblesTotal,
      revenuImposable,
      impots,
      prelevementsSociaux,
      deductionCSG,
      revenuNet,
      amortissementTable,
      plusValue,
      tresorerieCumulee,
    });
  };

  const generateAmortizationTable = (
    valeurGrosOeuvre: number,
    valeurFacade: number,
    valeurIGT: number,
    valeurAgencements: number,
    amortissementAnnuelGrosOeuvre: number,
    amortissementAnnuelFacade: number,
    amortissementAnnuelIGT: number,
    amortissementAnnuelAgencements: number,
    amortissementAnnuelMeubles: number
  ): AmortissementYear[] => {
    const table: AmortissementYear[] = [];
    const currentYear = new Date().getFullYear();

    for (let i = 0; i < inputs.dureeDetention; i++) {
      const year = currentYear + i;
      const dateCloture = `${year}-12-31`;

      // Calcul du prorata pour la première année
      const prorata =
        i === 0
          ? (new Date(dateCloture).getTime() -
              new Date(inputs.dateAcquisition).getTime()) /
            (365 * 24 * 60 * 60 * 1000)
          : 1;

      table.push({
        annee: i + 1,
        dateCloture,
        prorata,
        grosOeuvre: {
          brut: valeurGrosOeuvre,
          amortissement: amortissementAnnuelGrosOeuvre * prorata,
          cumul:
            i === 0
              ? amortissementAnnuelGrosOeuvre * prorata
              : amortissementAnnuelGrosOeuvre * prorata +
                (table[i - 1]?.grosOeuvre?.cumul || 0),
          vnc:
            valeurGrosOeuvre -
            (amortissementAnnuelGrosOeuvre * prorata +
              (table[i - 1]?.grosOeuvre?.cumul || 0)),
        },
        facade: {
          brut: valeurFacade,
          amortissement: amortissementAnnuelFacade * prorata,
          cumul:
            i === 0
              ? amortissementAnnuelFacade * prorata
              : amortissementAnnuelFacade * prorata +
                (table[i - 1]?.facade?.cumul || 0),
          vnc:
            valeurFacade -
            (amortissementAnnuelFacade * prorata +
              (table[i - 1]?.facade?.cumul || 0)),
        },
        igt: {
          brut: valeurIGT,
          amortissement: amortissementAnnuelIGT * prorata,
          cumul:
            i === 0
              ? amortissementAnnuelIGT * prorata
              : amortissementAnnuelIGT * prorata +
                (table[i - 1]?.igt?.cumul || 0),
          vnc:
            valeurIGT -
            (amortissementAnnuelIGT * prorata +
              (table[i - 1]?.igt?.cumul || 0)),
        },
        agencements: {
          brut: valeurAgencements,
          amortissement: amortissementAnnuelAgencements * prorata,
          cumul:
            i === 0
              ? amortissementAnnuelAgencements * prorata
              : amortissementAnnuelAgencements * prorata +
                (table[i - 1]?.agencements?.cumul || 0),
          vnc:
            valeurAgencements -
            (amortissementAnnuelAgencements * prorata +
              (table[i - 1]?.agencements?.cumul || 0)),
        },
        meubles: {
          brut: inputs.valeurMeubles,
          amortissement: amortissementAnnuelMeubles * prorata,
          cumul:
            i === 0
              ? amortissementAnnuelMeubles * prorata
              : amortissementAnnuelMeubles * prorata +
                (table[i - 1]?.meubles?.cumul || 0),
          vnc:
            inputs.valeurMeubles -
            (amortissementAnnuelMeubles * prorata +
              (table[i - 1]?.meubles?.cumul || 0)),
        },
        total: {
          amortissement:
            (amortissementAnnuelGrosOeuvre +
              amortissementAnnuelFacade +
              amortissementAnnuelIGT +
              amortissementAnnuelAgencements +
              amortissementAnnuelMeubles) *
            prorata,
          brut:
            valeurGrosOeuvre +
            valeurFacade +
            valeurIGT +
            valeurAgencements +
            inputs.valeurMeubles,
          cumul:
            (amortissementAnnuelGrosOeuvre +
              amortissementAnnuelFacade +
              amortissementAnnuelIGT +
              amortissementAnnuelAgencements +
              amortissementAnnuelMeubles) *
              prorata +
            (table[i - 1]?.total?.cumul || 0),
          vnc:
            valeurGrosOeuvre +
            valeurFacade +
            valeurIGT +
            valeurAgencements +
            inputs.valeurMeubles -
            ((amortissementAnnuelGrosOeuvre +
              amortissementAnnuelFacade +
              amortissementAnnuelIGT +
              amortissementAnnuelAgencements +
              amortissementAnnuelMeubles) *
              prorata +
              (table[i - 1]?.total?.cumul || 0)),
        },
      });
    }

    return table;
  };

  const calculatePlusValue = (
    amortissementTable: AmortissementYear[],
    montantInvestissement: number
  ): number => {
    // Calcul de la plus-value à la revente
    const prixRevente = montantInvestissement;
    const prixAcquisition = montantInvestissement;

    // Calcul des amortissements cumulés
    const amortissementsCumules = amortissementTable.reduce((acc, year) => {
      return acc + year.total.amortissement;
    }, 0);

    const valeurNetComptable = prixAcquisition - amortissementsCumules;
    const plusValueComptable = prixRevente - valeurNetComptable;

    return plusValueComptable;
  };

  const calculateTresorerieCumulee = (
    revenuNet: number,
    dureeDetention: number
  ): number => {
    // Calcul de la trésorerie cumulée sur la durée de détention
    return revenuNet * dureeDetention;
  };

  return {
    inputs,
    results,
    handleInputChange,
    setInputs,
  };
};

export default useInvestmentCalculator;
