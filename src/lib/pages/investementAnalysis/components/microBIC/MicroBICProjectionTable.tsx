import type { ProjectionDataItem } from '~/lib/types/micro-bic';
import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency, getValueColor } from '../../utils/formatters';

interface MicroBICProjectionTableProps {
  data: ProjectionDataItem[];
}

const MicroBICProjectionTable = ({ data }: MicroBICProjectionTableProps) => {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Produits',
      accessor: 'produits',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Abattement',
      accessor: 'abattement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Imposable',
      accessor: 'resultatImposable',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt',
      accessor: 'impot',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux',
      accessor: 'prelevements',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CSG Déductible',
      accessor: 'csgDed',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IRPP',
      accessor: 'irpp',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Hors Emprunts',
      accessor: 'tresorerieHorsEmprunts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remboursement Emprunt',
      accessor: 'remboursementEmprunt',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Totale',
      accessor: 'tresorerieTotale',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Cumulée',
      accessor: 'tresorerieCumulee',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Projection sur 30 ans - Régime Micro-BIC"
      description="Ce tableau présente une projection financière sur 30 ans de votre investissement en location meublée sous le régime Micro-BIC."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
      footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
    />
  );
};

export default MicroBICProjectionTable;
