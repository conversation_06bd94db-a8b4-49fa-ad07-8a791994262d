import {
  Box,
  Input,
  Radio,
  RadioGroup,
  Stack,
  useColorModeValue,
  Table,
  Tbody,
  Tr,
  Td,
  useBreakpointValue,
  VStack,
  Tooltip,
  Text,
  Heading,
} from '@chakra-ui/react';
import {
  useState,
  useEffect,
  FC,
  ReactNode,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { motion } from 'framer-motion';
import { IRealEstateForm } from '../../../types/investement-analysis';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';

// Utility function for formatting currency values
const formatCurrency = (value: number | string | null) => {
  if (value === null || value === undefined || value === '') return '';
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '';
  return numValue.toLocaleString('fr-FR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

// Utility function for parsing input values
const parseInputValue = (value: string): string => {
  // Remove non-numeric characters except decimal point
  const cleanedValue = value.replace(/[^0-9.]/g, '');
  // Ensure only one decimal point
  const parts = cleanedValue.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }
  return cleanedValue;
};

// Wrap Box with motion for animations
const MotionBox = motion(Box);

// Define interfaces for form items
interface FormItem {
  label: string;
  value: string | number;
  isEditable?: boolean;
  onChange?: (value: string) => void;
  description?: string;
  customRender?: () => ReactNode;
}

// Props for the FormSection component
interface FormSectionProps {
  title: string;
  data: FormItem[];
}

// Custom FormSection component to replace FinanceTable
const FormSection: FC<FormSectionProps> = ({ title, data }) => {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Box
      mb={6}
      overflowX="auto"
      borderWidth={1}
      borderRadius="lg"
      boxShadow="md"
      bg="white"
      p={0}
      aria-label={`Tableau financier : ${title}`}
      role="region"
    >
      <Heading
        size={{ base: 'md', md: 'lg' }}
        fontWeight="semibold"
        p={4}
        borderBottomWidth={1}
        borderColor="gray.200"
        textAlign="center"
        color="teal.700"
        fontSize={{ base: 'lg', md: 'xl' }}
        bg="gray.50"
      >
        {title}
      </Heading>
      {isMobile ? (
        <VStack spacing={4} align="stretch" p={4}>
          {data.map((item, index) => (
            <Box
              key={index}
              p={4}
              borderWidth={1}
              borderRadius="md"
              boxShadow="sm"
              bg="white"
              aria-label={`${item.label} entrée`}
              role="article"
              transition="all 0.2s"
              _hover={{ boxShadow: 'md' }}
            >
              <Text mb={2} fontWeight="medium" color="gray.700">
                {item.label}
              </Text>
              {item.customRender ? (
                item.customRender()
              ) : item.isEditable ? (
                <Input
                  value={
                    typeof item.value === 'number'
                      ? formatCurrency(item.value)
                      : item.value
                  }
                  onChange={(e) => {
                    const parsedValue = parseInputValue(e.target.value);
                    item.onChange?.(parsedValue);
                  }}
                  onWheel={disableWheelOnInputNumber}
                  size="sm"
                  bg="white"
                  borderColor="gray.300"
                  _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
                  w="150px"
                  aria-label={`${item.label} entrée`}
                  isInvalid={isNaN(parseFloat(item.value.toString()))}
                  errorBorderColor="red.500"
                />
              ) : (
                <Tooltip
                  label={item.label}
                  placement="top"
                  aria-label={`${item.label} infobulle`}
                >
                  <Text fontWeight="semibold" color="gray.800">
                    {item.value}
                  </Text>
                </Tooltip>
              )}
              {item.description && (
                <Text fontSize="xs" color="gray.500" mt={1}>
                  {item.description}
                </Text>
              )}
            </Box>
          ))}
        </VStack>
      ) : (
        <Table
          variant="simple"
          width="100%"
          aria-label={`Tableau financier détaillé : ${title}`}
          size="md"
        >
          <Tbody>
            {data.map((item, index) => (
              <Tr
                key={index}
                _hover={{ bg: 'gray.50', transition: 'background 0.2s' }}
                aria-label={`${item.label} ligne`}
                role="row"
                borderBottomWidth={index === data.length - 1 ? 0 : 1}
                borderColor="gray.200"
              >
                <Td
                  fontSize="sm"
                  color="gray.700"
                  py={3}
                  fontWeight="medium"
                  width="40%"
                >
                  {item.label}
                  {item.description && (
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      {item.description}
                    </Text>
                  )}
                </Td>
                <Td py={3}>
                  {item.customRender ? (
                    item.customRender()
                  ) : item.isEditable ? (
                    <Input
                      value={
                        typeof item.value === 'number'
                          ? formatCurrency(item.value)
                          : item.value
                      }
                      onChange={(e) => {
                        const parsedValue = parseInputValue(e.target.value);
                        item.onChange?.(parsedValue);
                      }}
                      onWheel={disableWheelOnInputNumber}
                      size="md"
                      bg="white"
                      borderColor="gray.300"
                      _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
                      _hover={{ borderColor: 'gray.400' }}
                      w="150px"
                      aria-label={`${item.label} entrée`}
                      isInvalid={isNaN(parseFloat(item.value.toString()))}
                      errorBorderColor="red.500"
                    />
                  ) : (
                    <Tooltip
                      label={item.label}
                      placement="top"
                      aria-label={`${item.label} infobulle`}
                    >
                      <Text fontWeight="semibold" color="gray.800">
                        {item.value}
                      </Text>
                    </Tooltip>
                  )}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}
    </Box>
  );
};
const RealEstateDetailsForm = forwardRef(
  (
    props: {
      setParentFormData: any;
      data: IRealEstateForm;
    },
    ref
  ) => {
    // State for editable fields
    const [formData, setFormData] = useState<IRealEstateForm>(props.data);

    // Update travauxTotal when related fields change
    useEffect(() => {
      const amelioration = formData.travauxAmeliorationDeductible === 'Oui'
        ? (formData.travauxAmeliorationMontant || 0)
        : 0;
      const reparation = formData.travauxReparationDeductible === 'Oui'
        ? (formData.travauxReparationMontant || 0)
        : 0;
      setFormData((prev) => ({
        ...prev,
        travauxTotal: amelioration + reparation,
      }));
    }, [
      formData.travauxAmeliorationMontant,
      formData.travauxReparationMontant,
      formData.travauxAmeliorationDeductible,
      formData.travauxReparationDeductible,
    ]);

    // Update fraisAcquisitionForfait when prixAcquisitionMontant changes
    useEffect(() => {
      if (formData.prixAcquisitionMontant) {
        const prixAcquisition =
          parseFloat(formData.prixAcquisitionMontant.toString()) || 0;
        const fraisForfait = prixAcquisition * 0.075; // 7.5% of acquisition price
        setFormData((prev) => ({
          ...prev,
          fraisAcquisitionForfait: fraisForfait,
        }));
      }
    }, [formData.prixAcquisitionMontant]);

    // Update travauxDepensesForfait when prixAcquisitionMontant changes
    useEffect(() => {
      if (formData.prixAcquisitionMontant) {
        const prixAcquisition =
          parseFloat(formData.prixAcquisitionMontant.toString()) || 0;
        const depensesForfait = prixAcquisition * 0.15; // 15% of acquisition price
        setFormData((prev) => ({
          ...prev,
          travauxDepensesForfait: depensesForfait,
        }));
      }
    }, [formData.prixAcquisitionMontant]);

    // Handle value change from form inputs
    const handleValueChange = (name: string, value: string): void => {
      // Convert to number if it's a numeric field
      const numericFields = [
        'travauxAmeliorationMontant',
        'travauxReparationMontant',
        'travauxTotal',
        'travauxConstructionMontant',
        'prixVenteMontant',
        'prixAcquisitionMontant',
        'fraisAcquisitionForfait',
        'travauxDepensesForfait',
        'capitalSocialSCI',
        'nombreAssocies',
        'revenusPartsSCI',
        'recettesExceptionnelles',
        'recettesEmplacementsPublicitaires',
      ];

      if (numericFields.includes(name)) {
        const numValue = value === '' ? 0 : parseFloat(value);
        setFormData((prev) => ({ ...prev, [name]: numValue }));
      } else {
        setFormData((prev) => ({ ...prev, [name]: value }));
      }
    };

    // Color mode for background
    const bgColor = useColorModeValue('white', 'gray.700');

    // Handle radio button change
    const handleRadioChange = (name: string, value: string): void => {
      setFormData((prev) => ({ ...prev, [name]: value }));
    };
    const onSubmit = () => {
      props.setParentFormData(formData);
      return true;
    };
    // Prepare data for form sections
    const locationsData = [
      {
        label: 'Locations Saisonnières/Meublées',
        value: formData.locationsSaisonnieres,
        customRender: () => (
          <RadioGroup
            value={formData.locationsSaisonnieres}
            onChange={(value) =>
              handleRadioChange('locationsSaisonnieres', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Locations Nues',
        value: formData.locationsNues,
        customRender: () => (
          <RadioGroup
            value={formData.locationsNues}
            onChange={(value) => handleRadioChange('locationsNues', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Locations Meublées',
        value: formData.locationsMeublees,
        customRender: () => (
          <RadioGroup
            value={formData.locationsMeublees}
            onChange={(value) => handleRadioChange('locationsMeublees', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Application Indice',
        value: formData.applicationIndice,
        customRender: () => (
          <RadioGroup
            value={formData.applicationIndice}
            onChange={(value) => handleRadioChange('applicationIndice', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
    ];

    const societeData = [
      {
        label: 'Nombre associés si société',
        value: formData.nombreAssocies,
        customRender: () => (
          <Input
            value={formData.nombreAssocies}
            onChange={(e) => {
              const parsedValue = parseInputValue(e.target.value);
              handleValueChange('nombreAssocies', parsedValue);
            }}
            onWheel={disableWheelOnInputNumber}
            size="md"
            bg="white"
            borderColor="gray.300"
            _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
            _hover={{ borderColor: 'gray.400' }}
            maxW="250px"
          />
        ),
      },
      {
        label: 'Revenus issus de parts de SCI/SCPI',
        value: formData.revenusPartsSCI,
        customRender: () => (
          <Input
            value={formData.revenusPartsSCI}
            onChange={(e) => {
              const parsedValue = parseInputValue(e.target.value);
              handleValueChange('revenusPartsSCI', parsedValue);
            }}
            onWheel={disableWheelOnInputNumber}
            size="md"
            bg="white"
            borderColor="gray.300"
            _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
            _hover={{ borderColor: 'gray.400' }}
            maxW="250px"
          />
        ),
      },
      {
        label: 'Recettes exceptionnelles',
        value: formData.recettesExceptionnelles,
        customRender: () => (
          <Input
            value={formData.recettesExceptionnelles}
            onChange={(e) => {
              const parsedValue = parseInputValue(e.target.value);
              handleValueChange('recettesExceptionnelles', parsedValue);
            }}
            onWheel={disableWheelOnInputNumber}
            size="md"
            bg="white"
            borderColor="gray.300"
            _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
            _hover={{ borderColor: 'gray.400' }}
            maxW="250px"
          />
        ),
      },
      {
        label: "Recettes issues d'emplacements publicitaires",
        value: formData.recettesEmplacementsPublicitaires,
        customRender: () => (
          <Input
            value={formData.recettesEmplacementsPublicitaires}
            onChange={(e) => {
              const parsedValue = parseInputValue(e.target.value);
              handleValueChange(
                'recettesEmplacementsPublicitaires',
                parsedValue
              );
            }}
            onWheel={disableWheelOnInputNumber}
            size="md"
            bg="white"
            borderColor="gray.300"
            _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
            _hover={{ borderColor: 'gray.400' }}
            maxW="250px"
          />
        ),
      },
      {
        label: "Si SCI à l'IS, montant du capital social",
        value: formData.capitalSocialSCI,
        isEditable: true,
        onChange: (val: string) => handleValueChange('capitalSocialSCI', val),
      },
    ];

    const travauxData = [
      // Travaux déductibles section header
      {
        label: 'Travaux déductibles :',
        value: '',
        customRender: () => (
          <Text fontWeight="bold" fontSize="md" color="teal.700">
            Montants
          </Text>
        ),
      },
      {
        label: "Travaux d'amélioration",
        value: formData.travauxAmeliorationMontant,
        customRender: () => (
          <Stack direction="row" spacing={4} align="center">
            <RadioGroup
              value={formData.travauxAmeliorationDeductible || 'Oui'}
              onChange={(value) =>
                handleRadioChange('travauxAmeliorationDeductible', value)
              }
            >
              <Stack direction="row" spacing={4}>
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">OUI</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">NON</Text>
                </Radio>
              </Stack>
            </RadioGroup>
            {formData.travauxAmeliorationDeductible === 'Oui' && (
              <Input
                value={formData.travauxAmeliorationMontant}
                onChange={(e) => {
                  const parsedValue = parseInputValue(e.target.value);
                  handleValueChange('travauxAmeliorationMontant', parsedValue);
                }}
                onWheel={disableWheelOnInputNumber}
                size="md"
                bg="white"
                borderColor="gray.300"
                _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
                _hover={{ borderColor: 'gray.400' }}
                maxW="150px"
                placeholder="40000"
              />
            )}
          </Stack>
        ),
        description:
          "Détail: Installation/Remplacement d'un équipement ou élément de confort nouveau...",
      },
      {
        label: "Travaux de réparation et d'entretien",
        value: formData.travauxReparationMontant,
        customRender: () => (
          <Stack direction="row" spacing={4} align="center">
            <RadioGroup
              value={formData.travauxReparationDeductible || 'Oui'}
              onChange={(value) =>
                handleRadioChange('travauxReparationDeductible', value)
              }
            >
              <Stack direction="row" spacing={4}>
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">OUI</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">NON</Text>
                </Radio>
              </Stack>
            </RadioGroup>
            {formData.travauxReparationDeductible === 'Oui' && (
              <Input
                value={formData.travauxReparationMontant}
                onChange={(e) => {
                  const parsedValue = parseInputValue(e.target.value);
                  handleValueChange('travauxReparationMontant', parsedValue);
                }}
                onWheel={disableWheelOnInputNumber}
                size="md"
                bg="white"
                borderColor="gray.300"
                _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
                _hover={{ borderColor: 'gray.400' }}
                maxW="150px"
                placeholder="20000"
              />
            )}
          </Stack>
        ),
        description: 'Détail: Maintien/Remise en état du bien...',
      },
      {
        label: 'Montants',
        value: formData.travauxTotal,
        customRender: () => (
          <Text fontWeight="bold" fontSize="md" color="gray.800">
            {formatCurrency(formData.travauxTotal)}
          </Text>
        ),
      },
      // Travaux non-déductibles section header
      {
        label: 'Travaux non-déductibles :',
        value: '',
        customRender: () => <Box />,
      },
      {
        label: 'Travaux de construction/reconstruction/agrandissement',
        value: formData.travauxConstruction,
        customRender: () => (
          <Stack direction="row" spacing={4} align="center">
            <RadioGroup
              value={formData.travauxConstruction || 'Non'}
              onChange={(value) =>
                handleRadioChange('travauxConstruction', value)
              }
            >
              <Stack direction="row" spacing={4}>
                <Radio value="Oui" colorScheme="teal" size="md">
                  <Text fontSize="md">OUI</Text>
                </Radio>
                <Radio value="Non" colorScheme="teal" size="md">
                  <Text fontSize="md">NON</Text>
                </Radio>
              </Stack>
            </RadioGroup>
            {formData.travauxConstruction === 'Oui' && (
              <Input
                value={formData.travauxConstructionMontant || ''}
                onChange={(e) => {
                  const parsedValue = parseInputValue(e.target.value);
                  handleValueChange('travauxConstructionMontant', parsedValue);
                }}
                onWheel={disableWheelOnInputNumber}
                size="md"
                bg="white"
                borderColor="gray.300"
                _focus={{ borderColor: 'teal.500', boxShadow: 'outline' }}
                _hover={{ borderColor: 'gray.400' }}
                maxW="150px"
                placeholder="Montant"
              />
            )}
          </Stack>
        ),
      },
      // Travaux imputable déficit foncier section
      {
        label: 'Travaux imputable déficit foncier',
        value: formData.travauxDeficitFoncier,
        customRender: () => (
          <RadioGroup
            value={formData.travauxDeficitFoncier}
            onChange={(value) =>
              handleRadioChange('travauxDeficitFoncier', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">OUI</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">NON</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
    ];

    const plusValueData = [
      {
        label: 'Prix de Vente (Montant)',
        value: formData.prixVenteMontant,
        isEditable: true,
        onChange: (val: string) => handleValueChange('prixVenteMontant', val),
      },
      {
        label: "Garantie d'éviction",
        value: formData.garantieEviction,
        customRender: () => (
          <RadioGroup
            value={formData.garantieEviction}
            onChange={(value) => handleRadioChange('garantieEviction', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Diagnostics obligatoires',
        value: formData.diagnosticsObligatoires,
        customRender: () => (
          <RadioGroup
            value={formData.diagnosticsObligatoires}
            onChange={(value) =>
              handleRadioChange('diagnosticsObligatoires', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: "Prix d'Acquisition (Montant)",
        value: formData.prixAcquisitionMontant,
        isEditable: true,
        onChange: (val: string) =>
          handleValueChange('prixAcquisitionMontant', val),
      },
      {
        label: 'Charges et indemnités',
        value: formData.chargesIndemnites,
        customRender: () => (
          <RadioGroup
            value={formData.chargesIndemnites}
            onChange={(value) => handleRadioChange('chargesIndemnites', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: "Frais d'acquisition réels",
        value: formData.fraisAcquisitionReels,
        customRender: () => (
          <RadioGroup
            value={formData.fraisAcquisitionReels}
            onChange={(value) =>
              handleRadioChange('fraisAcquisitionReels', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: "Frais d'acquisition forfaitaire (7,5%)",
        value: formData.fraisAcquisitionForfaitaire,
        customRender: () => (
          <RadioGroup
            value={formData.fraisAcquisitionForfaitaire}
            onChange={(value) =>
              handleRadioChange('fraisAcquisitionForfaitaire', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: "Frais d'acquisition forfaitaire (Montant)",
        value: formData.fraisAcquisitionForfait,
        isEditable: true,
        onChange: (val: string) =>
          handleValueChange('fraisAcquisitionForfait', val),
      },
      {
        label: 'Dépenses de travaux réelles',
        value: formData.depenseTravauxReelles,
        customRender: () => (
          <RadioGroup
            value={formData.depenseTravauxReelles}
            onChange={(value) =>
              handleRadioChange('depenseTravauxReelles', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Dépenses de travaux forfait (15%)',
        value: formData.travauxDepensesForfait,
        isEditable: true,
        onChange: (val: string) =>
          handleValueChange('travauxDepensesForfait', val),
      },
      {
        label: 'Frais de voirie',
        value: formData.fraisVoirie,
        customRender: () => (
          <RadioGroup
            value={formData.fraisVoirie}
            onChange={(value) => handleRadioChange('fraisVoirie', value)}
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
    ];

    const sarlFamilleData = [
      {
        label: 'Locations Meublées',
        value: formData.sarlLocationsMeublees,
        customRender: () => (
          <RadioGroup
            value={formData.sarlLocationsMeublees}
            onChange={(value) =>
              handleRadioChange('sarlLocationsMeublees', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Associés en ligne directe',
        value: formData.sarlAssociesLigneDirecte,
        customRender: () => (
          <RadioGroup
            value={formData.sarlAssociesLigneDirecte}
            onChange={(value) =>
              handleRadioChange('sarlAssociesLigneDirecte', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Associés frère(s)/sœur(s)',
        value: formData.sarlAssociesFreresSoeurs,
        customRender: () => (
          <RadioGroup
            value={formData.sarlAssociesFreresSoeurs}
            onChange={(value) =>
              handleRadioChange('sarlAssociesFreresSoeurs', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
      {
        label: 'Associés conjoints/PACS',
        value: formData.sarlAssociesConjointsPACS,
        customRender: () => (
          <RadioGroup
            value={formData.sarlAssociesConjointsPACS}
            onChange={(value) =>
              handleRadioChange('sarlAssociesConjointsPACS', value)
            }
          >
            <Stack direction="row" spacing={4}>
              <Radio value="Oui" colorScheme="teal" size="md">
                <Text fontSize="md">Oui</Text>
              </Radio>
              <Radio value="Non" colorScheme="teal" size="md">
                <Text fontSize="md">Non</Text>
              </Radio>
            </Stack>
          </RadioGroup>
        ),
      },
    ];
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box
        p={{ base: 4, md: 8 }}
        bg={bgColor}
        mx="auto"
        maxWidth="1200px"
        width="100%"
        borderWidth="1px"
        borderRadius="md"
        boxShadow="lg"
      >
        <Box
          p={{ base: 2, md: 6 }}
          borderRadius={8}
          role="main"
          aria-label="Détails immobiliers"
        >
          {/* Locations Section */}
          <MotionBox
            mb={6}
            bg="white"
            borderRadius={8}
            boxShadow="sm"
            overflow="hidden"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 0 * 0.1 }}
            aria-label="Section Locations"
            role="region"
          >
            <FormSection title="Locations" data={locationsData} />
          </MotionBox>

          {/* Société Section */}
          <MotionBox
            mb={6}
            bg="white"
            borderRadius={8}
            boxShadow="sm"
            overflow="hidden"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 1 * 0.1 }}
            aria-label="Section Société"
            role="region"
          >
            <FormSection title="Société" data={societeData} />
          </MotionBox>

          {/* Travaux Section */}
          <MotionBox
            mb={6}
            bg="white"
            borderRadius={8}
            boxShadow="sm"
            overflow="hidden"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 2 * 0.1 }}
            aria-label="Section Travaux"
            role="region"
          >
            <FormSection title="Travaux" data={travauxData} />
          </MotionBox>

          {/* Plus-Value Section */}
          <MotionBox
            mb={6}
            bg="white"
            borderRadius={8}
            boxShadow="sm"
            overflow="hidden"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 3 * 0.1 }}
            aria-label="Section Calcul de la Plus-Value"
            role="region"
          >
            <FormSection title="Calcul de la Plus-Value" data={plusValueData} />
          </MotionBox>

          {/* SARL de Famille Section */}
          <MotionBox
            mb={6}
            bg="white"
            borderRadius={8}
            boxShadow="sm"
            overflow="hidden"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 4 * 0.1 }}
            aria-label="Section SARL de Famille"
            role="region"
          >
            <FormSection title="SARL de Famille" data={sarlFamilleData} />
          </MotionBox>
        </Box>
      </Box>
    );
  }
);
export default RealEstateDetailsForm;
