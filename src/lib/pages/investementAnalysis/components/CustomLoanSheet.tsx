import {
  useState,
  useEffect,
  ChangeEvent,
  useRef,
  useCallback,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { disableWheelOnInputNumber } from '~/lib/utils/disableWheel';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import {
  Box,
  Grid,
  GridItem,
  Input,
  Heading,
  Center,
  useColorModeValue,
  useBreakpointValue,
  Spinner,
  Text,
  Flex,
  Divider,
  InputGroup,
  InputLeftElement,
  InputRightAddon,
  Icon,
  HStack,
  Card,
  CardHeader,
  CardBody,
  VStack,
} from '@chakra-ui/react';
import {
  FaEuroSign,
  FaPercentage,
  FaCalendarAlt,
  FaChartLine,
} from 'react-icons/fa';
import {
  FormDataInvestementAnalysistSteps,
  IInvestementCalculatorData,
} from '~/lib/types/investement-analysis';
type SetParentFormDataType = (data: IInvestementCalculatorData) => void;

export interface LoanScheduleEntry {
  N_PAIEMENT: number;
  DATE_ECHEANCE: string;
  SOLDE_DE_DEPART: string;
  PROGRAMME: string;
  SUPPLEMENTAIRE: string;
  PAIEMENT_TOTAL: string;
  PRINCIPAL: string;
  INTERETS: string;
  SOLDE_FINAL: string;
  INTERETS_CUMULES: string;
  ASSURANCE: string;
  TOTAL_MENSUAL: string;
  TOTAL_CHARGE: string;
}

const CustomLoanSheet = forwardRef(
  (
    props: {
      setParentFormData: SetParentFormDataType;
      data: FormDataInvestementAnalysistSteps;
    },
    ref
  ) => {
    const [rowData, setRowData] = useState<LoanScheduleEntry[]>([]);
    const [startDate, setStartDate] = useState<Date>(
      new Date(props.data.investementCalculatorData.dateAcquisition)
    );
    const [totalInterest, setTotalInterest] = useState(0);
    const [soldByYear, setSoldByYear] = useState<Record<number, number>>({});
    const [dataSoldByYear, setDataSoldByYear] = useState<
      Record<number, number>
    >({});

    const [loanAmount, setLoanAmount] = useState(
      props.data.investementCalculatorData.loanAmount
    );
    const [ratePercentage, setRatePercentage] = useState(1.3);
    const [loanTerm, setLoanTerm] = useState(25);
    const [paymentsPerYearValue, setPaymentsPerYearValue] = useState(12);
    const [insuranceAmount, setInsuranceAmount] = useState(96);

    const [isCalculating, setIsCalculating] = useState(false);
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

    const columnDefs = [
      {
        field: 'N_PAIEMENT' as keyof LoanScheduleEntry,
        headerName: 'N° PAIEMENT',
        width: 100,
      },
      {
        field: 'DATE_ECHEANCE' as keyof LoanScheduleEntry,
        headerName: "DATE D'ECHEANCE",
        width: 150,
      },
      {
        field: 'SOLDE_DE_DEPART' as keyof LoanScheduleEntry,
        headerName: 'SOLDE DE DÉPART',
        width: 150,
      },
      {
        field: 'PROGRAMME' as keyof LoanScheduleEntry,
        headerName: 'PAIEMENT PROGRAMME',
        width: 150,
      },
      {
        field: 'SUPPLEMENTAIRE' as keyof LoanScheduleEntry,
        headerName: 'PAIEMENT SUPPLÉMENTAIRE',
        width: 150,
      },
      {
        field: 'PAIEMENT_TOTAL' as keyof LoanScheduleEntry,
        headerName: 'PAIEMENT TOTAL',
        width: 150,
      },
      {
        field: 'PRINCIPAL' as keyof LoanScheduleEntry,
        headerName: 'PRINCIPAL',
        width: 100,
      },
      {
        field: 'INTERETS' as keyof LoanScheduleEntry,
        headerName: 'INTÉRÊTS',
        width: 100,
      },
      {
        field: 'SOLDE_FINAL' as keyof LoanScheduleEntry,
        headerName: 'SOLDE FINAL',
        width: 150,
      },
      {
        field: 'INTERETS_CUMULES' as keyof LoanScheduleEntry,
        headerName: 'INTÉRÊTS CUMULÉS',
        width: 150,
      },
      {
        field: 'ASSURANCE' as keyof LoanScheduleEntry,
        headerName: 'ASSURANCE',
        width: 100,
      },
      {
        field: 'TOTAL_MENSUAL' as keyof LoanScheduleEntry,
        headerName: 'TOTAL MENSUEL',
        width: 150,
      },
      {
        field: 'TOTAL_CHARGE' as keyof LoanScheduleEntry,
        headerName: 'TOTAL CHARGE',
        width: 150,
      },
    ];

    const generateLoanSchedule = useCallback(
      (currentDate: Date): LoanScheduleEntry[] => {
        let date = new Date(currentDate);
        let loanBalance = loanAmount;
        let schedule: LoanScheduleEntry[] = [];
        let calculateTotalInterest = 0;
        const monthlyRate = ratePercentage / 100 / paymentsPerYearValue;
        const totalPaymentsCalc = loanTerm * paymentsPerYearValue;
        const newDataSoldByYear: Record<number, number> = {};

        let pvif = Math.pow(1 + monthlyRate, totalPaymentsCalc);
        let calculatedMonthlyPayment =
          monthlyRate === 0
            ? loanBalance / totalPaymentsCalc
            : (monthlyRate / (pvif - 1)) * -(loanBalance * pvif);
        calculatedMonthlyPayment = Math.abs(calculatedMonthlyPayment);
        for (let i = 1; i <= totalPaymentsCalc; i++) {
          let interest = loanBalance * monthlyRate;
          let principalPayment = Math.min(
            calculatedMonthlyPayment - interest,
            loanBalance
          );
          let newBalance = loanBalance - principalPayment;
          calculateTotalInterest += interest;
          if (!newDataSoldByYear[date.getFullYear()])
            newDataSoldByYear[date.getFullYear()] = newBalance;

          schedule.push({
            N_PAIEMENT: i,
            DATE_ECHEANCE: date.toLocaleDateString('fr-FR'),
            SOLDE_DE_DEPART: `${loanBalance.toFixed(2)} €`,
            PROGRAMME: `${calculatedMonthlyPayment.toFixed(2)} €`,
            SUPPLEMENTAIRE: '0.00 €',
            PAIEMENT_TOTAL: `${calculatedMonthlyPayment.toFixed(2)} €`,
            PRINCIPAL: `${principalPayment.toFixed(2)} €`,
            INTERETS: `${interest.toFixed(2)} €`,
            SOLDE_FINAL:
              newBalance > 0 ? `${newBalance.toFixed(2)} €` : '0.00 €',
            INTERETS_CUMULES: `${calculateTotalInterest.toFixed(2)} €`,
            ASSURANCE: `${(insuranceAmount / 12).toFixed(2)} €`,
            TOTAL_MENSUAL: `${(calculatedMonthlyPayment + insuranceAmount / 12).toFixed(2)} €`,
            TOTAL_CHARGE: `${(interest + insuranceAmount / 12).toFixed(2)} €`,
          });
          setTotalInterest(calculateTotalInterest);
          loanBalance = newBalance;
          date.setMonth(date.getMonth() + 1);
          if (loanBalance <= 0) break;
        }
        setDataSoldByYear(newDataSoldByYear);
        return schedule;
      },
      [
        loanAmount,
        ratePercentage,
        loanTerm,
        paymentsPerYearValue,
        insuranceAmount,
      ]
    );

    const debouncedUpdate = useCallback(() => {
      setIsCalculating(true);
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      debounceTimerRef.current = setTimeout(() => {
        const newSchedule = generateLoanSchedule(startDate);
        setRowData(newSchedule);
        setSoldByYear(dataSoldByYear);
        setIsCalculating(false);
      }, 500);
    }, [generateLoanSchedule, startDate, dataSoldByYear]);

    useEffect(() => {
      const calculateSchedule = () => {
        setIsCalculating(true);
        const newSchedule = generateLoanSchedule(startDate);
        setRowData(newSchedule);
        setSoldByYear(dataSoldByYear);
        setIsCalculating(false);
      };

      calculateSchedule();
    }, [
      ratePercentage,
      loanAmount,
      loanTerm,
      paymentsPerYearValue,
      insuranceAmount,
      startDate,
      generateLoanSchedule,
      dataSoldByYear,
    ]);

    const handleInputChange = (
      e: ChangeEvent<HTMLInputElement>,
      setter: (value: number) => void,
      min?: number,
      max?: number
    ) => {
      const value = parseFloat(e.target.value);
      if (!isNaN(value)) {
        let finalValue = value;
        if (min !== undefined) finalValue = Math.max(finalValue, min);
        if (max !== undefined) finalValue = Math.min(finalValue, max);
        setter(finalValue);
        debouncedUpdate();
      } else if (e.target.value === '') {
        setter(0);
        debouncedUpdate();
      }
    };

    const bgColor = useColorModeValue('gray.50', 'gray.800');
    const cardBgColor = useColorModeValue('white', 'gray.700');
    const borderColor = useColorModeValue('gray.200', 'gray.600');
    const accentColor = useColorModeValue('teal.500', 'teal.300');
    const headingColor = useColorModeValue('gray.700', 'white');
    const textColor = useColorModeValue('gray.700', 'gray.200');
    const highlightColor = useColorModeValue('orange.100', 'orange.700');
    const headingSize = useBreakpointValue({ base: 'lg', md: 'xl', lg: '2xl' });

    const calculatedMonthlyPayment = (() => {
      const monthlyRate = ratePercentage / 100 / paymentsPerYearValue;
      const totalPaymentsCalc = loanTerm * paymentsPerYearValue;

      if (monthlyRate === 0) return loanAmount / totalPaymentsCalc;

      let pvif = Math.pow(1 + monthlyRate, totalPaymentsCalc);
      let pmt = (monthlyRate / (pvif - 1)) * -(loanAmount * pvif);
      return Math.abs(pmt);
    })();
    const onSubmit = () => {
      props.setParentFormData({
        ...props.data.investementCalculatorData,
        dateAcquisition: startDate.toISOString(),
        loanAmount,
        loanCalculatedData: rowData,
        soldByYear,
        monthlyPayment: calculatedMonthlyPayment + insuranceAmount / 12,
      });
      return true;
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box p={{ base: 4, md: 6 }} bg={bgColor} borderRadius="xl" boxShadow="sm">
        <Center mb={{ base: 6, md: 8 }}>
          <VStack>
            <Heading
              as="h1"
              fontSize={headingSize}
              color={accentColor}
              textAlign="center"
            >
              PLAN D'AMORTISSEMENT
            </Heading>
            <Divider width="60%" borderColor={accentColor} opacity={0.3} />
          </VStack>
        </Center>

        <Grid
          templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }}
          gap={8}
          mb={8}
        >
          <GridItem>
            <Card
              borderRadius="lg"
              boxShadow="md"
              bg={cardBgColor}
              borderColor={borderColor}
              borderWidth="1px"
              height="100%"
            >
              <CardHeader bg={highlightColor} borderTopRadius="lg" py={3}>
                <Flex justify="space-between" align="center">
                  <Heading size="md" color={headingColor}>
                    PARAMÈTRES DU PRÊT
                  </Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaEuroSign} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Montant du prêt
                      </Text>
                    </HStack>
                    <InputGroup>
                      <InputLeftElement pointerEvents="none">
                        <Icon as={FaEuroSign} color="gray.400" />
                      </InputLeftElement>
                      <Input
                        placeholder="Montant"
                        name="loanPrincipal"
                        type="number"
                        value={loanAmount}
                        onWheel={disableWheelOnInputNumber}
                        onChange={(e) => handleInputChange(e, setLoanAmount, 0)}
                        min={0}
                        step={1000}
                        borderColor={borderColor}
                        _hover={{ borderColor: accentColor }}
                        _focus={{
                          borderColor: accentColor,
                          boxShadow: `0 0 0 1px ${accentColor}`,
                        }}
                      />
                    </InputGroup>
                  </Box>

                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaPercentage} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Taux d'intérêt annuel
                      </Text>
                    </HStack>
                    <InputGroup>
                      <Input
                        placeholder="Taux"
                        name="rate"
                        type="number"
                        value={ratePercentage}
                        onWheel={disableWheelOnInputNumber}
                        onChange={(e) =>
                          handleInputChange(e, setRatePercentage, 0, 20)
                        }
                        min={0}
                        step={0.1}
                        max={20}
                        borderColor={borderColor}
                        _hover={{ borderColor: accentColor }}
                        _focus={{
                          borderColor: accentColor,
                          boxShadow: `0 0 0 1px ${accentColor}`,
                        }}
                      />
                      <InputRightAddon
                        children="%"
                        bg={accentColor}
                        color="white"
                      />
                    </InputGroup>
                  </Box>

                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaChartLine} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Période du prêt
                      </Text>
                    </HStack>
                    <InputGroup>
                      <Input
                        placeholder="Années"
                        name="termYears"
                        type="number"
                        value={loanTerm}
                        onWheel={disableWheelOnInputNumber}
                        onChange={(e) =>
                          handleInputChange(e, setLoanTerm, 1, 40)
                        }
                        min={1}
                        max={40}
                        step={1}
                        borderColor={borderColor}
                        _hover={{ borderColor: accentColor }}
                        _focus={{
                          borderColor: accentColor,
                          boxShadow: `0 0 0 1px ${accentColor}`,
                        }}
                      />
                      <InputRightAddon
                        children="ans"
                        bg={accentColor}
                        color="white"
                      />
                    </InputGroup>
                  </Box>

                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaChartLine} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Remboursements par an
                      </Text>
                    </HStack>
                    <Input
                      placeholder="Paiements par an"
                      name="paymentsPerYear"
                      type="number"
                      value={paymentsPerYearValue}
                      onWheel={disableWheelOnInputNumber}
                      onChange={(e) =>
                        handleInputChange(e, setPaymentsPerYearValue, 1, 12)
                      }
                      min={1}
                      max={12}
                      step={1}
                      borderColor={borderColor}
                      _hover={{ borderColor: accentColor }}
                      _focus={{
                        borderColor: accentColor,
                        boxShadow: `0 0 0 1px ${accentColor}`,
                      }}
                    />
                  </Box>

                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaEuroSign} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Assurance annuelle
                      </Text>
                    </HStack>
                    <InputGroup>
                      <InputLeftElement pointerEvents="none">
                        <Icon as={FaEuroSign} color="gray.400" />
                      </InputLeftElement>
                      <Input
                        placeholder="Assurance"
                        name="insurancePerYear"
                        type="number"
                        value={insuranceAmount}
                        onWheel={disableWheelOnInputNumber}
                        onChange={(e) =>
                          handleInputChange(e, setInsuranceAmount, 0)
                        }
                        min={0}
                        step={100}
                        borderColor={borderColor}
                        _hover={{ borderColor: accentColor }}
                        _focus={{
                          borderColor: accentColor,
                          boxShadow: `0 0 0 1px ${accentColor}`,
                        }}
                      />
                    </InputGroup>
                  </Box>

                  <Box>
                    <HStack mb={1}>
                      <Icon as={FaCalendarAlt} color={accentColor} />
                      <Text fontWeight="bold" color={textColor}>
                        Date de début du prêt
                      </Text>
                    </HStack>
                    <Box
                      borderWidth="1px"
                      borderColor={borderColor}
                      borderRadius="md"
                      p={1}
                    >
                      <DatePicker
                        selected={startDate}
                        onChange={(date: Date) => date && setStartDate(date)}
                        dateFormat="dd/MM/yyyy"
                        className="custom-datepicker"
                      />
                    </Box>
                  </Box>
                </VStack>
              </CardBody>
            </Card>
          </GridItem>

          <GridItem>
            <Card
              borderRadius="lg"
              boxShadow="md"
              bg={cardBgColor}
              borderColor={borderColor}
              borderWidth="1px"
              height="100%"
            >
              <CardHeader bg="red.100" borderTopRadius="lg" py={3}>
                <Flex justify="space-between" align="center">
                  <Heading size="md" color={headingColor}>
                    RÉSUMÉ DU PRÊT
                  </Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Flex
                    justify="space-between"
                    align="center"
                    gap={75}
                    p={2}
                    bg="gray.50"
                    borderRadius="md"
                  >
                    <Text fontWeight="bold" color={textColor}>
                      Paiement programmé:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {calculatedMonthlyPayment.toFixed(2)} €
                    </Text>
                  </Flex>

                  <Flex justify="space-between" align="center" p={2}>
                    <Text fontWeight="bold" color={textColor}>
                      Assurance mensuelle:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {(insuranceAmount / 12).toFixed(2)} €
                    </Text>
                  </Flex>

                  <Flex
                    justify="space-between"
                    align="center"
                    p={2}
                    bg="gray.50"
                    borderRadius="md"
                  >
                    <Text fontWeight="bold" color={textColor}>
                      Mensualité totale:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {(
                        calculatedMonthlyPayment +
                        insuranceAmount / 12
                      ).toFixed(2)}{' '}
                      €
                    </Text>
                  </Flex>

                  <Flex justify="space-between" align="center" p={2}>
                    <Text fontWeight="bold" color={textColor}>
                      Nombre de paiements prévu:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {loanTerm * paymentsPerYearValue}
                    </Text>
                  </Flex>

                  <Flex
                    justify="space-between"
                    align="center"
                    p={2}
                    bg="gray.50"
                    borderRadius="md"
                  >
                    <Text fontWeight="bold" color={textColor}>
                      Nombre réel de paiements:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {rowData.length}
                    </Text>
                  </Flex>

                  <Flex justify="space-between" align="center" p={2}>
                    <Text fontWeight="bold" color={textColor}>
                      Total des intérêts:
                    </Text>
                    <Text fontWeight="bold" color={accentColor}>
                      {totalInterest.toFixed(2)} €
                    </Text>
                  </Flex>

                  <Box p={2} bg="gray.50" borderRadius="md">
                    <Text fontWeight="bold" color={textColor} mb={2}>
                      Nom du preteur:
                    </Text>
                    <Input
                      placeholder="Nom"
                      size="md"
                      borderColor={borderColor}
                      _hover={{ borderColor: accentColor }}
                      _focus={{ borderColor: accentColor }}
                    />
                  </Box>
                </VStack>
              </CardBody>
            </Card>
          </GridItem>
        </Grid>
        <Card
          borderRadius="lg"
          boxShadow="md"
          bg={cardBgColor}
          borderColor={borderColor}
          borderWidth="1px"
          mb={4}
        >
          <CardHeader bg={accentColor} borderTopRadius="lg" py={3}>
            <Flex justify="space-between" align="center">
              <Heading size="md" color="white">
                TABLEAU D'AMORTISSEMENT
              </Heading>
            </Flex>
          </CardHeader>
          <CardBody p={0}>
            {isCalculating ? (
              <Flex
                justify="center"
                align="center"
                p={8}
                height="300px"
                width="100%"
              >
                <VStack spacing={4}>
                  <Spinner
                    thickness="4px"
                    speed="0.65s"
                    emptyColor="gray.200"
                    color={accentColor}
                    size="xl"
                  />
                  <Text fontSize="lg" color={textColor} fontWeight="medium">
                    Calcul en cours...
                  </Text>
                </VStack>
              </Flex>
            ) : (
              <Box
                width="100%"
                overflowX="auto"
                className="ag-theme-alpine"
                height="400px"
                sx={{
                  '.ag-header': {
                    backgroundColor: useColorModeValue('gray.50', 'gray.700'),
                  },
                  '.ag-header-cell': {
                    color: textColor,
                    fontWeight: 'bold',
                  },
                  '.ag-row-odd': {
                    backgroundColor: useColorModeValue('gray.50', 'gray.700'),
                  },
                  '.ag-row-hover': {
                    backgroundColor:
                      useColorModeValue('teal.50', 'teal.900') + ' !important',
                  },
                }}
              >
                <AgGridReact
                  columnDefs={columnDefs as any}
                  rowData={rowData}
                  pagination={true}
                  paginationPageSize={10}
                  defaultColDef={{
                    resizable: true,
                    sortable: true,
                    filter: true,
                  }}
                  animateRows={true}
                />
              </Box>
            )}
          </CardBody>
        </Card>
      </Box>
    );
  }
);

export default CustomLoanSheet;
