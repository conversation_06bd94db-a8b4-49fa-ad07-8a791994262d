import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import MicroBICTourismeNCProjectionTable from './MicroBICTourismeNCProjectionTable';
import MicroBICTourismeNCPlusValueProjectionTable from './MicroBICTourismeNCPlusValueProjectionTable';
import {
  MicroBICTourismeNCFormProps,
  MicroBICTourismeNCFormData,
} from '~/lib/types/micro-bic-tourisme-nc';
import { calculateMicroBICTourismeNCData } from '../../utils/microBICTourismeNCCalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';

const MicroBICTourismeNCForm = forwardRef<any, MicroBICTourismeNCFormProps>(
  (props, ref) => {
    const { data, setParentFormData } = props;
    const [formData, setFormData] = useState<MicroBICTourismeNCFormData>(
      {} as MicroBICTourismeNCFormData
    );

    // Format currency for display
    const formatCurrency = (value: number) => {
      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    };

    // Calculate values based on the data from previous steps and Excel formulas
    useEffect(() => {
      // Use the calculation function from the separate file
      const calculatedData = calculateMicroBICTourismeNCData(data);

      // Update the local state
      setFormData(calculatedData);

      // Pass the calculated data to the parent component
      setParentFormData(calculatedData);
    }, []);

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit: () => {
        // Validation logic if needed
        return true;
      },
      setDataFromParent: (parentData: any) => {
        setFormData(parentData);
      },
    }));

    // Prepare data for the conditions table
    const conditionsData = [
      {
        label: 'Locations Saisonnières/meublées',
        value: formData.locationsMeublees || '',
        comment:
          "Location meublée à usage d'habitation touristique non classée",
      },
      {
        label: 'Revenus locatifs hors charges du foyer fiscal',
        value: formatCurrency(formData.loyersAnnuels || 0),
        comment: 'Montant total des loyers perçus',
      },
      {
        label: 'Revenus issus de parts de SCI/SCPI',
        value: formatCurrency(formData.revenusSCI || 0),
        comment: 'Revenus provenant de parts de SCI ou SCPI',
      },
      {
        label:
          'Recettes exceptionnelles (ex : indemnisations par les assurances compensant la perte de loyer)',
        value: formatCurrency(formData.recettesExceptionnelles || 0),
        comment: 'Recettes exceptionnelles perçues',
      },
      {
        label: "Recettes issues d'emplacements publicitaires",
        value: formatCurrency(formData.recettesEmplacementsPublicitaires || 0),
        comment: "Recettes provenant d'emplacements publicitaires",
      },
      {
        label: 'TOTAL',
        value: formatCurrency(formData.totalRevenus || 0),
        comment: 'Total des revenus locatifs',
      },
      {
        label: 'Autres revenus locatifs au régime réel ?',
        value: formData.autresRevenusLocatifsReels || '',
        comment: "Existence d'autres revenus locatifs déclarés au régime réel",
      },
      {
        label: 'Possibilité du régime micro-BIC ?',
        value: formData.regimeMicroBICPossible ? 'OUI' : 'NON',
        comment: 'Éligibilité au régime micro-BIC',
      },
      {
        label: 'Autres revenus locatifs au régime micro-BIC',
        value: formatCurrency(formData.autresRevenusLocatifsMicroBIC || 0),
        comment:
          'Montant des autres revenus locatifs déclarés au régime micro-BIC',
      },
      {
        label: 'Possibilité de maintien du régime micro-BIC ?',
        value: formData.possibiliteMaintienRegimeMicroBIC ? 'OUI' : 'NON',
        comment: 'Le total des revenus ne doit pas dépasser 15 000€',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener || '',
        comment: 'Résultat de la vérification des conditions (oui/non)',
      },
      {
        label: 'Régime Micro-BIC Applicable au cas étudié',
        value: formData.regimeMicroBICApplicable ? 'OUI' : 'NON',
        comment: "Résultat final de l'éligibilité",
      },
    ];

    // Prepare data for the micro-BIC calculations table
    const microBICData = [
      {
        label: 'Revenus bruts',
        value: formatCurrency(formData.totalRevenus || 0),
        comment: 'Total des revenus locatifs bruts',
      },
      {
        label: 'Abattement forfaitaire (30%)',
        value: formatCurrency(formData.abattementForfaitaire || 0),
        comment: 'Abattement forfaitaire de 30% sur les revenus bruts',
      },
      {
        label: 'Revenu net imposable',
        value: formatCurrency(formData.revenuNetImposable || 0),
        comment: "Revenu après abattement, soumis à l'impôt",
      },
    ];

    // Prepare data for the imposition table
    const impositionData = [
      {
        label: "Taux Marginal d'Imposition",
        value: `${((formData.tauxMarginalImposition || 0) * 100).toFixed(0)}%`,
        comment: "Taux d'imposition applicable à votre tranche de revenus",
      },
      {
        label: "Montant de l'impôt",
        value: formatCurrency(formData.montantImpot || 0),
        comment: 'Impôt sur le revenu dû sur les revenus locatifs',
      },
      {
        label: 'Prélèvements sociaux (17,2%)',
        value: formatCurrency(formData.prelevementsSociaux || 0),
        comment: 'CSG, CRDS et autres prélèvements sociaux',
      },
      {
        label: 'CSG déductible (7%)',
        value: formatCurrency(formData.csgDeductible || 0),
        comment: 'Partie déductible de la CSG',
      },
      {
        label: 'Total des prélèvements',
        value: formatCurrency(formData.totalPrelevements || 0),
        comment: 'Total des impôts et prélèvements sociaux',
      },
    ];

    // Prepare data for the rentabilite table
    const rentabiliteData = [
      {
        label: 'Revenu net après impôt',
        value: formatCurrency(formData.revenuNetApresImpot || 0),
        comment: 'Revenu locatif après déduction des impôts',
      },
      {
        label: "Rentabilité nette d'impôt",
        value: `${(((formData.revenuNetApresImpot || 0) / (data?.realEstateData?.prixAcquisitionMontant || 1)) * 100).toFixed(2)}%`,
        comment: "Rendement net après impôts par rapport au prix d'acquisition",
      },
    ];

    // Prepare data for the tresorerie table
    const tresorerieData = [
      {
        label: 'Trésorerie dégagée (hors emprunts)',
        value: formatCurrency(formData.tresorerieDegageeHorsEmprunts || 0),
        comment: "Trésorerie générée avant remboursement d'emprunt",
      },
      {
        label: 'Remboursement emprunt',
        value: formatCurrency(formData.remboursementEmprunt || 0),
        comment: 'Montant annuel de remboursement du prêt',
      },
      {
        label: 'Trésorerie totale dégagée',
        value: formatCurrency(formData.tresorerieTotaleDegagee || 0),
        comment: "Trésorerie nette après remboursement d'emprunt",
      },
      {
        label: 'Cumul trésorerie dégagée',
        value: formatCurrency(formData.tresorerieCumulee || 0),
        comment: 'Cumul de la trésorerie sur la période',
      },
    ];

    // Define tab content
    const conditionsTab = (
      <FinanceTable
        title="Régime Micro-BIC - Conditions"
        data={conditionsData}
        showComments={false}
      />
    );

    const calculationsTab = (
      <>
        <FinanceTable
          title="Revenus BIC"
          data={microBICData}
          showComments={true}
        />
        <FinanceTable
          title="Imposition"
          data={impositionData}
          showComments={true}
        />
        <FinanceTable
          title="Rentabilité"
          data={rentabiliteData}
          showComments={true}
        />
      </>
    );

    const treasuryTab = (
      <FinanceTable
        title="Trésorerie"
        data={tresorerieData}
        showComments={true}
      />
    );

    const projectionsTab =
      formData.projectionData && formData.projectionData.length > 0 ? (
        <MicroBICTourismeNCProjectionTable data={formData.projectionData} />
      ) : null;

    const plusValuesTab =
      formData.plusValueData && formData.plusValueData.length > 0 ? (
        <MicroBICTourismeNCPlusValueProjectionTable
          data={formData.plusValueData}
        />
      ) : null;

    // Define tabs configuration
    const tabs = [
      { label: 'Conditions', content: conditionsTab },
      { label: 'Calculs', content: calculationsTab },
      { label: 'Trésorerie', content: treasuryTab },
      {
        label: 'Projections',
        content: projectionsTab,
        isVisible:
          formData.projectionData && formData.projectionData.length > 0,
      },
      {
        label: 'Plus-values',
        content: plusValuesTab,
        isVisible: formData.plusValueData && formData.plusValueData.length > 0,
      },
    ];

    const description =
      "Le régime Micro-BIC pour les locations touristiques non classées est un régime fiscal simplifié pour les propriétaires qui louent des logements meublés à usage touristique non classés et perçoivent moins de 15 000€ de revenus locatifs annuels. Il permet de bénéficier d'un abattement forfaitaire de 30% sur les revenus bruts.";

    return (
      <TaxRegimeFormBase
        title="Prév° Micro-BIC tourisme NC"
        description={description}
        tabs={tabs}
        warningMessage={
          formData.message ||
          "Régime Micro-BIC Tourisme Non Classé n'est plus applicable au cas étudié, un changement de régime est à opérer vers le régime BIC réel"
        }
        showWarning={formData.regimeMicroBICApplicable === 'Non'}
      />
    );
  }
);

export default MicroBICTourismeNCForm;
