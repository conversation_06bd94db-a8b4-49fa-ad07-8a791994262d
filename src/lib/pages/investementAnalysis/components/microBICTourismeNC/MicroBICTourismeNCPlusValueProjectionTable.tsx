import type { PlusValueDataItem } from '~/lib/types/micro-bic-tourisme-nc';
import ProjectionTable from '../common/ProjectionTable';
import {
  formatCurrency,
  formatPercentage,
  getValueColor,
} from '../../utils/formatters';

interface MicroBICTourismeNCPlusValueProjectionTableProps {
  data: PlusValueDataItem[];
}

const MicroBICTourismeNCPlusValueProjectionTable = ({
  data,
}: MicroBICTourismeNCPlusValueProjectionTableProps) => {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Prix de Vente',
      accessor: 'prixVente',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition",
      accessor: 'prixAcquisition',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value',
      accessor: 'plusValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux Abattement IR',
      accessor: 'tauxAbattementImpotRevenu',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Abattement IR',
      accessor: 'abattementImpotRevenu',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable IR',
      accessor: 'baseTaxableImpotRevenu',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt sur le Revenu',
      accessor: 'impotRevenu',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux Abattement PS',
      accessor: 'tauxAbattementPrelevementsSociaux',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Abattement PS',
      accessor: 'abattementPrelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable PS',
      accessor: 'baseTaxablePrelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux',
      accessor: 'prelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxation Supplémentaire',
      accessor: 'taxationSupplementaire',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Imposition',
      accessor: 'totalImposition',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Générée',
      accessor: 'tresorerieGeneree',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Prévisionnel de Plus-Values - Régime Micro-BIC Tourisme Non Classé"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de revente du bien à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default MicroBICTourismeNCPlusValueProjectionTable;
