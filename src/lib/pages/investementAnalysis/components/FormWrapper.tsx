import { Box, useBreakpointValue } from '@chakra-ui/react';
import ProfileInvestor from '~/lib/pages/buyerAssessment/components/profileInvestor/ProfileInvestor.tsx';
import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import InvestmentCalculator from './caseStudy/InvestementCalculator';
import CustomLoanSheet from './CustomLoanSheet';
import RealEstateForm from './RealEstateForm';
import SyntheseForm from './synthese/SyntheseForm';

interface FormWrapperProps {
  activeStep: number;
  formRefs: Array<React.RefObject<any>>;
  formData: FormDataInvestementAnalysistSteps;
  handleFormDataChange: <T extends keyof FormDataInvestementAnalysistSteps>(
    formName: T,
    newData: FormDataInvestementAnalysistSteps[T]
  ) => void;
  onRegimeClick?: (regime: string) => void;
}

export function FormWrapper({
  activeStep,
  formRefs,
  formData,
  handleFormDataChange,
  onRegimeClick,
}: FormWrapperProps) {
  // Responsive padding based on screen size
  const padding = useBreakpointValue({ base: 2, md: 4, lg: 6 });

  return (
    <Box
      width="100%"
      p={padding}
      transition="all 0.3s ease"
      aria-live="polite"
      role="region"
      aria-label={`Étape ${activeStep + 1}`}
    >
      {activeStep === 0 && (
        <ProfileInvestor
          ref={formRefs[activeStep]}
          data={formData.profileInvestorData}
          setParentFormData={(data) =>
            handleFormDataChange('profileInvestorData', data)
          }
        />
      )}
      {activeStep === 1 && (
        <CustomLoanSheet
          ref={formRefs[activeStep]}
          data={formData}
          setParentFormData={(data) =>
            handleFormDataChange('investementCalculatorData', data)
          }
        />
      )}
      {activeStep === 2 && (
        <InvestmentCalculator
          ref={formRefs[activeStep]}
          data={formData.investementCalculatorData}
          setParentFormData={(data) =>
            handleFormDataChange('investementCalculatorData', data)
          }
        />
      )}
      {activeStep === 3 && (
        <RealEstateForm
          ref={formRefs[activeStep]}
          data={formData.realEstateData}
          setParentFormData={(data) =>
            handleFormDataChange('realEstateData', data)
          }
        />
      )}

      {activeStep === 4 && (
        <SyntheseForm
          ref={formRefs[activeStep]}
          data={formData}
          onRegimeClick={onRegimeClick}
          setParentFormData={(key, data) => handleFormDataChange(key, data)}
        />
      )}
    </Box>
  );
}
