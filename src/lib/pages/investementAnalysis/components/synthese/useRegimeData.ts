import { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';

export const useRegimeData = (data: FormDataInvestementAnalysistSteps) => {
  // Check if a regime is applicable based on its key
  const isApplicable = (key: string): boolean => {
    switch (key) {
      case 'microFonciers':
        return data.microFonciersData?.regimeMicroFoncierApplicable === true;
      case 'fonciersReels':
        return data.fonciersReelsData?.regimeFonciersReelApplicable === true;
      case 'bicReelLMNP':
        return data.bicReelLMNPData?.regimeBICReelApplicable === 'OUI';
      case 'lmpSansTVA':
        return data.lmpSansTVAData?.regimeLMPApplicable === 'OUI';
      case 'microBIC':
        return data.microBICData?.regimeMicroBICApplicable === true;
      case 'microBICChambreHote':
        return data.microBICChambreHoteData?.regimeMicroBICApplicable === true;
      case 'microBICTourismeNC':
        return data.microBICTourismeNCData?.regimeMicroBICApplicable === true;
      case 'sciIS':
        return data.sciISData?.regimeApplicable === 'OUI';
      case 'sarlDeFamille':
        // Use the correct property name for SARL de Famille
        return data.sarlDeFamilleData?.regimeSARLDeFamilleApplicable === 'OUI';
      default:
        return false;
    }
  };

  // Get a value for a specific regime, year, and type
  const getRegimeValue = (key: string, year: number, type: string): number | 'N/A' => {
    // Get the projection data and field name based on the regime key and type
    let fieldName = '';

    // Set the field name based on the type
    if (type === 'net') {
      if (key === 'microFonciers') {
        fieldName = 'tresorerieTotale';
      } else if (key === 'fonciersReels') {
        fieldName = 'tresorerieTotaleDegagee';
      } else if (['bicReelLMNP', 'lmpSansTVA', 'sciIS', 'sarlDeFamille'].includes(key)) {
        fieldName = 'totalCashFlow';
      } else if (['microBIC', 'microBICChambreHote', 'microBICTourismeNC'].includes(key)) {
        fieldName = 'tresorerieHorsEmprunts';
      }
    } else if (type === 'cumul') {
      if (key === 'microFonciers') {
        fieldName = 'cumulTresorerie';
      } else if (key === 'fonciersReels') {
        fieldName = 'tresorerieTotalCumul';
      } else if (['bicReelLMNP', 'lmpSansTVA', 'sciIS', 'sarlDeFamille'].includes(key)) {
        fieldName = 'cumulativeCashFlow';
      } else if (['microBIC', 'microBICChambreHote', 'microBICTourismeNC'].includes(key)) {
        fieldName = 'tresorerieCumulee';
      }
    } else if (type === 'resale') {
      fieldName = 'totalTresorerieGeneree';
    }

    // If we don't have a field name, return N/A
    if (!fieldName) return 'N/A';

    // Get the projection data based on the regime key
    const projectionData =
      key === 'microFonciers' ? data.microFonciersData?.projectionData :
      key === 'fonciersReels' ? data.fonciersReelsData?.projectionData :
      key === 'bicReelLMNP' ? data.bicReelLMNPData?.projectionData :
      key === 'lmpSansTVA' ? data.lmpSansTVAData?.projectionData :
      key === 'microBIC' ? data.microBICData?.projectionData :
      key === 'microBICChambreHote' ? data.microBICChambreHoteData?.projectionData :
      key === 'microBICTourismeNC' ? data.microBICTourismeNCData?.projectionData :
      key === 'sciIS' ? data.sciISData?.projectionData :
      key === 'sarlDeFamille' ? data.sarlDeFamilleData?.projectionData :
      undefined;

    // If we don't have projection data, return N/A
    if (!projectionData) return 'N/A';

    // Check if there's a limit exceeded year for the current regime
    let limitExceededYear: number | undefined;

    switch (key) {
      case 'microFonciers':
        limitExceededYear = data.microFonciersData?.limitExceededYear;
        break;
      case 'microBIC':
        limitExceededYear = data.microBICData?.limitExceededYear;
        break;
      case 'microBICChambreHote':
        limitExceededYear = data.microBICChambreHoteData?.limitExceededYear;
        break;
      case 'microBICTourismeNC':
        limitExceededYear = data.microBICTourismeNCData?.limitExceededYear;
        break;
      case 'bicReelLMNP':
        limitExceededYear = data.bicReelLMNPData?.limitExceededYear;
        break;
      case 'lmpSansTVA':
        limitExceededYear = data.lmpSansTVAData?.limitExceededYear;
        break;
      default:
        limitExceededYear = undefined;
    }

    // If the limit is exceeded and the requested year is after the limit year, return N/A
    if (limitExceededYear && year > (limitExceededYear - (data.investementCalculatorData?.dateAcquisition ? new Date(data.investementCalculatorData.dateAcquisition).getFullYear() : 0))) {
      return 'N/A';
    }

    // Get the data for the specific year
    const yearData = projectionData[year - 1];
    if (!yearData) return 'N/A';

    // Return the value or 0 if it's undefined
    return yearData[fieldName] ?? 0;
  };

  return { getRegimeValue, isApplicable };
};