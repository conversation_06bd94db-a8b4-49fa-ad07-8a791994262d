export interface RegimeConfig {
  key: string;
  label: string;
  description: string;
  color: string;
  icon?: string;
}

export const regimesConfig: RegimeConfig[] = [
  {
    key: 'microFonciers',
    label: 'Micro-Fonciers',
    description:
      'Régime fiscal simplifié pour les revenus locatifs avec un abattement forfaitaire de 30%',
    color: 'teal',
    icon: '🏠',
  },
  {
    key: 'fonciersReels',
    label: 'Fonciers-Réel',
    description:
      'Régime permettant de déduire les charges réelles des revenus locatifs',
    color: 'blue',
    icon: '📊',
  },
  {
    key: 'bicReelLMNP',
    label: 'LMNP BIC-Réel simplifié',
    description:
      'Location meublée non professionnelle avec déduction des charges réelles et amortissements',
    color: 'purple',
    icon: '🛋️',
  },
  {
    key: 'lmpSansTVA',
    label: 'LMP Sans TVA',
    description:
      'Location meublée professionnelle sans assujettissement à la TVA',
    color: 'pink',
    icon: '💼',
  },
  {
    key: 'microBIC',
    label: 'Micro-BIC habitation',
    description:
      'Régime simplifié pour la location meublée avec un abattement forfaitaire de 50%',
    color: 'orange',
    icon: '🏡',
  },
  {
    key: 'sciIS',
    label: 'SCI IS',
    description:
      "Société Civile Immobilière soumise à l'impôt sur les sociétés",
    color: 'green',
    icon: '🏢',
  },
  {
    key: 'sarlDeFamille',
    label: 'SARL DE FAMILLE',
    description:
      "Société à responsabilité limitée composée uniquement de membres d'une même famille",
    color: 'cyan',
    icon: '👪',
  },
  {
    key: 'microBICChambreHote',
    label: "Micro-BIC Chambres d'hôtes",
    description:
      "Régime simplifié pour les chambres d'hôtes ou locations touristiques classées",
    color: 'yellow',
    icon: '🛏️',
  },
  {
    key: 'microBICTourismeNC',
    label: 'Micro-BIC tourisme non classé',
    description:
      'Régime simplifié pour les locations touristiques non classées',
    color: 'red',
    icon: '🏨',
  },
];
