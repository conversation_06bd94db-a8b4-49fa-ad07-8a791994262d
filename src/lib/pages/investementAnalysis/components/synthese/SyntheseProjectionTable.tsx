import {
  Box,
  Heading,
  Flex,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Divider,
  Text,
  Icon,
  useColorModeValue,
  Switch,
  FormControl,
  FormLabel,
  HStack,
  Tooltip,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import { regimesConfig } from './regimes.config';
import { useRegimeData } from './useRegimeData';
import { RegimeTableRows } from './RegimeTableRows';
import { FaChartLine } from 'react-icons/fa';

interface Props {
  data: FormDataInvestementAnalysistSteps;
  onRegimeClick?: (regime: string) => void;
}

const SyntheseProjectionTable = ({ data, onRegimeClick }: Props) => {
  const [yearsToShow, setYearsToShow] = useState<number>(5);
  const [showOnlyApplicable, setShowOnlyApplicable] = useState<boolean>(true);
  const years = Array.from({ length: 30 }, (_, i) => i + 1);
  const displayYears = years.slice(0, yearsToShow);

  const { getRegimeValue, isApplicable } = useRegimeData(data);

  // Filter regimes based on the showOnlyApplicable state
  const filteredRegimes = showOnlyApplicable
    ? regimesConfig.filter(regime => isApplicable(regime.key))
    : regimesConfig;

  const bgGradient = useColorModeValue(
    'linear(to-r, teal.50, blue.50)',
    'linear(to-r, teal.900, blue.900)'
  );

  return (
    <Box>
      <Flex
        alignItems="center"
        mb={6}
        bgGradient={bgGradient}
        p={4}
        borderRadius="lg"
        boxShadow="sm"
      >
        <Icon as={FaChartLine} color="teal.500" boxSize={6} mr={3} />
        <Heading size="md" color="teal.600">
          Synthèse des régimes fiscaux
        </Heading>
        <Text ml={4} color="gray.600" fontSize="sm">
          Comparez les différents régimes fiscaux applicables à votre
          investissement
        </Text>
      </Flex>

      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <FormControl display="flex" alignItems="center" width="auto">
          <FormLabel htmlFor="show-applicable" mb="0" mr={2} fontSize="sm" color="gray.600">
            <Tooltip label="Afficher uniquement les régimes applicables à votre situation" hasArrow placement="top">
              <HStack spacing={1} cursor="help">
                <Text>Régimes applicables uniquement</Text>
              </HStack>
            </Tooltip>
          </FormLabel>
          <Switch
            id="show-applicable"
            colorScheme="teal"
            size="md"
            isChecked={showOnlyApplicable}
            onChange={(e) => setShowOnlyApplicable(e.target.checked)}
          />
        </FormControl>

        <Select
          value={yearsToShow}
          onChange={(e) => setYearsToShow(Number(e.target.value))}
          width="200px"
          colorScheme="teal"
          borderColor="teal.200"
          _hover={{ borderColor: 'teal.300' }}
        >
          {[5, 10, 15, 20, 30].map((val) => (
            <option key={val} value={val}>
              {val} ans
            </option>
          ))}
        </Select>
      </Flex>

      <TableContainer overflowX="auto">
        <Table variant="simple" size="sm" colorScheme="teal">
          <Thead bg="teal.50">
            <Tr>
              <Th width="200px">Régime fiscal</Th>
              <Th>Type</Th>
              {displayYears.map((year) => (
                <Th key={year} isNumeric>
                  Année {year}
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {filteredRegimes.map((regime) => (
              <React.Fragment key={regime.key}>
                <RegimeTableRows
                  regime={regime}
                  years={displayYears}
                  getValue={getRegimeValue}
                  isApplicable={isApplicable(regime.key)}
                  onRegimeClick={onRegimeClick}
                  limitExceededYear={
                    regime.key === 'microFonciers' ? data.microFonciersData?.limitExceededYear :
                    regime.key === 'microBIC' ? data.microBICData?.limitExceededYear :
                    regime.key === 'microBICChambreHote' ? data.microBICChambreHoteData?.limitExceededYear :
                    regime.key === 'microBICTourismeNC' ? data.microBICTourismeNCData?.limitExceededYear :
                    regime.key === 'bicReelLMNP' ? data.bicReelLMNPData?.limitExceededYear :
                    regime.key === 'lmpSansTVA' ? data.lmpSansTVAData?.limitExceededYear :
                    undefined
                  }
                />
                <Tr>
                  <Td colSpan={2 + displayYears.length}>
                    <Divider my={2} />
                  </Td>
                </Tr>
              </React.Fragment>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default SyntheseProjectionTable;
