import {
  Tr,
  Td,
  Badge,
  Flex,
  Box,
  Text,
  Tooltip,
  HStack,
  useColorModeValue,
  Card,
  CardBody,
  Icon,
} from '@chakra-ui/react';
import { FaExclamationTriangle } from 'react-icons/fa';
import { keyframes } from '@emotion/react';
import { formatCurrency, getValueColor } from '~/lib/utils/formatters';
import { RegimeConfig } from './regimes.config';
import { useState } from 'react';

interface Props {
  regime: RegimeConfig;
  years: number[];
  getValue: (regimeKey: string, year: number, type: string) => number | 'N/A';
  isApplicable: boolean;
  onRegimeClick?: (key: string) => void;
  limitExceededYear?: number; // Year when the 15,000€ limit will be exceeded (for microFonciers)
}

// Define a pulse animation for the regime card
const pulseAnimation = keyframes`
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 128, 128, 0.4); }
  70% { transform: scale(1.02); box-shadow: 0 0 0 10px rgba(0, 128, 128, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 128, 128, 0); }
`;

export const RegimeTableRows = ({
  regime,
  years,
  getValue,
  isApplicable,
  onRegimeClick,
}: Props) => {
  const [isHovered, setIsHovered] = useState(false);

  const getColor = (val: any) => getValueColor(val);
  const getFormatted = (val: any) =>
    val === 'N/A' ? 'N/A' : formatCurrency(val);

  const cumulativeWithResale = (year: number) => {
    const cumul = getValue(regime.key, year, 'cumul');
    const resale = getValue(regime.key, year, 'resale');
    return cumul !== 'N/A' && resale !== 'N/A'
      ? (cumul as number) + (resale as number)
      : 'N/A';
  };

  // Get the best year (highest cumulative value)
  const getBestYear = () => {
    let bestYear = 1;
    let bestValue = -Infinity;

    years.forEach((year) => {
      const value = cumulativeWithResale(year);
      if (value !== 'N/A' && (value as number) > bestValue) {
        bestValue = value as number;
        bestYear = year;
      }
    });

    return {
      year: bestYear,
      value: bestValue !== -Infinity ? bestValue : 'N/A',
    };
  };

  const { year: bestYear } = getBestYear();

  // Background colors for the regime card
  const bgColor = useColorModeValue(
    `${regime.color}.50`,
    `${regime.color}.900`
  );
  const hoverBgColor = useColorModeValue(
    `${regime.color}.100`,
    `${regime.color}.800`
  );
  const borderColor = useColorModeValue(
    `${regime.color}.200`,
    `${regime.color}.700`
  );
  const textColor = useColorModeValue(
    `${regime.color}.800`,
    `${regime.color}.100`
  );

  // Animation style when hovered
  const animation = isHovered ? `${pulseAnimation} 2s infinite` : 'none';

  return (
    <>
      <Tr bg="white">
        <Td width="200px">
          <Tooltip
            label={regime.description}
            placement="right"
            hasArrow
            bg={`${regime.color}.700`}
            color="white"
          >
            <Card
              variant="outline"
              borderColor={borderColor}
              bg={bgColor}
              borderRadius="md"
              boxShadow={isHovered ? 'md' : 'sm'}
              transition="all 0.3s"
              animation={animation}
              cursor="pointer"
              onClick={() => onRegimeClick && onRegimeClick(regime.key)}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              _hover={{
                bg: hoverBgColor,
                transform: 'translateY(-2px)',
                boxShadow: 'md',
              }}
            >
              <CardBody p={3}>
                <Flex justifyContent="space-between" alignItems="center">
                  <HStack spacing={2}>
                    <Text fontSize="lg" mr={1}>
                      {regime.icon}
                    </Text>
                    <Box>
                      <Text fontWeight="bold" color={textColor}>
                        {regime.label}
                      </Text>
                      <Text fontSize="xs" color={`${regime.color}.600`}>
                        Meilleur résultat: Année {bestYear}
                      </Text>
                    </Box>
                  </HStack>
                  <Badge
                    colorScheme={isApplicable ? 'green' : 'red'}
                    variant={isApplicable ? 'solid' : 'outline'}
                    borderRadius="full"
                    px={2}
                  >
                    {isApplicable ? 'OUI' : 'NON'}
                  </Badge>
                </Flex>
              </CardBody>
            </Card>
          </Tooltip>
        </Td>
        <Td>Tréso net d'imposition</Td>
        {years.map((year) => (
          <Td
            key={year}
            isNumeric
            color={getColor(getValue(regime.key, year, 'net'))}
          >
            {getFormatted(getValue(regime.key, year, 'net'))}
          </Td>
        ))}
      </Tr>
      <Tr bg="gray.50">
        <Td></Td>
        <Td>Cumul Tréso net d'imposition</Td>
        {years.map((year) => (
          <Td
            key={year}
            isNumeric
            color={getColor(getValue(regime.key, year, 'cumul'))}
          >
            {getFormatted(getValue(regime.key, year, 'cumul'))}
          </Td>
        ))}
      </Tr>
      <Tr bg="white">
        <Td></Td>
        <Td>
          <Flex alignItems="center">
            <Text>Tréso si revente</Text>
          </Flex>
        </Td>
        {years.map((year) => (
          <Td
            key={year}
            isNumeric
            color={getColor(getValue(regime.key, year, 'resale'))}
          >
            {getFormatted(getValue(regime.key, year, 'resale'))}
          </Td>
        ))}
      </Tr>
      <Tr bg="gray.50">
        <Td></Td>
        <Td>Cumul activité + revente</Td>
        {years.map((year) => (
          <Td key={year} isNumeric color={getColor(cumulativeWithResale(year))}>
            {getFormatted(cumulativeWithResale(year))}
          </Td>
        ))}
      </Tr>
    </>
  );
};
