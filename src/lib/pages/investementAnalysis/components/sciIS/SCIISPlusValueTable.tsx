import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency } from '../../utils/formatters';
import { PlusValueDataItem } from '~/lib/types/sci-is';

interface SCIISPlusValueTableProps {
  data: PlusValueDataItem[];
}

const SCIISPlusValueTable = ({ data }: SCIISPlusValueTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.600';
    if (value < 0) return 'red.600';
    return undefined;
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Prix de Vente',
      accessor: 'salePrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition",
      accessor: 'acquisitionPrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements Déduits',
      accessor: 'deductedAmortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Valeur Nette Comptable',
      accessor: 'netBookValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value Fiscale',
      accessor: 'fiscalPlusValue',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Résultat Fiscal Avant Impôt',
      accessor: 'fiscalResultBeforeTax',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Impôt sur les Sociétés',
      accessor: 'totalIS',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Dividendes Distribuables',
      accessor: 'distributableDividendsForYear',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Flat Tax',
      accessor: 'flatTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Dividendes Nets',
      accessor: 'netDividendsFlatTax',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Après Distribution',
      accessor: 'treasuryAfterLoanRepayment',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="SCI à l'Impôt sur les Sociétés - Prévisionnel de reventes - Plus values"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de revente du bien à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default SCIISPlusValueTable;
