import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import SCIISProjectionTable from './SCIISProjectionTable';
import SCIISPlusValueTable from './SCIISPlusValueTable';
import SCIISPartsTable from './SCIISPartsTable';
import { SCIISFormProps, SCIISFormData } from '~/lib/types/sci-is';
import { calculateSCIISData } from '../../utils/sciISCalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';

const SCIISForm = forwardRef<any, SCIISFormProps>(
  ({ data, setParentFormData }, ref) => {
    const [formData, setFormData] = useState<SCIISFormData>({
      locationsMeublees: 'Non',
      locationsSaisonnieresMeublees: 'Non',
      nombreAssociesRequis: 'NON',
      concatener: 'Non',
      regimeApplicable: 'NON',
      indiceMoyen: 0.02,
      projectionData: [],
      plusValueData: [],
      sciPartsData: [],
    });

    // Calculate values based on the data from previous steps and Excel formulas
    useEffect(() => {
      // Use the calculation function from the separate file
      const calculatedData = calculateSCIISData(data);

      // Update the local state
      setFormData(calculatedData);

      // Pass the calculated data to the parent component
      setParentFormData(calculatedData);
    }, [data, setParentFormData]);

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit: () => {
        // Validation logic if needed
        return true;
      },
      setDataFromParent: (parentData: any) => {
        setFormData(parentData?.sciISData || {});
      },
    }));

    // Data for the finance tables
    const conditionsData = [
      {
        label: 'Locations Meublées',
        value: formData.locationsMeublees || 'Non',
      },
      {
        label: 'Locations Saisonnières/Meublées',
        value: formData.locationsSaisonnieresMeublees || 'Non',
      },
      {
        label: "Nombre associé requis pour SCI à l'IS",
        value: formData.nombreAssociesRequis || 'NON',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'Régime applicable ?',
        value: formData.regimeApplicable || 'NON',
        colorScheme: formData.regimeApplicable === 'OUI' ? 'green' : 'red',
      },
    ];

    const disclaimerData = [
      {
        label:
          "Ce prévisionnel part d'une hypothèse de rémunération par le biais de dividendes et non sous une autre forme.",
        value: '',
      },
      {
        label:
          "Ce prévisionnel part également d'une hypothèse de la constitution d'une SCI à l'IS par des personnes physiques (et non par le biais d'une PM)",
        value: '',
      },
    ];

    // Define tab content
    const conditionsTab = (
      <>
        <FinanceTable
          title="SCI à l'Impôt sur les Sociétés - Conditions"
          data={conditionsData}
          showComments={false}
        />
        <FinanceTable
          title="Avertissements"
          data={disclaimerData}
          showComments={false}
        />
      </>
    );

    const projectionsTab = (
      <>
        {formData.projectionData && formData.projectionData.length > 0 ? (
          <SCIISProjectionTable data={formData.projectionData} />
        ) : null}
      </>
    );

    const plusValuesTab = (
      <>
        {formData.plusValueData && formData.plusValueData.length > 0 ? (
          <SCIISPlusValueTable data={formData.plusValueData} />
        ) : null}
      </>
    );

    const sciPartsTab = (
      <>
        {formData.sciPartsData && formData.sciPartsData.length > 0 ? (
          <SCIISPartsTable data={formData.sciPartsData} />
        ) : null}
      </>
    );

    // Define tabs configuration
    const tabs = [
      { label: 'Conditions', content: conditionsTab },
      {
        label: 'Prévisionnel',
        content: projectionsTab,
        isVisible:
          formData.projectionData && formData.projectionData.length > 0,
      },
      {
        label: 'Plus-values',
        content: plusValuesTab,
        isVisible: formData.plusValueData && formData.plusValueData.length > 0,
      },
      {
        label: 'Parts SCI',
        content: sciPartsTab,
        isVisible: formData.sciPartsData && formData.sciPartsData.length > 0,
      },
    ];

    const description =
      "La SCI à l'Impôt sur les Sociétés (IS) est une structure juridique qui permet d'acquérir et de gérer un bien immobilier tout en bénéficiant du régime fiscal des sociétés. Elle permet notamment d'amortir le bien immobilier et de déduire les intérêts d'emprunt.";

    return (
      <TaxRegimeFormBase
        title="Prev° SCI IS"
        description={description}
        tabs={tabs}
        warningMessage="Le régime SCI à l'IS n'est pas applicable au cas étudié."
        showWarning={formData.regimeApplicable !== 'OUI'}
      />
    );
  }
);

export default SCIISForm;
