import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency } from '../../utils/formatters';
import { ProjectionDataItem } from '~/lib/types/sci-is';

interface SCIISProjectionTableProps {
  data: ProjectionDataItem[];
}

const SCIISProjectionTable = ({ data }: SCIISProjectionTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.600';
    if (value < 0) return 'red.600';
    return undefined;
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Revenus Locatifs',
      accessor: 'rentalIncome',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Charges Locatives',
      accessor: 'rentalCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Assurance PNO',
      accessor: 'nonOccupantInsurance',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxes Foncières',
      accessor: 'propertyTaxes',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Intérêts d'emprunt",
      accessor: 'loanInterest',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements',
      accessor: 'amortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Comptable',
      accessor: 'accountingResult',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Impôt sur les Sociétés',
      accessor: 'totalIS',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Dividendes Distribuables',
      accessor: 'distributableDividends',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Flat Tax',
      accessor: 'flatTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Dividendes Nets',
      accessor: 'netDividends',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Totale',
      accessor: 'totalCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Cumulée',
      accessor: 'cumulativeCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="SCI à l'Impôt sur les Sociétés - Prévisionnel de trésorerie sur 30 ans"
      description="Ce tableau présente une projection financière sur 30 ans de votre investissement en SCI à l'IS."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
      footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
    />
  );
};

export default SCIISProjectionTable;
