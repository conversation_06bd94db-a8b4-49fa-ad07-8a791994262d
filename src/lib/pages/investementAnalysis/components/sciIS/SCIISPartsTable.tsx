import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency } from '../../utils/formatters';
import { SCIPartsDataItem } from '~/lib/types/sci-is';

interface SCIISPartsTableProps {
  data: SCIPartsDataItem[];
}

const SCIISPartsTable = ({ data }: SCIISPartsTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.600';
    if (value < 0) return 'red.600';
    return undefined;
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Valeur Vénale Immeuble',
      accessor: 'propertyValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie',
      accessor: 'treasuryWithoutDividends',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Comptes Courants Associés',
      accessor: 'associateAccounts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Crédits',
      accessor: 'loans',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Valeur Mathématique Théorique',
      accessor: 'theoreticalSalePrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Valeur d'Acquisition",
      accessor: 'acquisitionValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value sur Parts',
      accessor: 'plusValueOnParts',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Flat Tax',
      accessor: 'flatTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Dividendes Nets',
      accessor: 'netDividendsFlatTax',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="SCI à l'Impôt sur les Sociétés - Prévisionnel de cessions des parts sociales"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de cession des parts sociales à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default SCIISPartsTable;
