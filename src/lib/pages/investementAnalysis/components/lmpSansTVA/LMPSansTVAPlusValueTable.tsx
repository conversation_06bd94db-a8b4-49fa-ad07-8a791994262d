import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency } from '../../utils/formatters';
import { PlusValueDataItem } from '~/lib/types/lmp-sans-tva';

interface LMPSansTVAPlusValueTableProps {
  data: PlusValueDataItem[];
}

const LMPSansTVAPlusValueTable = ({ data }: LMPSansTVAPlusValueTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.600';
    if (value < 0) return 'red.600';
    return undefined;
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Prix de Vente',
      accessor: 'salePrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition",
      accessor: 'acquisitionPrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements Déduits',
      accessor: 'deductedAmortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Valeur Nette Comptable',
      accessor: 'netBookValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value Fiscale',
      accessor: 'fiscalPlusValue',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Plus-Value Court Terme',
      accessor: 'shortTermPlusValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IR TMI',
      accessor: 'shortTermIncomeTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'SSI',
      accessor: 'ssiContributions',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Imposition CT',
      accessor: 'totalShortTermTaxation',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value Long Terme',
      accessor: 'longTermPlusValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IR 12,8%',
      accessor: 'incomeTax12_8Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'PS 17,2%',
      accessor: 'socialContributions17_2Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Imposition LT',
      accessor: 'totalLongTermTaxation',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Capital Restant',
      accessor: 'remainingLoan',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Après Remboursement',
      accessor: 'treasuryAfterLoanRepayment',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime LMP - Prévisionnel de reventes - Plus values"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de revente du bien à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default LMPSansTVAPlusValueTable;
