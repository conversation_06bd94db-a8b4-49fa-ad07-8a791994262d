import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import LMPSansTVAProjectionTable from './LMPSansTVAProjectionTable';
import LMPSansTVAPlusValueTable from './LMPSansTVAPlusValueTable';
import {
  LMPSansTVAFormProps,
  LMPSansTVAFormData,
} from '~/lib/types/lmp-sans-tva';
import { calculateLMPSansTVAData } from '../../utils/lmpSansTVACalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';
import { formatCurrency } from '../../utils/formatters';

const LMPSansTVAForm = forwardRef<any, LMPSansTVAFormProps>(
  ({ data, setParentFormData }, ref) => {
    const [formData, setFormData] = useState<LMPSansTVAFormData>({
      locationsMeublees: 'Non',
      locationsSaisonnieresMeublees: 'Non',
      concatener: false,
      revenusLocatifsMeublesChargesComprises: 0,
      nombrePartsFoyerFiscal: 1,
      limiteRevenusLocatifsMeuble: 23000,
      limiteAtteinte: 'NON',
      revenusLocatifsAutres: 'NON',
      revenusLocatifsMeublesMicroBIC: 0,
      possibiliteMaintenirRegimeReel: 'OUI',
      conclusion: 'NON',
      regimeLMPApplicable: 'NON',
      indiceMoyen: 0.02,
      projectionData: [],
      plusValueData: [],
    });

    // Calculate values based on the data from previous steps and Excel formulas
    useEffect(() => {
      // Use the calculation function from the separate file
      const calculatedData = calculateLMPSansTVAData(data);

      // Update the local state
      setFormData(calculatedData);

      // Pass the calculated data to the parent component
      setParentFormData(calculatedData);
    }, [data, setParentFormData]);

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit: () => {
        // Validation logic if needed
        return true;
      },
      setDataFromParent: (parentData: any) => {
        setFormData(parentData?.lmpSansTVAData || {});
      },
    }));

    // Data for the finance tables
    const conditionsData = [
      {
        label: 'Locations Meublées',
        value: formData.locationsMeublees || 'Non',
      },
      {
        label: 'Locations Saisonnières/Meublées',
        value: formData.locationsSaisonnieresMeublees || 'Non',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'Revenus locatifs meublés charges comprises du foyer fiscal',
        value: formatCurrency(formData.revenusLocatifsMeublesChargesComprises),
      },
      {
        label: 'Nombre de parts du foyer fiscal',
        value: formData.nombrePartsFoyerFiscal.toString(),
      },
      {
        label: 'Limite des revenus locatifs issus de la location en meublée',
        value: formatCurrency(formData.limiteRevenusLocatifsMeuble),
      },
      {
        label: 'Limite N°1 atteinte ?',
        value: formData.limiteAtteinte || 'NON',
        colorScheme: formData.limiteAtteinte === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'Les revenus issus de la location en meublée représentent-ils plus de la moitié des revenus globaux du foyer fiscal ?',
        value: formData.revenusLocatifsAutres || 'NON',
        colorScheme: formData.revenusLocatifsAutres === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'Revenus locatifs meublés au régime MICRO-BIC',
        value: formatCurrency(formData.revenusLocatifsMeublesMicroBIC),
      },
      {
        label: 'Possibilité de maintien du régime réel sous le statut LMP ?',
        value: formData.possibiliteMaintenirRegimeReel || 'OUI',
        colorScheme: formData.possibiliteMaintenirRegimeReel === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'CONCLUSION :',
        value: '',
      },
      {
        label: "Le régime BIC au réel s'applique-t-il obligatoirement ?",
        value: formData.limiteAtteinte === 'OUI' ? 'OUI' : 'SUR OPTION',
        colorScheme: formData.limiteAtteinte === 'OUI' ? 'green' : 'yellow',
      },
      {
        label: 'Le régime BIC au réel simplifié sous le statut LMP est-il applicable ?',
        value: formData.regimeLMPApplicable || 'NON',
        colorScheme: formData.regimeLMPApplicable === 'OUI' ? 'green' :
                    formData.regimeLMPApplicable === 'SUR OPTION' ? 'yellow' : 'red',
      },
    ];

    const disclaimerData = [
      {
        label: "Ce prévisionnel part d'une hypothèse de rémunération par le biais de dividendes et non sous une autre forme.",
        value: '',
      },
      {
        label: "Ce prévisionnel part également d'une hypothèse de la constitution d'une SCI à l'IS par des personnes physiques (et non par le biais d'une PM)",
        value: '',
      },
    ];

    // Define tab content
    const conditionsTab = (
      <>
        <FinanceTable
          title="LOUEUR EN MEUBLE PROFESSIONNEL (LMP) - Conditions"
          data={conditionsData}
          showComments={false}
        />
        <FinanceTable
          title="Avertissements"
          data={disclaimerData}
          showComments={false}
        />
      </>
    );

    const projectionsTab = (
      <>
        {formData.projectionData && formData.projectionData.length > 0 ? (
          <LMPSansTVAProjectionTable data={formData.projectionData} />
        ) : null}
      </>
    );

    const plusValuesTab = (
      <>
        {formData.plusValueData && formData.plusValueData.length > 0 ? (
          <LMPSansTVAPlusValueTable data={formData.plusValueData} />
        ) : null}
      </>
    );

    // Define tabs configuration
    const tabs = [
      { label: 'Conditions', content: conditionsTab },
      {
        label: 'Prévisionnel',
        content: projectionsTab,
        isVisible:
          formData.projectionData && formData.projectionData.length > 0,
      },
      {
        label: 'Plus-values',
        content: plusValuesTab,
        isVisible: formData.plusValueData && formData.plusValueData.length > 0,
      },
    ];

    const description =
      "Le régime LMP (Loueur en Meublé Professionnel) sans TVA s'applique aux personnes qui louent des logements meublés et dont les revenus locatifs dépassent 23 000€ par an et représentent plus de la moitié des revenus globaux du foyer fiscal. Ce régime permet de déduire les charges réelles, d'amortir les biens et de bénéficier du statut de professionnel.";

    return (
      <TaxRegimeFormBase
        title="Prev° LMP sans TVA"
        description={description}
        tabs={tabs}
        warningMessage="Le régime LMP sans TVA n'est pas applicable au cas étudié."
        showWarning={formData.regimeLMPApplicable !== 'OUI'}
      />
    );
  }
);

export default LMPSansTVAForm;
