import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency } from '../../utils/formatters';
import { ProjectionDataItem } from '~/lib/types/lmp-sans-tva';

interface LMPSansTVAProjectionTableProps {
  data: ProjectionDataItem[];
}

const LMPSansTVAProjectionTable = ({
  data,
}: LMPSansTVAProjectionTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.600';
    if (value < 0) return 'red.600';
    return undefined;
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Revenus Locatifs',
      accessor: 'rentalIncome',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Charges d'entretien",
      accessor: 'maintenanceCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Charges Locatives',
      accessor: 'rentalCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Assurance PNO',
      accessor: 'nonOccupantInsurance',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Assurance Emprunteur',
      accessor: 'loanInsurance',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxes Foncières',
      accessor: 'propertyTaxes',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CFE',
      accessor: 'cfe',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Intérêts d'emprunt",
      accessor: 'loanInterest',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements',
      accessor: 'amortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Fiscal',
      accessor: 'fiscalResult',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Cotisations SSI',
      accessor: 'ssiContributions',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IR',
      accessor: 'incomeTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Impositions',
      accessor: 'totalTaxes',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Dégagée',
      accessor: 'totalCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Cumul Trésorerie',
      accessor: 'cumulativeCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="LOUEUR EN MEUBLE PROFESSIONNEL (LMP) - Prévisionnel sur 30 ans"
      description="Ce tableau présente une projection financière sur 30 ans de votre investissement en LMP sans TVA."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
      footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
    />
  );
};

export default LMPSansTVAProjectionTable;
