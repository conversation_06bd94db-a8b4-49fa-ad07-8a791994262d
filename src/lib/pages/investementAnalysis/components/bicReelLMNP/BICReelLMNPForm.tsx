import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import BICReelLMNPProjectionTable from './BICReelLMNPProjectionTable';
import BICReelLMNPPlusValueTable from './BICReelLMNPPlusValueTable';
import {
  BICReelLMNPFormProps,
  BICReelLMNPFormData,
} from '~/lib/types/bic-reel-lmnp';
import { calculateBICReelLMNPData } from '../../utils/bicReelLMNPCalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';
import { formatCurrency } from '../../utils/formatters';

const BICReelLMNPForm = forwardRef<any, BICReelLMNPFormProps>(
  ({ data, setParentFormData }, ref) => {
    const [formData, setFormData] = useState<BICReelLMNPFormData>({
      locationsMeublees: 'Non',
      locationsSaisonnieresMeublees: 'Non',
      concatener: false,
      revenusLocatifsMeublesChargesComprises: 0,
      nombrePartsFoyerFiscal: 1,
      revenusLocatifsMeublesSuperieurMoitieRevenus: 'NON',
      limiteRevenusLocatifsMeuble: 23000,
      limiteAtteinte:  'NON',
      revenusLocatifsMeublesMicroBIC: 0,
      possibiliteMaintenirRegimeReel: 'OUI',
      regimeBICReelApplicable: 'NON',
      indiceMoyen: 0.02,
      projectionData: [],
      plusValueData: [],
    });

    // Using the formatCurrency utility from formatters.ts

    // Calculate values based on the data from previous steps and Excel formulas
    useEffect(() => {
      // Use the calculation function from the separate file
      const calculatedData = calculateBICReelLMNPData(data);

      // Update the local state
      setFormData(calculatedData);

      // Pass the calculated data to the parent component
      setParentFormData(calculatedData);
    }, [data, setParentFormData]);

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit: () => {
        // Validation logic if needed
        return true;
      },
      setDataFromParent: (parentData: any) => {
        setFormData(parentData?.bicReelLMNPData || {});
      },
    }));

    // Data for the finance tables
    const conditionsData = [
      {
        label: 'Locations Meublées',
        value: formData.locationsMeublees || 'Non',
      },
      {
        label: 'Locations Saisonnières/Meublées',
        value: formData.locationsSaisonnieresMeublees || 'Non',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'Revenus locatifs meublés charges comprises du foyer fiscal',
        value: formatCurrency(
          formData.revenusLocatifsMeublesChargesComprises || 0
        ),
      },
      {
        label: 'Nombre de parts du foyer fiscal',
        value: formData.nombrePartsFoyerFiscal.toString(),
      },
      {
        label: 'Limite des revenus locatifs issus de la location en meublée',
        value: formatCurrency(formData.limiteRevenusLocatifsMeuble || 0),
      },
      {
        label: 'Limite N°1 atteinte ?',
        value:  formData.limiteAtteinte || 'NON',
      },
      {
        label: 'Si Limite précédente atteinte :',
        value: '',
      },
      {
        label:
          'Les revenus issus de la location en meublée représentent-ils plus de la moitié des revenus globaux du foyer fiscal ?',
        value: formData.revenusLocatifsMeublesSuperieurMoitieRevenus,
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'Revenus locatifs meublés au régime MICRO-BIC',
        value: formatCurrency(formData.revenusLocatifsMeublesMicroBIC || 0),
      },
      {
        label: 'Possibilité de maintien du régime réel sous le statut LMNP ?',
        value: formData.possibiliteMaintenirRegimeReel || 'OUI',
      },
      {
        label: 'CONCLUSION :',
        value: '',
      },
      {
        label: 'Le régime BIC au réel simplifié est-il applicable ?',
        value: formData.regimeBICReelApplicable || 'NON',
        colorScheme:
          formData.regimeBICReelApplicable === 'OUI' ? 'green' : 'red',
      },
    ];

    // Define tab content
    const conditionsTab = (
      <FinanceTable
        title="Statut LMNP - Régime BIC au Réel simplifié - Conditions"
        data={conditionsData}
        showComments={false}
      />
    );

    const projectionsTab = (
      <>
        {formData.projectionData && formData.projectionData.length > 0 ? (
          <BICReelLMNPProjectionTable data={formData.projectionData} />
        ) : null}
      </>
    );

    const plusValuesTab = (
      <>
        {formData.plusValueData && formData.plusValueData.length > 0 ? (
          <BICReelLMNPPlusValueTable data={formData.plusValueData} />
        ) : null}
      </>
    );

    // Define tabs configuration
    const tabs = [
      { label: 'Conditions', content: conditionsTab },
      {
        label: 'Prévisionnel',
        content: projectionsTab,
        isVisible:
          formData.projectionData && formData.projectionData.length > 0,
      },
      {
        label: 'Plus-values',
        content: plusValuesTab,
        isVisible: formData.plusValueData && formData.plusValueData.length > 0,
      },
    ];

    const description =
      "Le régime BIC au réel simplifié est applicable aux loueurs en meublé non professionnels (LMNP) dont les revenus locatifs ne dépassent pas certaines limites. Il permet de déduire les charges réelles et d'amortir les biens, contrairement au régime Micro-BIC qui applique un abattement forfaitaire.";

    return (
      <TaxRegimeFormBase
        title="Prev° BIC-REEL LMNP"
        description={description}
        tabs={tabs}
        warningMessage="Le régime BIC au réel simplifié n'est pas applicable au cas étudié."
        showWarning={formData.regimeBICReelApplicable !== 'OUI'}
      />
    );
  }
);

export default BICReelLMNPForm;
