import type { PlusValueDataItem } from '~/lib/types/bic-reel-lmnp';
import ProjectionTable from '../common/ProjectionTable';
import {
  formatCurrency,
  formatPercentage,
  getValueColor,
} from '../../utils/formatters';

interface BICReelLMNPPlusValueTableProps {
  data: PlusValueDataItem[];
}

const BICReelLMNPPlusValueTable = ({
  data,
}: BICReelLMNPPlusValueTableProps) => {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Prix de Vente',
      accessor: 'salePrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition",
      accessor: 'netAcquisitionPrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value',
      accessor: 'plusValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux Abattement IR',
      accessor: 'incomeTaxAbatementRate',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Abattement IR',
      accessor: 'incomeTaxAbatement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable IR',
      accessor: 'incomeTaxBase',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt sur le Revenu',
      accessor: 'incomeTax19Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux Abattement PS',
      accessor: 'socialChargesAbatementRate',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Abattement PS',
      accessor: 'socialChargesAbatement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable PS',
      accessor: 'socialChargesBase',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux',
      accessor: 'socialCharges17_2Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxation Supplémentaire',
      accessor: 'additionalTaxationAmount',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Imposition',
      accessor: 'totalPlusValueTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Générée',
      accessor: 'totalTreasuryGenerated',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime BIC au réel simplifié - Prévisionnel de reventes - Plus values des particuliers"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de revente du bien à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default BICReelLMNPPlusValueTable;
