import type { ProjectionDataItem } from '~/lib/types/bic-reel-lmnp';
import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency, getValueColor } from '../../utils/formatters';

interface BICReelLMNPProjectionTableProps {
  data: ProjectionDataItem[];
}

const BICReelLMNPProjectionTable = ({
  data,
}: BICReelLMNPProjectionTableProps) => {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Revenus Locatifs',
      accessor: 'rentalIncome',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Charges de Gestion',
      accessor: 'managementCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Autres Charges',
      accessor: 'otherCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements',
      accessor: 'amortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Comptable',
      accessor: 'accountingResult',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Fiscal',
      accessor: 'taxableResult',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt sur le Revenu',
      accessor: 'incomeTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux',
      accessor: 'socialCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CSG Déductible',
      accessor: 'deductibleCSG',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IRPP',
      accessor: 'totalTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Hors Emprunts',
      accessor: 'cashFlowExcludingLoans',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remboursement Emprunt',
      accessor: 'loanRepayments',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Totale',
      accessor: 'totalCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Cumulée',
      accessor: 'cumulativeCashFlow',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime BIC au réel simplifié - Prévisionnel de trésorerie sur 30 ans"
      description="Ce tableau présente une projection financière sur 30 ans de votre investissement en location meublée sous le régime BIC au réel simplifié."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
      footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
    />
  );
};

export default BICReelLMNPProjectionTable;
