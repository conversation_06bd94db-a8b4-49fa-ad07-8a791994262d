import { ProjectionDataItem } from '~/lib/types/micro-bic-chambre-hote';
import ProjectionTable from '../common/ProjectionTable';
import { formatCurrency, getValueColor } from '../../utils/formatters';

interface MicroBICChambreHoteProjectionTableProps {
  data: ProjectionDataItem[];
}

export default function MicroBICChambreHoteProjectionTable({
  data,
}: MicroBICChambreHoteProjectionTableProps) {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Produits',
      accessor: 'produits',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Abattement 50%',
      accessor: 'abattement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat imposable',
      accessor: 'resultatImposable',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt',
      accessor: 'impot',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements sociaux',
      accessor: 'prelevements',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CSG déductible',
      accessor: 'csgDed',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IRPP',
      accessor: 'irpp',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie dégagée',
      accessor: 'tresorerieHorsEmprunts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remboursements emprunts',
      accessor: 'remboursementEmprunt',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie totale',
      accessor: 'tresorerieTotale',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Cumul trésorerie',
      accessor: 'tresorerieCumulee',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime Micro-BIC Chambres d'Hôtes - Prévisionnel de trésorerie"
      description="Ce tableau présente une projection financière incluant les revenus, les charges, les impôts et la trésorerie dégagée chaque année."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
      footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
    />
  );
}
