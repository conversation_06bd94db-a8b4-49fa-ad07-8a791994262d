import { PlusValueDataItem } from '~/lib/types/micro-bic-chambre-hote';
import ProjectionTable from '../common/ProjectionTable';
import {
  formatCurrency,
  formatPercentage,
  getValueColor,
} from '../../utils/formatters';

interface MicroBICChambreHotePlusValueProjectionTableProps {
  data: PlusValueDataItem[];
}

export default function MicroBICChambreHotePlusValueProjectionTable({
  data,
}: MicroBICChambreHotePlusValueProjectionTableProps) {
  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Prix de vente',
      accessor: 'prixVente',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-value',
      accessor: 'plusValue',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-value par part',
      accessor: 'plusValueParPart',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Abattement IR',
      accessor: 'tauxAbattementImpotRevenu',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Base taxable IR',
      accessor: 'baseTaxableImpotRevenu',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt sur le revenu',
      accessor: 'impotRevenu',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Abattement PS',
      accessor: 'tauxAbattementPrelevementsSociaux',
      isNumeric: true,
      format: (value: number) => formatPercentage(value),
    },
    {
      header: 'Base taxable PS',
      accessor: 'baseTaxablePrelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements sociaux',
      accessor: 'prelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxation supplémentaire',
      accessor: 'taxationSupplementaire',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total imposition',
      accessor: 'totalImposition',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie générée',
      accessor: 'tresorerieGeneree',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime Micro-BIC Chambres d'Hôtes - Prévisionnel de reventes - Plus values des particuliers"
      description="Ce tableau présente une projection des plus-values potentielles, incluant les prix de vente, les plus-values générées et les impositions associées."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
}
