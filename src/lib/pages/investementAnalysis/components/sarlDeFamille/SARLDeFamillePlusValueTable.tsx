import { formatCurrency, formatPercentage } from '../../utils/formatters';
import type { PlusValueDataItem } from '~/lib/types/sarl-de-famille';
import ProjectionTable from '../common/ProjectionTable';

interface SARLDeFamillePlusValueTableProps {
  data: PlusValueDataItem[];
}

const SARLDeFamillePlusValueTable = ({
  data,
}: SARLDeFamillePlusValueTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.500';
    if (value < 0) return 'red.500';
    return 'gray.500';
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Prix de Vente',
      accessor: 'salePrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Augmentation Prix Vente',
      accessor: 'salePriceIncrease',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition",
      accessor: 'acquisitionPrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements Pratiqués',
      accessor: 'amortizationPracticed',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Prix d'Acquisition Net",
      accessor: 'netAcquisitionPrice',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Augmentation Prix d'Acquisition",
      accessor: 'acquisitionPriceIncrease',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plus-Value',
      accessor: 'plusValue',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Nombre de Parts',
      accessor: 'numberOfParts',
      isNumeric: true,
    },
    {
      header: 'Plus-Value par Part',
      accessor: 'plusValuePerPart',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Taux Abattement IR',
      accessor: 'incomeTaxAbatementRate',
      isNumeric: true,
      format: formatPercentage,
    },
    {
      header: 'Abattement IR',
      accessor: 'incomeTaxAbatement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable IR',
      accessor: 'incomeTaxBase',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable IR par Part',
      accessor: 'incomeTaxBasePerPart',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Impôt sur le Revenu 19%',
      accessor: 'incomeTax19Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux Abattement PS',
      accessor: 'socialContributionsAbatementRate',
      isNumeric: true,
      format: formatPercentage,
    },
    {
      header: 'Abattement PS',
      accessor: 'socialContributionsAbatement',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Base Taxable PS',
      accessor: 'socialContributionsBase',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux 17.2%',
      accessor: 'socialContributions17_2Percent',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxation Supplémentaire',
      accessor: 'additionalTaxation',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Imposition',
      accessor: 'totalTaxation',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Capital Restant',
      accessor: 'remainingLoan',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Après Remboursement',
      accessor: 'treasuryAfterLoanRepayment',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime SARL DE FAMILLE - Prévisionnel de reventes - Plus values"
      description="Ce tableau présente une simulation des plus-values potentielles et de leur imposition en cas de revente du bien à différentes échéances."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default SARLDeFamillePlusValueTable;
