import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import SARLDeFamilleProjectionTable from './SARLDeFamilleProjectionTable';
import SARLDeFamillePlusValueTable from './SARLDeFamillePlusValueTable';
import {
  SARLDeFamilleFormProps,
  SARLDeFamilleFormData,
} from '~/lib/types/sarl-de-famille';
import { calculateSARLDeFamilleData } from '../../utils/sarlDeFamilleCalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';
import { formatCurrency } from '../../utils/formatters';

const SARLDeFamilleForm = forwardRef<any, SARLDeFamilleFormProps>(
  ({ data, setParentFormData }, ref) => {
    const [formData, setFormData] = useState<SARLDeFamilleFormData>({
      locationsMeublees: 'Non',
      nombreAssociesRequis: 'NON',
      associesLigneDirecte: 'Non',
      associesFreresSoeurs: 'Non',
      associesConjointsPACS: 'Non',
      eligibiliteSARLDeFamille: 'NON',
      revenusLocatifsMeublesChargesComprises: 0,
      nombrePartsFoyerFiscal: 1,
      limiteRevenusLocatifsMeuble: 23000,
      limiteAtteinte: 'NON',
      revenusLocatifsAutres: 'NON',
      concatener: 'NONNON',
      regimeBICReelApplicable: 'OUI',
      regimeSARLDeFamilleApplicable: 'NON',
      indiceMoyen: 0.02,
      projectionData: [],
      plusValueData: [],
    });

    // Calculate values based on the data from previous steps and Excel formulas
    useEffect(() => {
      // Use the calculation function from the separate file
      const calculatedData = calculateSARLDeFamilleData(data);

      // Update the local state
      setFormData(calculatedData);

      // Pass the calculated data to the parent component
      setParentFormData(calculatedData);
    }, [data, setParentFormData]);

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit: () => {
        // Validation logic if needed
        return true;
      },
      setDataFromParent: (parentData: any) => {
        setFormData(parentData?.sarlDeFamilleData || {});
      },
    }));

    // Data for the finance tables
    const conditionsData = [
      {
        label: 'Locations Meublées',
        value: formData.locationsMeublees || 'Non',
      },
      {
        label: 'Nombre associés requis pour SARL DE FAMILLE ?',
        value: formData.nombreAssociesRequis || 'NON',
      },
      {
        label:
          "Associés membres d'une même famille en ligne directe ascendante ou descendante",
        value: formData.associesLigneDirecte || 'Non',
      },
      {
        label: 'Associés étant frère(s) et/ou sœur(s)',
        value: formData.associesFreresSoeurs || 'Non',
      },
      {
        label:
          'Associés - Entre conjoints et partenaires liés par un pacte civil de solidarité',
        value: formData.associesConjointsPACS || 'Non',
      },
      {
        label: 'Eligibilité SARL DE FAMILLE',
        value: formData.eligibiliteSARLDeFamille || 'NON',
        colorScheme:
          formData.eligibiliteSARLDeFamille === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'Revenus locatifs meublés charges comprises du foyer fiscal',
        value: formatCurrency(formData.revenusLocatifsMeublesChargesComprises),
      },
      {
        label: 'Nombre de parts du foyer fiscal',
        value: formData.nombrePartsFoyerFiscal.toString(),
      },
      {
        label: 'Limite des revenus locatifs issus de la location en meublée',
        value: formatCurrency(formData.limiteRevenusLocatifsMeuble),
      },
      {
        label: 'Limite N°1 atteinte ?',
        value: formData.limiteAtteinte || 'NON',
        colorScheme: formData.limiteAtteinte === 'NON' ? 'green' : 'red',
      },
      {
        label: 'Si Limite précédente atteinte :',
        value: '',
      },
      {
        label:
          'Les revenus issus de la location en meublée représentent-ils plus de la moitié des revenus globaux du foyer fiscal ?',
        value: formData.revenusLocatifsAutres || 'NON',
        colorScheme: formData.revenusLocatifsAutres === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'CONCATENER',
        value: formData.concatener ? 'Oui' : 'Non',
      },
      {
        label: 'CONCLUSION :',
        value: '',
      },
      {
        label:
          'Le régime BIC au réel simplifié est-il applicable en bénéficiant des avantages du statut LMNP ?',
        value: formData.regimeBICReelApplicable || 'NON',
        colorScheme:
          formData.regimeBICReelApplicable === 'OUI' ? 'green' : 'red',
      },
      {
        label: 'CONCATENER',
        value:
          formData.eligibiliteSARLDeFamille + formData.regimeBICReelApplicable,
      },
      {
        label:
          "La SARL de famille à l'IR avec les avantages du statut LMNP est-elle possible ?",
        value: formData.regimeSARLDeFamilleApplicable || 'NON',
        colorScheme:
          formData.regimeSARLDeFamilleApplicable === 'OUI' ? 'green' : 'red',
      },
    ];

    const travailleDeductiblesData = [
      {
        label: 'Application indice',
        value: formData.indiceMoyen > 0 ? 'OUI' : 'NON',
      },
      {
        label: 'Travaux déductibles :',
        value: '',
      },
      {
        label: "Travaux d'amélioration",
        value: 'NON',
      },
      {
        label: "Travaux de réparation et d'entretien",
        value: 'NON',
      },
      {
        label: 'Travaux non-déductibles :',
        value: '',
      },
      {
        label: 'Travaux de construction/reconstruction/agrandissement',
        value: 'NON',
      },
      {
        label: 'Travaux imputable déficit foncier',
        value: 'NON',
      },
    ];

    // Define tab content
    const conditionsTab = (
      <>
        <FinanceTable
          title="SARL DE FAMILLE - BIC au Réel simplifié en bénéficiant avantages du statut LMNP - Conditions"
          data={conditionsData}
          showComments={false}
        />
        <FinanceTable
          title="Travaux déductibles"
          data={travailleDeductiblesData}
          showComments={false}
        />
      </>
    );

    const projectionsTab = (
      <>
        {formData.projectionData && formData.projectionData.length > 0 ? (
          <SARLDeFamilleProjectionTable data={formData.projectionData} />
        ) : null}
      </>
    );

    const plusValuesTab = (
      <>
        {formData.plusValueData && formData.plusValueData.length > 0 ? (
          <SARLDeFamillePlusValueTable data={formData.plusValueData} />
        ) : null}
      </>
    );

    // Define tabs configuration
    const tabs = [
      { label: 'Conditions', content: conditionsTab },
      {
        label: 'Prévisionnel',
        content: projectionsTab,
        isVisible:
          formData.projectionData && formData.projectionData.length > 0,
      },
      {
        label: 'Plus-values',
        content: plusValuesTab,
        isVisible: formData.plusValueData && formData.plusValueData.length > 0,
      },
    ];

    const description =
      "Le régime SARL de famille à l'IR avec les avantages du statut LMNP s'applique aux sociétés dont les associés sont membres d'une même famille et qui louent des logements meublés. Ce régime permet de bénéficier de la transparence fiscale de l'IR tout en ayant les avantages du statut LMNP, notamment la déduction des charges réelles et l'amortissement des biens.";

    return (
      <TaxRegimeFormBase
        title="Prev° SARL DE FAMILLE"
        description={description}
        tabs={tabs}
        warningMessage="Le régime SARL de famille à l'IR avec les avantages du statut LMNP n'est pas applicable au cas étudié."
        showWarning={formData.regimeSARLDeFamilleApplicable !== 'OUI'}
      />
    );
  }
);

export default SARLDeFamilleForm;
