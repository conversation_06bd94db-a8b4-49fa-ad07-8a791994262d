import { formatCurrency } from '../../utils/formatters';
import type { ProjectionDataItem } from '~/lib/types/sarl-de-famille';
import ProjectionTable from '../common/ProjectionTable';

interface SARLDeFamilleProjectionTableProps {
  data: ProjectionDataItem[];
}

const SARLDeFamilleProjectionTable = ({
  data,
}: SARLDeFamilleProjectionTableProps) => {
  // Function to determine color based on value
  const getValueColor = (value: number) => {
    if (value > 0) return 'green.500';
    if (value < 0) return 'red.500';
    return 'gray.500';
  };

  // Define columns for the table
  const columns = [
    {
      header: 'Année',
      accessor: 'year',
      isNumeric: true,
    },
    {
      header: 'Revenus Locatifs',
      accessor: 'rentalIncome',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Charges d'entretien",
      accessor: 'maintenanceCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Charges Locatives',
      accessor: 'rentalCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Assurance Non Occupant',
      accessor: 'nonOccupantInsurance',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Assurance Emprunteur',
      accessor: 'loanInsurance',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Frais d'emprunts",
      accessor: 'loanFees',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxes Foncières',
      accessor: 'propertyTaxes',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CFE',
      accessor: 'cfe',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: "Intérêts d'emprunt",
      accessor: 'loanInterest',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Frais bancaires',
      accessor: 'bankFees',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Charges Gestion',
      accessor: 'totalManagementCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Honoraires comptables',
      accessor: 'accountingFees',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Honoraires juridiques',
      accessor: 'legalFees',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Frais de domiciliation',
      accessor: 'domiciliationFees',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Rémunération du gérant',
      accessor: 'managerRemuneration',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Total Autres Charges',
      accessor: 'totalOtherCharges',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Amortissements',
      accessor: 'amortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Cumul Amortissements',
      accessor: 'cumulativeAmortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Comptable',
      accessor: 'accountingResult',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Amort. > Résultat',
      accessor: 'isAmortizationGreaterThanResult',
      isNumeric: false,
    },
    {
      header: 'Résultat Comptable BIC',
      accessor: 'accountingResultWithAmortization',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Amort. reportés',
      accessor: 'reportedAmortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Cumul Amort. reportés',
      accessor: 'cumulativeReportedAmortization',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat Fiscal',
      accessor: 'fiscalResult',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Impôt sur le revenu',
      accessor: 'incomeTax',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Prélèvements Sociaux',
      accessor: 'socialContributions',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CSG Déductible',
      accessor: 'csgDeduction',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IRPP',
      accessor: 'irpp',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Cotisations sociales minimales',
      accessor: 'minimumSocialContributions',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Dégagée',
      accessor: 'treasuryWithoutLoans',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Remb. emprunt 15 ans',
      accessor: 'loanRepayment15Years',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remb. emprunt 20 ans',
      accessor: 'loanRepayment20Years',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remb. emprunt 25 ans',
      accessor: 'loanRepayment25Years',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie Totale',
      accessor: 'totalTreasury',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie Cumulée',
      accessor: 'cumulativeTreasury',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title="Régime SARL DE FAMILLE - Prévisionnel de trésorerie sur 30 ans"
      description="Ce tableau présente une simulation de la trésorerie dégagée sur 30 ans dans le cadre d'une SARL de famille à l'IR avec les avantages du statut LMNP."
      data={data}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default SARLDeFamilleProjectionTable;
