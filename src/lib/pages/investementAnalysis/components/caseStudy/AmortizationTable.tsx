import React from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useBreakpointValue,
  NumberInput,
  NumberInputField,
  Text,
} from '@chakra-ui/react';


interface AmortizationItem {
  label: string;
  quotePart: string;
  duration: number;
  amount: number;
  amortization: number;
  isEditable?: boolean;
}

interface AmortizationTableProps {
  items: AmortizationItem[];
  onAmountChange?: (label: string, value: number) => void;
}

const AmortizationTable: React.FC<AmortizationTableProps> = ({ items, onAmountChange }) => {
  const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);
  const totalAmortization = items.reduce((sum, item) => sum + item.amortization, 0);

  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Box mb={6} overflowX="auto">
      <Heading size="md" textAlign="center" color="teal.600" mb={4}>
        Simulation d'amortissement annuel
      </Heading>
      <Table variant="simple" size={isMobile ? "sm" : "md"} width="100%">
        <Thead>
          <Tr>
            <Th>Élément</Th>
            <Th>Quote Part</Th>
            <Th>Durée</Th>
            <Th isNumeric>Montant</Th>
            <Th isNumeric>Amortissement</Th>
          </Tr>
        </Thead>
        <Tbody>
          {items.map((item, idx) => (
            <Tr key={idx} _hover={{ bg: 'gray.50', transition: 'background 0.2s' }}>
              <Td fontSize="sm" color="gray.700">{item.label}</Td>
              <Td fontSize="sm">{item.quotePart}</Td>
              <Td fontSize="sm">{item.duration}</Td>
              <Td isNumeric fontSize="sm">
                {item.isEditable && onAmountChange ? (
                  <NumberInput
                    value={item.amount}
                    onChange={(_, val) => onAmountChange(item.label, val)}
                    min={0}
                    size="sm"
                    maxW="120px"
                    ml="auto"
                  >
                    <NumberInputField textAlign="right" />
                  </NumberInput>
                ) : (
                  <Text fontWeight="semibold" color="gray.800">
                    {item.amount.toLocaleString()} €
                  </Text>
                )}
              </Td>
              <Td isNumeric fontSize="sm">
                <Text fontWeight="semibold" color="gray.800">
                  {item.amortization.toFixed(0)} €
                </Text>
              </Td>
            </Tr>
          ))}
          <Tr fontWeight="bold" bg="gray.50">
            <Td>Total</Td>
            <Td>100%</Td>
            <Td></Td>
            <Td isNumeric>
              <Text fontWeight="bold" color="gray.800">
                {totalAmount.toLocaleString()} €
              </Text>
            </Td>
            <Td isNumeric>
              <Text fontWeight="bold" color="gray.800">
                {totalAmortization.toFixed(0)} €
              </Text>
            </Td>
          </Tr>
        </Tbody>
      </Table>
    </Box>
  );
};

export default AmortizationTable;
