import React from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Heading,
  useBreakpointValue,
  Text,
} from '@chakra-ui/react';

type AnnualDepreciation = {
  label: string;
  start: number | null;
  end: number | null;
  annualAmount: number | null;
};

type AnnualDepreciationTableProps = {
  data: AnnualDepreciation[];
};

const formatCurrency = (value: number | null) => {
  if (value === null || isNaN(value)) return '-';
  return value.toLocaleString('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
  });
};

const AnnualDepreciationTable: React.FC<AnnualDepreciationTableProps> = ({ data }) => {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Box mb={6} overflowX="auto">
      <Heading size="md" textAlign="center" color="teal.600" mb={4}>
        Amortissement annuel
      </Heading>
      <Table variant="simple" size={isMobile ? "sm" : "md"} width="100%">
        <Thead>
          <Tr>
            <Th>Années</Th>
            <Th isNumeric>Année début</Th>
            <Th isNumeric>Année fin</Th>
            <Th isNumeric>Amortissement annuel</Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map((entry, index) => (
            <Tr key={index} _hover={{ bg: 'gray.50', transition: 'background 0.2s' }}>
              <Td fontSize="sm" color="gray.700">{entry.label}</Td>
              <Td isNumeric fontSize="sm">
                <Text fontWeight="semibold" color="gray.800">
                  {entry.start ?? '-'}
                </Text>
              </Td>
              <Td isNumeric fontSize="sm">
                <Text fontWeight="semibold" color="gray.800">
                  {entry.end ?? '-'}
                </Text>
              </Td>
              <Td isNumeric fontSize="sm">
                <Text fontWeight="semibold" color="gray.800">
                  {formatCurrency(entry.annualAmount)}
                </Text>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default AnnualDepreciationTable;
