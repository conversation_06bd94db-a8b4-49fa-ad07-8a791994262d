import { Box, Text } from '@chakra-ui/react';
import { useEffect } from 'react';
import { InvestmentInputs } from '../../hooks/useInvestmentCalculator';
import { getTaxBracket } from '~/lib/pages/buyerAssessment/components/couple/enconomicAndFiscal/TaxCalculation';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
 
interface InvestmentInputProps {
  data: InvestmentInputs;
  onChange: (name: string, value: any) => void;
  loyersAnnuelsChargesComprises: number;
  dontTerrain: number;
  dontConstruction: number;
}

const InvestmentInput = ({
  data,
  onChange,
  loyersAnnuelsChargesComprises,
  dontTerrain,
  dontConstruction,
}: InvestmentInputProps) => {
  // Calculate TMI based on revenuFiscalReference and nombreParts
  const calculateTMI = () => {
    if (data.revenuFiscalReference && data.nombreParts) {
      // Calculate income per part
      const incomePerPart = data.revenuFiscalReference / data.nombreParts;

      // Calculate tax per part using the TaxCalculation function
      const tmi = getTaxBracket(incomePerPart);

      // Update TMI in the data
      onChange('tauxMarginalImposition', Number(tmi.toFixed(2)));
    }
  };

  // Calculate montantInvestissement based on coutAcquisition, travaux
  const calculateMontantInvestissement = () => {
    const montant = data.coutAcquisition + data.travaux;
    onChange('montantInvestissement', montant);
  };

  // Calculate TMI on component mount and when dependencies change
  useEffect(() => {
    calculateTMI();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.revenuFiscalReference, data.nombreParts]);

  // Calculate montantInvestissement when dependencies change
  useEffect(() => {
    calculateMontantInvestissement();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.coutAcquisition, data.travaux]);



  // Define data sections similar to ProjectAnalysis
  const investmentData = [
    {
      label: "Montant investissement (frais notaire compris)",
      value: data.montantInvestissement,
      isEditable: false,
      customRender: () => (
        <Text fontWeight="semibold" color="gray.800">
          {data.montantInvestissement.toLocaleString()} €
        </Text>
      ),
    },
    {
      label: "Achat + frais d'acquisition",
      value: data.coutAcquisition,
      isEditable: true,
      onChange: (val: string) => {
        onChange('coutAcquisition', Number(val));
        calculateMontantInvestissement();
      },
    },
    {
      label: "Travaux",
      value: data.travaux,
      isEditable: true,
      onChange: (val: string) => {
        onChange('travaux', Number(val));
        calculateMontantInvestissement();
      },
    },
    {
      label: "Dont terrain",
      value: `${(data.quotePartTerrain * 100).toFixed(0)}% - ${dontTerrain.toLocaleString()} €`,
      isEditable: false,
    },
    {
      label: "Dont construction",
      value: `${dontConstruction.toLocaleString()} €`,
      isEditable: false,
    },
  ];

  const fiscalData = [
    {
      label: "Revenu Fiscal de Référence du foyer",
      value: data.revenuFiscalReference,
      isEditable: true,
      onChange: (val: string) => {
        onChange('revenuFiscalReference', Number(val));
        calculateTMI();
      },
    },
    {
      label: "Taux Marginal d'Imposition (TMI)",
      value: (data.tauxMarginalImposition * 100).toFixed(1) + "%",
      isEditable: false,
    },
    {
      label: "Nombre parts foyer fiscal",
      value: data.nombreParts,
      isEditable: true,
      onChange: (val: string) => {
        onChange('nombreParts', Number(val));
        calculateTMI();
      },
    },
  ];

  const rentalData = [
    {
      label: "Loyers annuels hors charges projet",
      value: data.loyersAnnuelHorsCharges,
      isEditable: true,
      onChange: (val: string) => onChange('loyersAnnuelHorsCharges', Number(val)),
    },
    {
      label: "Charges Comprises",
      value: data.chargesComprises,
      isEditable: true,
      onChange: (val: string) => onChange('chargesComprises', Number(val)),
    },
    {
      label: "Loyers annuels charges comprises",
      value: `${loyersAnnuelsChargesComprises.toLocaleString()} €`,
      isEditable: false,
    },
  ];



  return (
    <Box p={{ base: 2, md: 4 }}>
      <FinanceTable
        title="Données d'investissement"
        data={investmentData}
        showComments={false}
      />
      <FinanceTable
        title="Données fiscales"
        data={fiscalData}
        showComments={false}
      />
      <FinanceTable
        title="Données locatives"
        data={rentalData}
        showComments={false}
      />
    </Box>
  );
};

export default InvestmentInput;