import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
} from '@chakra-ui/react';

const ChargesTable = () => {
  const chargesData = [
    { label: 'Charges Locatives', amount: 1800 },
    { label: 'Assurances Non Occupant', amount: 300 },
    { label: 'Assurances Emprunteur', amount: 96 },
    { label: "Charges d'entretien", amount: 140 },
    { label: 'Taxes Foncières', amount: 1450 },
    { label: "Frais d'agences gestions locatives", amount: 3000 },
    { label: 'Frais dossiers emprunt', amount: 1000 },
    { label: 'CFE', amount: 400 },
    { label: 'Frais Bancaires', amount: 40 },
    { label: "Intérêts d'emprunt", amount: 2559 },
    { label: 'Travaux', amount: 3000 },
    { label: 'Honoraires comptables', amount: 0 },
    { label: 'Honoraires juridiques (approb° comptes)', amount: 0 },
    { label: 'Frais de domiciliation', amount: 0 },
    { label: 'Rémunération du gérant (dans le cadre SCI)', amount: 0 },
  ];

  return (
    <TableContainer>
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Charges annuelles (non fluctuantes)</Th>
            <Th isNumeric>Année 1</Th>
          </Tr>
        </Thead>
        <Tbody>
          {chargesData.map((charge, index) => (
            <Tr key={index}>
              <Td>{charge.label}</Td>
              <Td isNumeric>{charge.amount}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default ChargesTable;
