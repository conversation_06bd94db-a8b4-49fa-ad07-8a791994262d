import { Box } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import AnnualChargesTable from './AnnualCharge';
import AmortizationTable from './AmortizationTable';
import AnnualDepreciationTable from './AnnualDepreciationTable';
import InvestmentInput from './InvestmentInput';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import useInvestmentCalculator from '../../hooks/useInvestmentCalculator';
import { forwardRef, useImperativeHandle, useEffect, useState } from 'react';
import { SetParentFormDataType } from '~/lib/types/buyer-assessment';
import { IInvestementCalculatorData } from '~/lib/types/investement-analysis';

// Wrap Box with motion for animations
const MotionBox = motion(Box);

interface InvestmentCalculatorProps {
  setParentFormData: SetParentFormDataType;
  data?: IInvestementCalculatorData;
}

const InvestmentCalculator = forwardRef(
  (props: InvestmentCalculatorProps, ref) => {
    // Use the centralized hook for all data management
    const { inputs, results, handleInputChange, setInputs } =
      useInvestmentCalculator();
    const [isInitialized, setIsInitialized] = useState(false);

    // Initialize with data from parent if available (only once)
    useEffect(() => {
      if (props.data && Object.keys(props.data).length > 0 && !isInitialized) {
        setInputs(prevInputs => ({...prevInputs, ...props.data}));
        setIsInitialized(true);
      }
    }, [props.data, setInputs, isInitialized]);

    // Debounced auto-save to prevent data loss without interfering with user input
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        props.setParentFormData(inputs);
      }, 1000); // Save after 1 second of inactivity

      return () => clearTimeout(timeoutId);
    }, [inputs, props.setParentFormData]);

    // Derived data for annual depreciation
    const annualDepreciationData = [
      {
        label: 'Années 1 à 5',
        start: 1,
        end: 5,
        annualAmount: results.amortissementTotalAnnuel,
      },
      {
        label: 'Années 6 à 8',
        start: 6,
        end: 8,
        annualAmount:
          results.amortissementTotalAnnuel - results.amortissementAnnuelMeubles,
      },
      {
        label: 'Années 9 à 15',
        start: 9,
        end: 15,
        annualAmount:
          results.amortissementTotalAnnuel -
          results.amortissementAnnuelMeubles -
          results.amortissementAnnuelAgencements,
      },
      {
        label: 'Années 16 à 20',
        start: 16,
        end: 20,
        annualAmount:
          results.amortissementAnnuelGrosOeuvre +
          results.amortissementAnnuelFacade,
      },
      {
        label: 'Années 21 à 50',
        start: 21,
        end: 50,
        annualAmount: results.amortissementAnnuelGrosOeuvre,
      },
      { label: '> 50 ans', start: null, end: null, annualAmount: null },
    ];

    // Amortization data for the table
    const amortizationData = [
      {
        label: 'Terrain',
        quotePart: `${inputs.quotePartTerrain * 100}%`,
        duration: 0,
        amount: results.valeurTerrain,
        amortization: 0,
      },
      {
        label: 'Gros Œuvre - Structure',
        quotePart: `${inputs.quotePartGrosOeuvre * 100}%`,
        duration: inputs.dureeGrosOeuvre,
        amount: results.valeurGrosOeuvre,
        amortization: results.amortissementAnnuelGrosOeuvre,
      },
      {
        label: 'Façade / Étanchéité',
        quotePart: `${inputs.quotePartFacade * 100}%`,
        duration: inputs.dureeFacade,
        amount: results.valeurFacade,
        amortization: results.amortissementAnnuelFacade,
      },
      {
        label: 'Installations TGT',
        quotePart: `${inputs.quotePartIGT * 100}%`,
        duration: inputs.dureeIGT,
        amount: results.valeurIGT,
        amortization: results.amortissementAnnuelIGT,
      },
      {
        label: 'Agencements',
        quotePart: `${inputs.quotePartAgencements * 100}%`,
        duration: inputs.dureeAgencements,
        amount: results.valeurAgencements,
        amortization: results.amortissementAnnuelAgencements,
      },
      {
        label: 'Meubles',
        quotePart: '100%',
        duration: inputs.dureeMeubles,
        amount: inputs.valeurMeubles,
        amortization: results.amortissementAnnuelMeubles,
        isEditable: true,
      },
    ];

    // Used for responsive design in child components

    // Prepare fees data for FinanceTable
    const feesData = [
      {
        label: 'Honoraires comptables',
        value: inputs.fraisComptables || 0,
        isEditable: true,
        onChange: (val: string) =>
          handleInputChange('fraisComptables', Number(val)),
      },
      {
        label: 'Honoraires juridiques',
        value: inputs.fraisJuridiques || 0,
        isEditable: true,
        onChange: (val: string) =>
          handleInputChange('fraisJuridiques', Number(val)),
      },
      {
        label: 'Rémunération du gérant',
        value: inputs.fraisGerant || 0,
        isEditable: true,
        onChange: (val: string) =>
          handleInputChange('fraisGerant', Number(val)),
      },
    ];
    const onSubmit = () => {
      props.setParentFormData(inputs);
      return true;
    };
    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      onSubmit,
    }));
    return (
      <Box
        p={{ base: 2, md: 6 }}
        borderRadius={8}
        role="main"
        aria-label="Analyse d'investissement"
      >
        {/* Investment Inputs Section */}
        <MotionBox
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 0 * 0.1 }}
        >
          <InvestmentInput
            data={inputs}
            onChange={handleInputChange}
            loyersAnnuelsChargesComprises={
              inputs.loyersAnnuelHorsCharges + inputs.chargesComprises
            }
            dontTerrain={results.valeurTerrain}
            dontConstruction={results.valeurConstruction}
          />
        </MotionBox>

        {/* Annual Charges Section */}
        <MotionBox
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 1 * 0.1 }}
        >
          <AnnualChargesTable
            inputs={inputs}
            handleInputChange={handleInputChange}
          />
        </MotionBox>

        {/* Fees Section */}
        <MotionBox
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 2 * 0.1 }}
        >
          <FinanceTable
            title="Honoraires & Rémunérations"
            data={feesData}
            showComments={false}
          />
        </MotionBox>

        {/* Amortization Table Section */}
        <MotionBox
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 3 * 0.1 }}
        >
          <AmortizationTable
            items={amortizationData}
            onAmountChange={(label, value) => {
              if (label === 'Meubles') {
                handleInputChange('valeurMeubles', value);
              }
            }}
          />
        </MotionBox>

        {/* Annual Depreciation Section */}
        <MotionBox
          mb={6}
          bg="white"
          borderRadius={8}
          boxShadow="sm"
          overflow="hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 4 * 0.1 }}
        >
          <AnnualDepreciationTable data={annualDepreciationData} />
        </MotionBox>
      </Box>
    );
  }
);
export default InvestmentCalculator;
