import { ProjectionDataItem } from '~/lib/types/fonciers-reels';
import ProjectionTable from '../common/ProjectionTable';
import {
  formatCurrency,
  formatPercentage,
  getValueColor,
} from '../../utils/formatters';

interface FonciersReelsProjectionTableProps {
  data: ProjectionDataItem[];
  title?: string;
}

const FonciersReelsProjectionTable = ({
  data,
  title = 'Prévisionnel sur 30 ans',
}: FonciersReelsProjectionTableProps) => {
  // Map the data to match our common component's expected format
  const mappedData = data.map((item) => ({
    year: item.annee,
    revenusLocatifs: item.revenusLocatifsHorsCharges,
    travaux: item.travaux,
    taxeFonciere: item.taxeFonciere,
    chargesLocatives: item.chargesLocatives,
    interetsEmprunts: item.interetsEmprunts,
    fraisEmprunts: item.fraisEmprunts,
    fraisAgences: item.fraisAgences,
    primesAssurances: item.primesAssurances,
    resultatFonciers: item.resultatFonciers,
    resultatCumulatifs: item.resultatFonciersCumulatifs,
    plafondDeficitImputable: item.plafondDeficitImputable,
    resultatReportables: item.resultatFonciersReportables,
    tauxMarginal: item.tauxMarginalImposition,
    prelevementsSociaux: item.prelevementsSociaux,
    csgDeductible: item.csgDeductible,
    irpp: item.irpp,
    tresorerieDegagee: item.tresorerieDegageeHorsEmprunts,
    remboursementsEmprunts: item.remboursementsEmprunts,
    tresorerieTotale: item.tresorerieTotaleDegagee,
    tresorerieAvecEcoIR: item.tresorerieTotaleDegageeAvecEcoIR,
    tresorerieCumulee: item.tresorerieTotalCumul,
  }));

  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Revenus locatifs',
      accessor: 'revenusLocatifs',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Travaux',
      accessor: 'travaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taxe foncière',
      accessor: 'taxeFonciere',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Charges locatives',
      accessor: 'chargesLocatives',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Intérêts emprunts',
      accessor: 'interetsEmprunts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Frais emprunts',
      accessor: 'fraisEmprunts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Frais agences',
      accessor: 'fraisAgences',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Primes assurances',
      accessor: 'primesAssurances',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat fonciers',
      accessor: 'resultatFonciers',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat cumulatifs',
      accessor: 'resultatCumulatifs',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Plafond déficit imputable',
      accessor: 'plafondDeficitImputable',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Résultat reportables',
      accessor: 'resultatReportables',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Taux marginal',
      accessor: 'tauxMarginal',
      isNumeric: true,
      format: (value: number) => formatPercentage(value, 2, 2),
    },
    {
      header: 'Prélèvements sociaux',
      accessor: 'prelevementsSociaux',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'CSG déductible',
      accessor: 'csgDeductible',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'IRPP',
      accessor: 'irpp',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie dégagée',
      accessor: 'tresorerieDegagee',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Remboursements emprunts',
      accessor: 'remboursementsEmprunts',
      isNumeric: true,
      format: formatCurrency,
    },
    {
      header: 'Trésorerie totale',
      accessor: 'tresorerieTotale',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie avec éco IR',
      accessor: 'tresorerieAvecEcoIR',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie cumulée',
      accessor: 'tresorerieCumulee',
      isNumeric: true,
      format: formatCurrency,
      colorCondition: getValueColor,
    },
  ];

  return (
    <ProjectionTable
      title={title}
      description="Ce tableau présente une projection financière détaillée du régime Fonciers Réels, incluant les revenus, les charges, les impôts et la trésorerie dégagée chaque année."
      data={mappedData}
      columns={columns}
      defaultYearsToShow={10}
    />
  );
};

export default FonciersReelsProjectionTable;
