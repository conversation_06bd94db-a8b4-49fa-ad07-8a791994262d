// No need for Chakra UI imports with the base component
import { Text } from '@chakra-ui/react';
import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import {
  FonciersReelsFormProps,
  FonciersReelsFormData,
} from '~/lib/types/fonciers-reels';
import { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis.tsx';
import { calculateFonciersReelsData } from '../../utils/fonciersReelsCalculations';
import FonciersReelsProjectionTable from './FonciersReelsProjectionTable';
import FonciersReelsPlusValueTable from './FonciersReelsPlusValueTable';
import FonciersReelsSCIPartsTable from './FonciersReelsSCIPartsTable';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';

const FonciersReelsForm = forwardRef((props: FonciersReelsFormProps, ref) => {
  const { data, setParentFormData } = props;
  const [formData, setFormData] = useState<Partial<FonciersReelsFormData>>(
    data?.fonciersReelsData || {}
  );

  // No need for fontSize with the base component

  // Format number as currency
  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return '0 €';
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(value);
  };

  // Calculate values based on the data from previous steps
  useEffect(() => {
    // Use the calculation function from the separate file
    const calculatedData = calculateFonciersReelsData(data);

    // Update the local state
    setFormData(calculatedData);

    // Pass the calculated data to the parent component
    setParentFormData(calculatedData);
  }, [data]);

  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    onSubmit: () => {
      // Validation logic if needed
      return true;
    },
    setDataFromParent: (parentData: FormDataInvestementAnalysistSteps) => {
      setFormData(parentData?.fonciersReelsData || {});
    },
  }));

  // Data for the finance tables
  const conditionsData = [
    {
      label:
        'Locations nues (usage habitation, locaux commerciaux, professionnels)',
      value: formData.locationsNues || 'Oui',
    },
    {
      label: 'Revenus locatifs hors charges du projet',
      value: formatCurrency(formData.loyersAnnuels || 0),
    },
    {
      label: 'Autres revenus locatifs en Nue ?',
      value: formData.autresRevenusLocatifsNue || 'Non',
    },
    {
      label: 'Micro-fonciers',
      value: formatCurrency(formData.microFonciers || 0),
    },
    {
      label: 'Fonciers au régime réel',
      value: formatCurrency(formData.foncierReels || 0),
    },
    {
      label: 'Régime Fonciers Réel possible ?',
      value: formData.regimeFonciersReelPossible ? 'Oui' : 'Non',
      colorScheme: formData.regimeFonciersReelPossible ? 'green' : 'red',
    },
    {
      label: 'CONCATENER',
      value: formData.concatener ? 'Oui' : 'Non',
    },
    {
      label: 'Régime Fonciers Réel applicable ?',
      value: formData.regimeFonciersReelApplicable ? 'Oui' : 'Non',
      colorScheme: formData.regimeFonciersReelApplicable ? 'green' : 'red',
    },
    {
      label: 'CFE applicable ?',
      value: formData.cfeApplicable ? 'Oui' : 'Non',
      colorScheme: formData.cfeApplicable ? 'red' : 'green',
    },
  ];

  // Define tab content
  const conditionsTab = (
    <FinanceTable
      title="Régime Fonciers Réels - Conditions"
      data={conditionsData}
      showComments={false}
    />
  );

  const previsionnelTab = (
    <>
      {formData.projectionData && formData.projectionData.length > 0 ? (
        <FonciersReelsProjectionTable data={formData.projectionData} />
      ) : (
        <Text color="gray.600" mb={4}>
          Le prévisionnel de trésorerie sur 30 ans sera implémenté
          ultérieurement.
        </Text>
      )}
    </>
  );

  const plusValuesTab = (
    <>
      {formData.plusValueData && formData.plusValueData.length > 0 ? (
        <FonciersReelsPlusValueTable data={formData.plusValueData} />
      ) : (
        <Text color="gray.600" mb={4}>
          Le prévisionnel de revente bien immobilier - Plus values des
          particuliers sera implémenté ultérieurement.
        </Text>
      )}
    </>
  );

  const sciPartsTab = (
    <>
      {formData.sciPartsData && formData.sciPartsData.length > 0 ? (
        <FonciersReelsSCIPartsTable data={formData.sciPartsData} />
      ) : (
        <Text color="gray.600" mb={4}>
          Le prévisionnel cession parts sociales SCI IR - Plus values des
          particuliers sera implémenté ultérieurement.
        </Text>
      )}
    </>
  );

  // Define tabs configuration
  const tabs = [
    { label: 'Conditions', content: conditionsTab },
    { label: 'Prévisionnel', content: previsionnelTab },
    { label: 'Plus-values', content: plusValuesTab },
    { label: 'Parts SCI', content: sciPartsTab },
  ];

  const description =
    'Le régime Fonciers Réels permet de déduire les charges réelles de vos revenus fonciers, contrairement au régime Micro-Foncier qui applique un abattement forfaitaire. Ce régime est obligatoire si vos revenus fonciers dépassent 15 000€ par an.';

  return (
    <TaxRegimeFormBase
      title="Prévision Fonciers-Réels"
      description={description}
      tabs={tabs}
      warningMessage="Le régime Fonciers Réels n'est pas applicable au cas étudié."
      showWarning={!formData.regimeFonciersReelApplicable}
    />
  );
});

export default FonciersReelsForm;
