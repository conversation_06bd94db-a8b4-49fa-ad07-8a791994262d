import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  useBreakpointValue,
  Select,
  Flex,
} from '@chakra-ui/react';
import { useState } from 'react';
import { SCIPartsDataItem } from '~/lib/types/fonciers-reels';

interface FonciersReelsSCIPartsTableProps {
  data: SCIPartsDataItem[];
  title?: string;
}

const FonciersReelsSCIPartsTable = ({
  data,
  title = 'Prévisionnel Cession Parts SCI sur 30 ans',
}: FonciersReelsSCIPartsTableProps) => {
  const [yearsToShow, setYearsToShow] = useState<number>(10);
  const fontSize = useBreakpointValue({ base: 'xs', md: 'sm' });
  const headerFontSize = useBreakpointValue({ base: 'sm', md: 'md' });

  // Format number as currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format number as percentage
  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // If no data, show a message
  if (!data || data.length === 0) {
    return (
      <Box>
        <Text fontSize={headerFontSize} fontWeight="bold" mb={4}>
          {title}
        </Text>
        <Text>Aucune donnée disponible pour le moment.</Text>
      </Box>
    );
  }

  // Filter data based on years to show
  const filteredData = data.slice(0, yearsToShow);

  return (
    <Box>
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <Text fontSize={headerFontSize} fontWeight="bold">
          {title}
        </Text>
        <Select
          value={yearsToShow}
          onChange={(e) => setYearsToShow(Number(e.target.value))}
          width="auto"
          size="sm"
        >
          <option value={5}>5 ans</option>
          <option value={10}>10 ans</option>
          <option value={15}>15 ans</option>
          <option value={20}>20 ans</option>
          <option value={25}>25 ans</option>
          <option value={30}>30 ans</option>
        </Select>
      </Flex>

      <Box overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead>
            <Tr bg="teal.50">
              <Th fontSize={fontSize}>Année</Th>
              <Th fontSize={fontSize}>Valeur actif réel</Th>
              <Th fontSize={fontSize}>Trésorerie disponible</Th>
              <Th fontSize={fontSize}>Emprunt</Th>
              <Th fontSize={fontSize}>Compte courant</Th>
              <Th fontSize={fontSize}>Prix cession parts</Th>
              <Th fontSize={fontSize}>Augmentation prix vente</Th>
              <Th fontSize={fontSize}>Valeur origine parts</Th>
              <Th fontSize={fontSize}>Prise en compte gains fiscaux</Th>
              <Th fontSize={fontSize}>Plus-value</Th>
              <Th fontSize={fontSize}>Nombre parts</Th>
              <Th fontSize={fontSize}>Plus-value par part</Th>
              <Th fontSize={fontSize}>Taux abattement IR</Th>
              <Th fontSize={fontSize}>Abattement IR</Th>
              <Th fontSize={fontSize}>Base taxable IR</Th>
              <Th fontSize={fontSize}>Impôt sur le revenu</Th>
              <Th fontSize={fontSize}>Taux abattement PS</Th>
              <Th fontSize={fontSize}>Abattement PS</Th>
              <Th fontSize={fontSize}>Base taxable PS</Th>
              <Th fontSize={fontSize}>Prélèvements sociaux</Th>
              <Th fontSize={fontSize}>Taxation supplémentaire</Th>
              <Th fontSize={fontSize}>Total imposition</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredData.map((item) => (
              <Tr key={item.annee}>
                <Td fontSize={fontSize}>{item.annee}</Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.valeurTheoriqueActifReel)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.tresorerieDisponible)}
                </Td>
                <Td fontSize={fontSize}>{formatCurrency(item.emprunt)}</Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.compteCourant)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.prixCessionPartsTheorique)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.augmentationPrixVente)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.valeurOriginePartsSociales)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.priseEnCompteGainsFiscaux)}
                </Td>
                <Td fontSize={fontSize}>{formatCurrency(item.plusValue)}</Td>
                <Td fontSize={fontSize}>{item.nombreParts}</Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.plusValueParPart)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatPercentage(item.tauxAbattementImpotRevenu)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.abattementImpotRevenu)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.baseTaxableImpotRevenu)}
                </Td>
                <Td fontSize={fontSize}>{formatCurrency(item.impotRevenu)}</Td>
                <Td fontSize={fontSize}>
                  {formatPercentage(item.tauxAbattementPrelevementsSociaux)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.abattementPrelevementsSociaux)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.baseTaxablePrelevementsSociaux)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.prelevementsSociauxPlusValue)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.taxationSupplementaire)}
                </Td>
                <Td fontSize={fontSize}>
                  {formatCurrency(item.totalImpositionPlusValue)}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Box>
  );
};

export default FonciersReelsSCIPartsTable;
