// No need for Chakra UI imports with the base component
import { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';
import MicroFoncierProjectionTable from './MicroFoncierProjectionTable';
import MicroFoncierPlusValueProjectionTable from './MicroFoncierPlusValueProjectionTable';
import {
  MicroFonciersFormProps,
  MicroFonciersFormData,
} from '~/lib/types/mircro-foncier';
import { calculateMicroFonciersData } from '../../utils/microFonciersCalculations';
import TaxRegimeFormBase from '../common/TaxRegimeFormBase';

const MicroFonciersForm = forwardRef((props: MicroFonciersFormProps, ref) => {
  const { data, setParentFormData } = props;
  const [formData, setFormData] = useState<Partial<MicroFonciersFormData>>(
    data || {}
  );

  // No need for fontSize with the base component

  // Format number as currency
  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return '0 €';
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(value);
  };

  // Calculate values based on the data from previous steps and Excel formulas
  useEffect(() => {
    // Use the calculation function from the separate file
    const calculatedData = calculateMicroFonciersData(data, formatCurrency);

    // Update the local state
    setFormData(calculatedData);

    // Pass the calculated data to the parent component
    setParentFormData(calculatedData);
  }, []);

  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    onSubmit: () => {
      // Validation logic if needed
      return true;
    },
    setDataFromParent: (parentData: any) => {
      setFormData(parentData);
    },
  }));

  // Data for the finance tables
  const conditionsData = [
    {
      label:
        'Locations nues (usage habitation, locaux commerciaux, professionnels)',
      value: formData.locationsNues || 'Oui',
    },
    {
      label: 'Revenus locatifs hors charges du foyer fiscal',
      value: formatCurrency(formData.loyersAnnuels || 0),
    },
    {
      label: 'Autres revenus locatifs en Nue ?',
      value: formData.autresRevenusLocatifsNue || 'Non',
    },
    {
      label: 'Fonciers au régime réel',
      value: formatCurrency(formData.foncierReels || 0),
    },
    {
      label: 'Régime Micro-fonciers possible ?',
      value: formData.regimeMicroFoncierPossible ? 'Oui' : 'Non',
      colorScheme: formData.regimeMicroFoncierPossible ? 'green' : 'red',
    },
    {
      label: 'Revenus issus de parts de SCI/SCPI',
      value: formatCurrency(formData.revenusSCI || 0),
    },
    {
      label: 'Recettes exceptionnelles',
      value: formatCurrency(formData.recettesExceptionnelles || 0),
    },
    {
      label: "Recettes issues d'emplacements publicitaires",
      value: formatCurrency(formData.recettesEmplacementsPublicitaires || 0),
    },
    {
      label: 'TOTAL',
      value: formatCurrency(formData.totalRevenus || 0),
      colorScheme: 'teal',
    },
    {
      label: 'Limite régime micro-foncier non atteinte ?',
      value: formData.limiteNonAtteinte ? 'Oui' : 'Non',
      colorScheme: formData.limiteNonAtteinte ? 'green' : 'red',
    },
    {
      label: 'Régime micro-foncier applicable ?',
      value: formData.regimeMicroFoncierApplicable ? 'Oui' : 'Non',
      colorScheme: formData.regimeMicroFoncierApplicable ? 'green' : 'red',
      comment: formData.message,
    },
  ];

  const microFonciersData = [
    {
      label: 'Revenus bruts fonciers',
      value: formatCurrency(formData.totalRevenus || 0),
    },
    {
      label: 'Abattement forfaitaire (30%)',
      value: formatCurrency(formData.abattementForfaitaire || 0),
    },
    {
      label: 'Revenu net imposable',
      value: formatCurrency(formData.revenuNetImposable || 0),
    },
  ];

  const impositionData = [
    {
      label: "Taux marginal d'imposition",
      value: `${((formData.tauxMarginalImposition || 0) * 100).toFixed(1)}%`,
    },
    {
      label: "Montant de l'impôt",
      value: formatCurrency(formData.montantImpot || 0),
    },
    {
      label: 'Prélèvements sociaux (17,2%)',
      value: formatCurrency(formData.prelevementsSociaux || 0),
    },
    {
      label: 'CSG Déductible (6,8%)',
      value: formatCurrency(-(formData.csgDeductible || 0)),
    },
    {
      label: 'Total des prélèvements',
      value: formatCurrency(formData.totalPrelevements || 0),
      colorScheme: 'teal',
    },
  ];

  const tresorerieData = [
    {
      label: 'Trésorerie dégagée hors emprunts',
      value: formatCurrency(formData.tresorerieDegageeHorsEmprunts || 0),
    },
    {
      label: 'Remboursements emprunts sur 25 ans',
      value: formatCurrency(formData.remboursementEmprunt || 0),
    },
    {
      label: 'Trésorerie totale dégagée',
      value: formatCurrency(formData.tresorerieTotaleDegagee || 0),
      colorScheme:
        (formData.tresorerieTotaleDegagee || 0) >= 0 ? 'green' : 'red',
    },
  ];

  const rentabiliteData = [
    {
      label: 'Revenu net après impôt',
      value: formatCurrency(formData.revenuNetApresImpot || 0),
    },
    {
      label: "Rentabilité nette d'impôt",
      value: `${(formData.rentabiliteNetteImpot || 0).toFixed(2)}%`,
      colorScheme: 'teal',
    },
  ];

  // Define tab content
  const conditionsTab = (
    <FinanceTable
      title="Régime Micro-Fonciers - Conditions"
      data={conditionsData}
      showComments={false}
    />
  );

  const calculationsTab = (
    <>
      <FinanceTable
        title="Revenus Fonciers"
        data={microFonciersData}
        showComments={true}
      />
      <FinanceTable
        title="Imposition"
        data={impositionData}
        showComments={true}
      />
      <FinanceTable
        title="Rentabilité"
        data={rentabiliteData}
        showComments={true}
      />
    </>
  );

  const treasuryTab = (
    <FinanceTable
      title="Trésorerie"
      data={tresorerieData}
      showComments={true}
    />
  );

  const projectionsTab =
    formData.projectionData && formData.projectionData.length > 0 ? (
      <MicroFoncierProjectionTable
        projectionData={formData.projectionData}
        formatCurrency={formatCurrency}
      />
    ) : null;

  const plusValuesTab =
    formData.plusValueData && formData.plusValueData.length > 0 ? (
      <MicroFoncierPlusValueProjectionTable
        plusValueData={formData.plusValueData}
        formatCurrency={formatCurrency}
        limitExceededYear={formData.limitExceededYear}
      />
    ) : null;

  // Define tabs configuration
  const tabs = [
    { label: 'Conditions', content: conditionsTab },
    { label: 'Calculs', content: calculationsTab },
    { label: 'Trésorerie', content: treasuryTab },
    {
      label: 'Projections',
      content: projectionsTab,
      isVisible: formData.projectionData && formData.projectionData.length > 0,
    },
    {
      label: 'Plus-values',
      content: plusValuesTab,
      isVisible: formData.plusValueData && formData.plusValueData.length > 0,
    },
  ];

  const description =
    "Le régime Micro-Foncier est un régime fiscal simplifié pour les propriétaires qui perçoivent moins de 15 000€ de revenus fonciers annuels. Il permet de bénéficier d'un abattement forfaitaire de 30% sur les revenus bruts.";

  return (
    <TaxRegimeFormBase
      title="Prévision Micro-Fonciers"
      description={description}
      tabs={tabs}
      warningMessage={
        formData.message ||
        "Régime Micro-Fonciers n'est plus applicable au cas étudié, un changement de régime est à opérer vers le régime fonciers réels ou en meublé"
      }
      showWarning={!formData.regimeMicroFoncierApplicable}
    />
  );
});

export default MicroFonciersForm;
