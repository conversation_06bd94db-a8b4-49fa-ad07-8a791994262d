import React from 'react';
import { Box, Alert, AlertIcon, AlertTitle, AlertDescription } from '@chakra-ui/react';
import { ProjectionDataItem } from '~/lib/types/mircro-foncier';
import ProjectionTable from '../common/ProjectionTable';
import {
  formatCurrency as formatCurrencyUtil,
  getValueColor,
} from '../../utils/formatters';

interface MicroFoncierProjectionTableProps {
  projectionData: ProjectionDataItem[];
  formatCurrency: (value: number | undefined) => string;
}

const MicroFoncierProjectionTable: React.FC<
  MicroFoncierProjectionTableProps
> = ({ projectionData, formatCurrency }) => {
  // Check if any year has the limit exceeded flag
  const limitExceededData = projectionData.find(year => year.limitExceeded);

  // Map the data to match our common component's expected format
  const mappedData = projectionData.map((year) => ({
    year: year.annee,
    produits: year.produits,
    abattement: year.abattement,
    resultatImposable: year.resultatImposable,
    impot: year.impot,
    prelevements: year.prelevements,
    csgDed: -year.csgDed, // Note the negation here to match the display in the original component
    irpp: year.irpp,
    tresorerieHorsEmprunts: year.tresorerieHorsEmprunts,
    tresorerieTotale: year.tresorerieTotale,
    tresorerieCumulee: year.cumulTresorerie,
    limitExceeded: year.limitExceeded,
  }));

  const columns = [
    { header: 'Année', accessor: 'year' },
    {
      header: 'Produits',
      accessor: 'produits',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Abattement 30%',
      accessor: 'abattement',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Résultat imposable',
      accessor: 'resultatImposable',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Impôt',
      accessor: 'impot',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Prélèvements sociaux',
      accessor: 'prelevements',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'CSG Déd.',
      accessor: 'csgDed',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'IRPP',
      accessor: 'irpp',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Trésorerie hors emprunts',
      accessor: 'tresorerieHorsEmprunts',
      isNumeric: true,
      format: formatCurrencyUtil,
    },
    {
      header: 'Trésorerie totale',
      accessor: 'tresorerieTotale',
      isNumeric: true,
      format: formatCurrencyUtil,
      colorCondition: getValueColor,
    },
    {
      header: 'Trésorerie cumulée',
      accessor: 'tresorerieCumulee',
      isNumeric: true,
      format: formatCurrencyUtil,
      colorCondition: getValueColor,
    },
  ];

  return (
    <Box>
      {limitExceededData && (
        <Alert status="warning" mb={4} borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Attention</AlertTitle>
            <AlertDescription>
              Le régime Micro-Fonciers ne sera plus applicable à partir de l'année {limitExceededData.limitExceededYear} car les revenus locatifs dépasseront la limite de 15 000€ (produits = {formatCurrency(limitExceededData.produits)}).
            </AlertDescription>
          </Box>
        </Alert>
      )}
      <ProjectionTable
        title="Régime Micro-Fonciers - Prévisionnel de trésorerie"
        description="Ce tableau présente une projection financière incluant les revenus, les charges, les impôts et la trésorerie dégagée chaque année."
        data={mappedData}
        columns={columns}
        defaultYearsToShow={10}
        footnote="(1) Possibilité de modifier l'indice. Nous avons appliqué une évolution de l'indice cohérente d'un montant constant de 2%"
      />
    </Box>
  );
};

export default MicroFoncierProjectionTable;
