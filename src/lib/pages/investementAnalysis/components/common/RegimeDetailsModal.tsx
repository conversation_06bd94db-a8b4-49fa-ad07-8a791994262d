import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalCloseButton,
  Button,
  Box,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Center,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
// Import full form components
import MicroFonciersForm from '../microFoncier/MicroFonciersForm';
import FonciersReelsForm from '../fonciersReels/FonciersReelsForm';
import MicroBICForm from '../microBIC/MicroBICForm';
import MicroBICChambreHoteForm from '../microBICChambreHote/MicroBICChambreHoteForm';
import MicroBICTourismeNCForm from '../microBICTourismeNC/MicroBICTourismeNCForm';
import BICReelLMNPForm from '../bicReelLMNP/BICReelLMNPForm';
import SCIISForm from '../sciIS/SCIISForm';
import LMPSansTVAForm from '../lmpSansTVA/LMPSansTVAForm';
import SARLDeFamilleForm from '../sarlDeFamille/SARLDeFamilleForm';

interface RegimeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedRegime: string | null;
  data: FormDataInvestementAnalysistSteps;
}

const RegimeDetailsModal = ({
  isOpen,
  onClose,
  selectedRegime,
  data,
}: RegimeDetailsModalProps) => {
  const [isLoading, setIsLoading] = useState(true);

  // When the modal opens, set loading to true initially
  useEffect(() => {
    if (isOpen && selectedRegime) {
      setIsLoading(true);

      // Check if we have data for the selected regime
      const hasData = () => {
        switch (selectedRegime) {
          case 'microFonciers':
            return !!data.microFonciersData;
          case 'fonciersReels':
            return !!data.fonciersReelsData;
          case 'microBIC':
            return !!data.microBICData;
          case 'microBICChambreHote':
            return !!data.microBICChambreHoteData;
          case 'microBICTourismeNC':
            return !!data.microBICTourismeNCData;
          case 'bicReelLMNP':
            return !!data.bicReelLMNPData;
          case 'sciIS':
            return !!data.sciISData;
          case 'lmpSansTVA':
            return !!data.lmpSansTVAData;
          case 'sarlDeFamille':
            return !!data.sarlDeFamilleData;
          default:
            return false;
        }
      };

      // If we have data, set loading to false
      if (hasData()) {
        setIsLoading(false);
      } else {
        // If we don't have data, wait a bit and check again
        // This is to give time for calculations to complete
        const timer = setTimeout(() => {
          setIsLoading(false);
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, selectedRegime, data]);
  // Helper function to get regime title
  const getRegimeTitle = () => {
    switch (selectedRegime) {
      case 'microFonciers':
        return 'PRÉV° MICRO-FONCIERS';
      case 'fonciersReels':
        return 'PRÉV° FONCIERS-RÉELS';
      case 'microBIC':
        return 'PRÉV° MICRO-BIC HABITATION';
      case 'microBICChambreHote':
        return "PRÉV° M-BIC CHAMBRE D'HOTE+TC";
      case 'microBICTourismeNC':
        return 'PRÉV° MICRO-BIC TOURISME NC';
      case 'bicReelLMNP':
        return 'PREV° BIC-REEL LMNP';
      case 'sciIS':
        return 'PREV° SCI IS';
      case 'lmpSansTVA':
        return 'PREV° LMP SANS TVA';
      case 'sarlDeFamille':
        return 'PREV° SARL DE FAMILLE';
      default:
        return 'Détails du régime';
    }
  };

  // Helper function to check if regime is applicable
  const isRegimeApplicable = () => {
    // First check if data exists for the regime
    const hasData = () => {
      switch (selectedRegime) {
        case 'microFonciers':
          return !!data.microFonciersData;
        case 'fonciersReels':
          return !!data.fonciersReelsData;
        case 'microBIC':
          return !!data.microBICData;
        case 'microBICChambreHote':
          return !!data.microBICChambreHoteData;
        case 'microBICTourismeNC':
          return !!data.microBICTourismeNCData;
        case 'bicReelLMNP':
          return !!data.bicReelLMNPData;
        case 'sciIS':
          return !!data.sciISData;
        case 'lmpSansTVA':
          return !!data.lmpSansTVAData;
        case 'sarlDeFamille':
          return !!data.sarlDeFamilleData;
        default:
          return false;
      }
    };

    // If no data exists, regime is not applicable
    if (!hasData()) {
      return false;
    }

    // If data exists, check the applicability condition
    switch (selectedRegime) {
      case 'microFonciers':
        return data.microFonciersData?.regimeMicroFoncierApplicable === true;
      case 'fonciersReels':
        return data.fonciersReelsData?.regimeFonciersReelApplicable === true;
      case 'microBIC':
        return data.microBICData?.regimeMicroBICApplicable === true;
      case 'microBICChambreHote':
        return data.microBICChambreHoteData?.regimeMicroBICApplicable === true;
      case 'microBICTourismeNC':
        return data.microBICTourismeNCData?.regimeMicroBICApplicable === true;
      case 'bicReelLMNP':
        return data.bicReelLMNPData?.regimeBICReelApplicable === 'OUI';
      case 'sciIS':
        return data.sciISData?.regimeApplicable === 'OUI';
      case 'lmpSansTVA':
        return data.lmpSansTVAData?.regimeLMPApplicable === 'OUI';
      case 'sarlDeFamille':
        return data.sarlDeFamilleData?.regimeSARLDeFamilleApplicable === 'OUI';
      default:
        return false;
    }
  };

  // Helper function to get regime description
  const getRegimeDescription = () => {
    switch (selectedRegime) {
      case 'microFonciers':
        return "Le régime Micro-Foncier est un régime fiscal simplifié pour les propriétaires qui perçoivent moins de 15 000€ de revenus fonciers annuels. Il permet de bénéficier d'un abattement forfaitaire de 30% sur les revenus bruts.";
      case 'fonciersReels':
        return "Le régime Fonciers-Réels permet de déduire les charges réelles liées à la location nue (intérêts d'emprunt, charges de copropriété, travaux, etc.) des revenus locatifs, contrairement au régime Micro-Foncier qui applique un abattement forfaitaire.";
      case 'microBIC':
        return "Le régime Micro-BIC habitation est un régime fiscal simplifié pour les propriétaires qui louent des logements meublés à usage d'habitation et perçoivent moins de 77 700€ de revenus locatifs annuels. Il permet de bénéficier d'un abattement forfaitaire de 50% sur les revenus bruts.";
      case 'microBICChambreHote':
        return "Le régime Micro-BIC pour les chambres d'hôtes et meublés de tourisme classés est un régime fiscal simplifié pour les propriétaires qui louent des logements meublés à usage touristique et perçoivent moins de 77 700€ de revenus locatifs annuels. Il permet de bénéficier d'un abattement forfaitaire de 50% sur les revenus bruts.";
      case 'microBICTourismeNC':
        return "Le régime Micro-BIC pour les meublés de tourisme non classés est un régime fiscal simplifié pour les propriétaires qui louent des logements meublés à usage touristique non classés et perçoivent moins de 77 700€ de revenus locatifs annuels. Il permet de bénéficier d'un abattement forfaitaire de 50% sur les revenus bruts.";
      case 'bicReelLMNP':
        return "Le régime BIC au réel simplifié est applicable aux loueurs en meublé non professionnels (LMNP) dont les revenus locatifs ne dépassent pas certaines limites. Il permet de déduire les charges réelles et d'amortir les biens, contrairement au régime Micro-BIC qui applique un abattement forfaitaire.";
      case 'sciIS':
        return "Le régime SCI à l'IS permet de bénéficier de la fiscalité des sociétés soumises à l'impôt sur les sociétés, avec un taux d'imposition fixe sur les bénéfices et la possibilité d'amortir les biens immobiliers.";
      case 'lmpSansTVA':
        return "Le régime LMP sans TVA s'applique aux loueurs en meublé professionnels qui réalisent plus de 23 000€ de recettes annuelles et dont ces recettes excèdent les autres revenus du foyer fiscal. Il permet de bénéficier d'avantages fiscaux spécifiques.";
      case 'sarlDeFamille':
        return "Le régime SARL de famille est une option fiscale permettant à une SARL composée uniquement de membres d'une même famille d'opter pour l'impôt sur le revenu plutôt que l'impôt sur les sociétés, tout en conservant les avantages juridiques d'une société.";
      default:
        return 'Sélectionnez un régime pour voir sa description.';
    }
  };

  // Render the appropriate projection table based on the selected regime
  const renderProjectionTable = () => {
    // Log the data to help with debugging
    console.log('Modal data for regime:', selectedRegime, data);

    // Check if we have data for the selected regime
    const hasData = () => {
      switch (selectedRegime) {
        case 'microFonciers':
          return !!data.microFonciersData;
        case 'fonciersReels':
          return !!data.fonciersReelsData;
        case 'microBIC':
          return !!data.microBICData;
        case 'microBICChambreHote':
          return !!data.microBICChambreHoteData;
        case 'microBICTourismeNC':
          return !!data.microBICTourismeNCData;
        case 'bicReelLMNP':
          return !!data.bicReelLMNPData;
        case 'sciIS':
          return !!data.sciISData;
        case 'lmpSansTVA':
          return !!data.lmpSansTVAData;
        case 'sarlDeFamille':
          return !!data.sarlDeFamilleData;
        default:
          return false;
      }
    };

    // If we don't have data, show a message
    if (!hasData()) {
      return (
        <Alert status="info" mb={4} borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              Les données de projection ne sont pas disponibles pour ce régime.
              Veuillez vous assurer que toutes les étapes précédentes ont été
              complétées.
            </AlertDescription>
          </Box>
        </Alert>
      );
    }

    // If we have data, render the appropriate form
    const setParentFormData = (updatedData: any) => {
      // This is a read-only view, so we don't need to update the parent data
      console.log('Form data updated:', updatedData);
    };

    switch (selectedRegime) {
      case 'microFonciers':
        return data.microFonciersData ? (
          <MicroFonciersForm
            data={data}
            setParentFormData={setParentFormData}
          />
        ) : null;
      case 'fonciersReels':
        return data.fonciersReelsData ? (
          <FonciersReelsForm
            data={data}
            setParentFormData={setParentFormData}
          />
        ) : null;
      case 'microBIC':
        return data.microBICData ? (
          <MicroBICForm data={data} setParentFormData={setParentFormData} />
        ) : null;
      case 'microBICChambreHote':
        return data.microBICChambreHoteData ? (
          <MicroBICChambreHoteForm
            data={data}
            setParentFormData={setParentFormData}
          />
        ) : null;
      case 'microBICTourismeNC':
        return data.microBICTourismeNCData ? (
          <MicroBICTourismeNCForm
            data={data}
            setParentFormData={setParentFormData}
          />
        ) : null;
      case 'bicReelLMNP':
        return data.bicReelLMNPData ? (
          <BICReelLMNPForm data={data} setParentFormData={setParentFormData} />
        ) : null;
      case 'sciIS':
        return data.sciISData ? (
          <SCIISForm data={data} setParentFormData={setParentFormData} />
        ) : null;
      case 'lmpSansTVA':
        return data.lmpSansTVAData ? (
          <LMPSansTVAForm data={data} setParentFormData={setParentFormData} />
        ) : null;
      case 'sarlDeFamille':
        return data.sarlDeFamilleData ? (
          <SARLDeFamilleForm
            data={data}
            setParentFormData={setParentFormData}
          />
        ) : null;
      default:
        return <Text>Aucune donnée de projection disponible</Text>;
    }
  };

  // Check if we have data for the selected regime
  const hasData = () => {
    if (!selectedRegime) return false;

    switch (selectedRegime) {
      case 'microFonciers':
        return !!data.microFonciersData;
      case 'fonciersReels':
        return !!data.fonciersReelsData;
      case 'microBIC':
        return !!data.microBICData;
      case 'microBICChambreHote':
        return !!data.microBICChambreHoteData;
      case 'microBICTourismeNC':
        return !!data.microBICTourismeNCData;
      case 'bicReelLMNP':
        return !!data.bicReelLMNPData;
      case 'sciIS':
        return !!data.sciISData;
      case 'lmpSansTVA':
        return !!data.lmpSansTVAData;
      case 'sarlDeFamille':
        return !!data.sarlDeFamilleData;
      default:
        return false;
    }
  };

  // Use a larger size for the modal when displaying the full forms
  const modalSize = 'full';

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={modalSize}>
      <ModalOverlay />
      <ModalContent maxW="90%">
        <ModalHeader>{getRegimeTitle()}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {!isRegimeApplicable() && (
            <Alert status="warning" mb={4} borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Attention</AlertTitle>
                <AlertDescription>
                  Ce régime n'est pas applicable au cas étudié.
                </AlertDescription>
              </Box>
            </Alert>
          )}

          <Text mb={4}>{getRegimeDescription()}</Text>

          {isLoading ? (
            <Center p={10}>
              <Spinner
                thickness="4px"
                speed="0.65s"
                emptyColor="gray.200"
                color="teal.500"
                size="xl"
              />
            </Center>
          ) : !hasData() ? (
            <Alert status="info" mb={4} borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Information</AlertTitle>
                <AlertDescription>
                  Les données de projection ne sont pas disponibles pour ce
                  régime. Veuillez vous assurer que toutes les étapes
                  précédentes ont été complétées.
                </AlertDescription>
              </Box>
            </Alert>
          ) : (
            // For all regimes, render the full form which already has its own tabs
            <Box pb={4}>{renderProjectionTable()}</Box>
          )}
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="teal" mr={3} onClick={onClose}>
            Fermer
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default RegimeDetailsModal;
