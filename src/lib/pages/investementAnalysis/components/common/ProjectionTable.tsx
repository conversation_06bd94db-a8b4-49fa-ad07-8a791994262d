import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Heading,
  Flex,
  Select,
  TableContainer,
} from '@chakra-ui/react';
import { useState } from 'react';

interface Column {
  header: string;
  accessor: string;
  isNumeric?: boolean;
  format?: (value: any) => string;
  colorCondition?: (value: any) => string | undefined;
}

interface ProjectionTableProps {
  title: string;
  description?: string;
  data: any[];
  columns: Column[];
  defaultYearsToShow?: number;
  footnote?: string;
}

/**
 * A common table component for displaying projection data with consistent styling and behavior
 */
const ProjectionTable = ({
  title,
  description,
  data,
  columns,
  defaultYearsToShow = 10,
  footnote,
}: ProjectionTableProps) => {
  const [yearsToShow, setYearsToShow] = useState<number>(defaultYearsToShow);

  if (!data || data.length === 0) {
    return (
      <Box>
        <Heading size="md" mb={4} color="teal.600">
          {title}
        </Heading>
        <Text color="gray.600">Aucune donnée disponible.</Text>
      </Box>
    );
  }

  // Limit data to the selected number of years
  const displayData = data.slice(0, Math.min(yearsToShow, data.length));

  return (
    <Box>
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <Heading size="md" color="teal.600">
          {title}
        </Heading>
        <Flex alignItems="center">
          <Text mr={2} fontSize="sm">
            Nombre d'années:
          </Text>
          <Select
            value={yearsToShow}
            onChange={(e) => setYearsToShow(Number(e.target.value))}
            size="sm"
            width="100px"
          >
            <option value={5}>5 ans</option>
            <option value={10}>10 ans</option>
            <option value={15}>15 ans</option>
            <option value={20}>20 ans</option>
            <option value={25}>25 ans</option>
            <option value={30}>30 ans</option>
          </Select>
        </Flex>
      </Flex>

      {description && (
        <Text mb={4} fontSize="sm" color="gray.600">
          {description}
        </Text>
      )}

      <TableContainer overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead bg="teal.50">
            <Tr>
              {columns.map((column, index) => (
                <Th key={index} isNumeric={column.isNumeric}>
                  {column.header}
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {displayData.map((row, rowIndex) => (
              <Tr key={rowIndex} bg={rowIndex % 2 === 0 ? 'white' : 'gray.50'}>
                {columns.map((column, colIndex) => {
                  const value = row[column.accessor];
                  const formattedValue = column.format ? column.format(value) : value;
                  const color = column.colorCondition ? column.colorCondition(value) : undefined;
                  
                  return (
                    <Td 
                      key={colIndex} 
                      isNumeric={column.isNumeric}
                      color={color}
                    >
                      {formattedValue}
                    </Td>
                  );
                })}
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>

      {footnote && (
        <Text fontSize="xs" color="gray.500" mt={2}>
          {footnote}
        </Text>
      )}
    </Box>
  );
};

export default ProjectionTable;
