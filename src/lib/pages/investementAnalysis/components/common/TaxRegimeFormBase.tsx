import {
  Box,
  Heading,
  Text,
  useBreakpointValue,
  Card,
  CardHeader,
  CardBody,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import { ReactNode } from 'react';

interface TabConfig {
  label: string;
  content: ReactNode;
  isVisible?: boolean;
}

interface TaxRegimeFormBaseProps {
  title: string;
  description: string;
  tabs: TabConfig[];
  warningMessage?: string;
  showWarning?: boolean;
}

/**
 * Base component for all tax regime forms to ensure consistent styling and structure
 */
const TaxRegimeFormBase = ({
  title,
  description,
  tabs,
  warningMessage,
  showWarning = false,
}: TaxRegimeFormBaseProps) => {
  const fontSize = useBreakpointValue({ base: 'sm', md: 'md' });
  
  // Filter out tabs that should not be visible
  const visibleTabs = tabs.filter(tab => tab.isVisible !== false);

  return (
    <Box p={{ base: 2, md: 4 }}>
      <Card mb={6} variant="outline" boxShadow="sm">
        <CardHeader bg="teal.50" py={3}>
          <Heading size="md" textAlign="center" color="teal.600">
            {title}
          </Heading>
        </CardHeader>
        <CardBody>
          <Text mb={4} fontSize={fontSize} color="gray.600">
            {description}
          </Text>

          <Tabs isFitted variant="enclosed" colorScheme="teal" mb={6}>
            <TabList>
              {visibleTabs.map((tab, index) => (
                <Tab key={index}>{tab.label}</Tab>
              ))}
            </TabList>

            <TabPanels>
              {visibleTabs.map((tab, index) => (
                <TabPanel key={index}>
                  {tab.content}
                  
                  {index === 0 && showWarning && (
                    <Alert status="warning" mt={4} borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <AlertTitle>Attention</AlertTitle>
                        <AlertDescription>
                          {warningMessage || "Ce régime n'est pas applicable au cas étudié."}
                        </AlertDescription>
                      </Box>
                    </Alert>
                  )}
                </TabPanel>
              ))}
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>
    </Box>
  );
};

export default TaxRegimeFormBase;
