import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  SCIISFormData,
  ProjectionDataItem,
  PlusValueDataItem,
  SCIPartsDataItem,
} from '~/lib/types/sci-is';

/**
 * Calculates all the data for the SCI IS tax regime
 * @param data Input data from the parent component
 * @returns Calculated data for the SCI IS form
 */
export function calculateSCIISData(
  data: FormDataInvestementAnalysistSteps
): SCIISFormData {
  // Get values from previous steps
  const locationsMeublees = data?.realEstateData?.locationsMeublees || 'Non';
  const locationsSaisonnieresMeublees =
    data?.realEstateData?.locationsSaisonnieres || 'Non';
  const nombreAssocies = data?.realEstateData?.nombreAssocies || 0;
  const nombreAssociesRequis = nombreAssocies < 2 ? 'NON' : 'OUI';
  const hasMeubleeOrSaisonniere = locationsMeublees === 'Oui' || locationsSaisonnieresMeublees === 'Oui';
  const concatener = hasMeubleeOrSaisonniere ? 'Oui' : 'Non';
  const regimeApplicable = nombreAssociesRequis === 'NON' ? 'NON' :
    !hasMeubleeOrSaisonniere ? 'NON' : 'OUI';

  // Get financial data
  const loyersAnnuels = data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;
  const chargesLocatives = data?.investementCalculatorData?.chargesLocatives || 0;
  const assurancesNonOccupant = data?.investementCalculatorData?.assurancesNonOccupant || 0;
  const assurancesEmprunteur = data?.investementCalculatorData?.assurancesEmprunteur || 0;
  const chargesEntretien = data?.investementCalculatorData?.chargesEntretien || 0;
  const taxesFoncieres = data?.investementCalculatorData?.taxesFoncieres || 0;
  const fraisEmprunts = data?.investementCalculatorData?.fraisEmprunts || 0;
  const cfe = data?.investementCalculatorData?.CFE || 0;
  const fraisBancaires = data?.investementCalculatorData?.fraisBancaires || 0;
  const interetsEmprunt = data?.investementCalculatorData?.interetsEmprunt || 0;
  const fraisComptables = data?.investementCalculatorData?.fraisComptables || 0;
  const fraisJuridiques = data?.investementCalculatorData?.fraisJuridiques || 0;
  const fraisGerant = data?.investementCalculatorData?.fraisGerant || 0;
  const amortissement = 0; // This would need to be calculated based on amortization schedule
  const prixAcquisition = data?.realEstateData?.prixAcquisitionMontant || 0;
  const indiceMoyen = data?.realEstateData?.applicationIndice === 'Oui' ? 0.02 : 0;

  // Calculate projection data for 30 years
  const projectionData: ProjectionDataItem[] = [];
  let cumulativeCashFlow = 0;
  let cumulativeFiscalResult = 0;
  let cumulativeDividends = 0;

  for (let year = 1; year <= 30; year++) {
    // Calculate rental income with annual increase based on index
    const rentalIncome = year === 1
      ? loyersAnnuels
      : indiceMoyen > 0
        ? projectionData[year - 2].rentalIncome * (1 + indiceMoyen)
        : loyersAnnuels;

    // Calculate property taxes with 5% annual increase
    const propertyTaxes = year === 1
      ? taxesFoncieres
      : projectionData[year - 2].propertyTaxes * 1.05;

    // Calculate loan interest based on loan schedule
    // This is a simplified calculation, in a real app this would come from a loan amortization table
    const loanInterest = year <= 25 ? interetsEmprunt / year : 0;

    // Calculate accounting result
    const accountingResult = rentalIncome - chargesLocatives - assurancesNonOccupant -
      assurancesEmprunteur - chargesEntretien - propertyTaxes - cfe - loanInterest -
      fraisBancaires - fraisComptables - fraisJuridiques - fraisGerant - amortissement;

    // Fiscal result is the same as accounting result for this simplified example
    const fiscalResult = accountingResult;
    const isDeficit = fiscalResult < 0;

    // Update cumulative fiscal result
    if (isDeficit) {
      cumulativeFiscalResult += fiscalResult;
    } else {
      cumulativeFiscalResult = fiscalResult > cumulativeFiscalResult ? fiscalResult : cumulativeFiscalResult + fiscalResult;
    }

    // Calculate corporate tax (IS)
    const reducedRateIS = cumulativeFiscalResult <= 0 ? 0 :
      cumulativeFiscalResult <= 42500 ? cumulativeFiscalResult * 0.15 : 42500 * 0.15;

    const normalRateIS = cumulativeFiscalResult > 42500 ? (cumulativeFiscalResult - 42500) * 0.25 : 0;
    const totalIS = reducedRateIS + normalRateIS;

    // Calculate dividends
    const canDistributeDividends = (cumulativeFiscalResult - totalIS) > 0;
    const distributableDividends = canDistributeDividends ? (cumulativeFiscalResult - totalIS) : 0;
    cumulativeDividends += distributableDividends;
    const remainingDividends = cumulativeDividends - distributableDividends;

    // Calculate flat tax on dividends (30%)
    const flatTax = distributableDividends * 0.3;
    const netDividends = distributableDividends - flatTax;

    // Calculate cash flow
    const cashFlowBeforeLoans = rentalIncome - chargesLocatives - assurancesNonOccupant -
      assurancesEmprunteur - chargesEntretien - propertyTaxes - cfe - fraisBancaires -
      fraisComptables - fraisJuridiques - fraisGerant - totalIS - distributableDividends;

    // Simplified loan repayment calculations
    const loanRepayment15Years = year <= 15 ? (fraisEmprunts - loanInterest - assurancesEmprunteur) / 15 : 0;
    const loanRepayment20Years = year <= 20 ? (fraisEmprunts - loanInterest - assurancesEmprunteur) / 20 : 0;
    const loanRepayment25Years = year <= 25 ? (fraisEmprunts - loanInterest - assurancesEmprunteur) / 25 : 0;

    // Total cash flow depends on the loan duration
    const totalCashFlow = cashFlowBeforeLoans - loanRepayment15Years - loanRepayment20Years - loanRepayment25Years;
    cumulativeCashFlow += totalCashFlow;

    projectionData.push({
      year,
      rentalIncome,
      maintenanceCharges: chargesEntretien,
      rentalCharges: chargesLocatives,
      nonOccupantInsurance: assurancesNonOccupant,
      loanInsurance: assurancesEmprunteur,
      loanFees: fraisEmprunts,
      propertyTaxes,
      cfe,
      loanInterest,
      bankFees: fraisBancaires,
      accountingFees: fraisComptables,
      legalFees: fraisJuridiques,
      domiciliationFees: fraisGerant,
      amortization: amortissement,
      accountingResult,
      fiscalResult,
      isDeficit,
      cumulativeFiscalResult,
      reducedRateIS,
      normalRateIS,
      totalIS,
      canDistributeDividends,
      distributableDividends,
      cumulativeDividends,
      remainingDividends,
      flatTax,
      netDividends,
      cashFlowBeforeLoans,
      loanRepayment15Years,
      loanRepayment20Years,
      loanRepayment25Years,
      totalCashFlow,
      cumulativeCashFlow,
    });
  }

  // Calculate plus value data
  const plusValueData: PlusValueDataItem[] = [];

  for (let year = 1; year <= 30; year++) {
    // Calculate property value with 3% annual appreciation
    const salePrice = prixAcquisition * Math.pow(1.03, year);

    // Simplified calculation for deducted amortization
    const deductedAmortization = amortissement * year;

    // Net book value is acquisition price minus accumulated amortization
    const netBookValue = prixAcquisition - deductedAmortization;

    // Fiscal plus value is sale price minus net book value
    const fiscalPlusValue = salePrice - netBookValue;

    // Use the cumulative fiscal result from projections
    const cumulativeFiscalResult = projectionData[year - 1].cumulativeFiscalResult;

    // Fiscal result before tax includes the plus value
    const fiscalResultBeforeTax = cumulativeFiscalResult + fiscalPlusValue;

    const isDeficit = fiscalResultBeforeTax < 0;

    // Calculate corporate tax (IS)
    const reducedRateIS = fiscalResultBeforeTax <= 0 ? 0 :
      fiscalResultBeforeTax <= 42500 ? fiscalResultBeforeTax * 0.15 : 42500 * 0.15;

    const normalRateIS = fiscalResultBeforeTax > 42500 ? (fiscalResultBeforeTax - 42500) * 0.25 : 0;
    const totalIS = reducedRateIS + normalRateIS;

    // Calculate dividends
    const canDistributeDividends = (fiscalResultBeforeTax - totalIS) > 0;
    const distributableDividends = canDistributeDividends ? (fiscalResultBeforeTax - totalIS) : 0;

    // Use the cumulative dividends from projections
    const cumulativeDividends = projectionData[year - 1].cumulativeDividends;

    // Total distributable dividends includes current plus value dividends
    const totalDistributableDividends = distributableDividends + cumulativeDividends;

    // Available cash before dividends
    const availableCashBeforeDividends = projectionData[year - 1].cumulativeCashFlow +
      totalIS + salePrice - totalIS;

    // Distributable dividends for the year
    const distributableDividendsForYear = Math.min(totalDistributableDividends, availableCashBeforeDividends);

    // Calculate flat tax on dividends (30%)
    const flatTax = distributableDividendsForYear * 0.3;
    const netDividendsFlatTax = distributableDividendsForYear - flatTax;

    // Cash after dividend distribution
    const cashAfterDividendDistribution = availableCashBeforeDividends - flatTax - netDividendsFlatTax;

    // Remaining loan to repay
    const remainingLoan = year <= 25 ? (fraisEmprunts - projectionData[year - 1].loanInterest) * (25 - year) / 25 : 0;

    // Treasury after loan repayment
    const treasuryAfterLoanRepayment = cashAfterDividendDistribution - remainingLoan + netDividendsFlatTax;

    plusValueData.push({
      year,
      salePrice,
      acquisitionPrice: prixAcquisition,
      deductedAmortization,
      netBookValue,
      fiscalPlusValue,
      cumulativeFiscalResult,
      fiscalResultBeforeTax,
      isDeficit,
      reducedRateIS,
      normalRateIS,
      totalIS,
      canDistributeDividends,
      distributableDividends,
      cumulativeDividends,
      totalDistributableDividends,
      availableCashBeforeDividends,
      distributableDividendsForYear,
      flatTax,
      netDividendsFlatTax,
      cashAfterDividendDistribution,
      remainingLoan,
      treasuryAfterLoanRepayment,
    });
  }

  // Calculate SCI parts data
  const sciPartsData: SCIPartsDataItem[] = [];

  for (let year = 1; year <= 30; year++) {
    // Calculate property value with 3% annual appreciation
    const propertyValue = prixAcquisition * Math.pow(1.03, year);

    // Treasury without dividends from projections
    const treasuryWithoutDividends = projectionData[year - 1].cumulativeCashFlow +
      projectionData[year - 1].distributableDividends;

    // Other assets (simplified as 0 for this example)
    const otherAssets = 0;

    // Associate accounts (simplified calculation)
    const associateAccounts = data?.realEstateData?.capitalSocialSCI || 0;

    // Loans remaining
    const loans = year <= 25 ? (fraisEmprunts - projectionData[year - 1].loanInterest) * (25 - year) / 25 : 0;

    // Theoretical sale price of SCI parts
    const theoreticalSalePrice = propertyValue + treasuryWithoutDividends + otherAssets - associateAccounts - loans;

    // Acquisition value of SCI parts
    const acquisitionValue = data?.realEstateData?.capitalSocialSCI || 0;

    // Plus value on SCI parts
    const plusValueOnParts = theoreticalSalePrice - acquisitionValue;

    // Calculate flat tax on plus value (30%)
    const flatTax = plusValueOnParts > 0 ? plusValueOnParts * 0.3 : 0;
    const netDividendsFlatTax = plusValueOnParts - flatTax;

    sciPartsData.push({
      year,
      propertyValue,
      treasuryWithoutDividends,
      otherAssets,
      associateAccounts,
      loans,
      theoreticalSalePrice,
      acquisitionValue,
      plusValueOnParts,
      flatTax,
      netDividendsFlatTax,
    });
  }

  return {
    locationsMeublees,
    locationsSaisonnieresMeublees,
    nombreAssociesRequis,
    concatener,
    regimeApplicable,
    indiceMoyen,
    projectionData,
    plusValueData,
    sciPartsData,
  };
}
