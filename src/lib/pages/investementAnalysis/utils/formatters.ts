/**
 * Format a number as currency (EUR)
 * @param value The number to format
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number | undefined): string => {
  if (value === undefined || isNaN(value)) return '0 €';
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

/**
 * Format a number as percentage
 * @param value The number to format (0.01 = 1%)
 * @param minimumFractionDigits Minimum number of decimal places
 * @param maximumFractionDigits Maximum number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number | undefined,
  minimumFractionDigits: number = 0,
  maximumFractionDigits: number = 2
): string => {
  if (value === undefined || isNaN(value)) return '0%';
  return new Intl.NumberFormat('fr-FR', {
    style: 'percent',
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(value);
};

/**
 * Get color for positive/negative values
 * @param value The value to check
 * @returns Color string or undefined
 */
export const getValueColor = (value: number | undefined): string | undefined => {
  if (value === undefined || isNaN(value)) return undefined;
  return value >= 0 ? 'green.600' : 'red.600';
};

/**
 * Format a boolean value as 'Oui' or 'Non'
 * @param value The boolean value
 * @returns 'Oui' or 'Non'
 */
export const formatBoolean = (value: boolean | undefined): string => {
  if (value === undefined) return 'Non';
  return value ? 'Oui' : 'Non';
};
