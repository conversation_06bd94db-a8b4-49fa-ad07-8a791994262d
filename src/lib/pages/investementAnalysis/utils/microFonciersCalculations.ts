import {
  APPLICABLE_FISCAL_OPTIONS,
  getKeyByLabel,
} from '~/lib/pages/buyerAssessment/components/profileInvestor/constants/assetOptions';
import type {
  FormDataInvestementAnalysistSteps,
  IInvestementCalculatorData,
} from '~/lib/types/investement-analysis';
import type {
  AssetInfo,
  MicroFonciersFormData,
  PlusValueDataItem,
  ProjectionDataItem,
} from '~/lib/types/mircro-foncier';
/**
 * Calculates all the data for the Micro-Fonciers tax regime
 * @param data Input data from the parent component
 * @param formatCurrency Function to format currency values
 * @returns Calculated data for the Micro-Fonciers form
 */
export function calculateMicroFonciersData(
  data: FormDataInvestementAnalysistSteps,
  formatCurrency: (value: number | undefined) => string
): Partial<MicroFonciersFormData> {
  // Get values from previous steps

  const loyersAnnuels = data.investementCalculatorData.loyersAnnuelHorsCharges;
  const { tauxMarginalImposition } = data.investementCalculatorData;
  const montantInvestissement = data.investementCalculatorData.loanAmount;
  const remboursementEmprunt = data.investementCalculatorData.monthlyPayment
    ? data.investementCalculatorData.monthlyPayment * 12
    : 0;
  const { nombreParts } = data.investementCalculatorData;

  // Get profile investor data
  const assetsInfoTable = data.profileInvestorData.assetsInfoTable || [];
  const taxRegimeTotals = data.profileInvestorData.taxRegimeTotals || {};

  // Count properties with different rental modes and tax regimes
  let microFonciers =
    data.profileInvestorData?.taxRegimeTotals?.micro_fonciers || 0;
  let foncierReels =
    data.profileInvestorData?.taxRegimeTotals?.fonciers_reel || 0;

  // Get all tax regime keys from the constants
  const allTaxRegimeKeys = APPLICABLE_FISCAL_OPTIONS.map(
    (option) => option.key
  );

  // Initialize counters for each tax regime
  const taxRegimeCounts: Record<string, number> = {};
  allTaxRegimeKeys.forEach((key) => {
    taxRegimeCounts[key] = 0;
  });

  // Use the taxRegimeTotals if available, otherwise count manually
  if (Object.keys(taxRegimeTotals).length > 0) {
    // Count properties based on tax regime totals
    allTaxRegimeKeys.forEach((key) => {
      taxRegimeCounts[key] = taxRegimeTotals[key] ? 1 : 0;
    });
  } else {
    // Fallback to counting properties manually
    assetsInfoTable.forEach((asset: AssetInfo) => {
      // Get the key from the label if we're using the old format
      const regimeKey =
        getKeyByLabel(APPLICABLE_FISCAL_OPTIONS, asset.applicableTaxRegime) ||
        asset.applicableTaxRegime;

      // If it's a valid key, increment the counter
      if (allTaxRegimeKeys.includes(regimeKey)) {
        taxRegimeCounts[regimeKey]++;
      }
    });
  }

  // Extract specific counts for backward compatibility
  microFonciers = taxRegimeCounts.micro_fonciers || 0;
  foncierReels = taxRegimeCounts.fonciers_reel || 0;

  // Micro-Fonciers conditions
  const { locationsNues } = data.realEstateData;
  const autresRevenusLocatifsNue =
    microFonciers + foncierReels > 0 ? 'Oui' : 'Non';

  // Calculate revenue for properties with different tax regimes
  let revenusFoncierReels = 0;

  // Use the taxRegimeTotals if available, otherwise calculate manually
  if (Object.keys(taxRegimeTotals).length > 0) {
    revenusFoncierReels = taxRegimeTotals.fonciers_reel || 0;
  } else {
    // Calculate manually by summing annualRent for assets with fonciers_reel regime
    revenusFoncierReels = assetsInfoTable.reduce((sum, asset) => {
      const regimeKey =
        getKeyByLabel(APPLICABLE_FISCAL_OPTIONS, asset.applicableTaxRegime) ||
        asset.applicableTaxRegime;
      if (regimeKey === 'fonciers_reel') {
        return sum + (parseFloat(asset.annualRent) || 0);
      }
      return sum;
    }, 0);
  }

  // Get additional revenue sources
  const revenusSCI = data.realEstateData.revenusPartsSCI || 0;
  const recettesExceptionnelles =
    data.realEstateData.recettesExceptionnelles || 0;
  const recettesEmplacementsPublicitaires =
    data.realEstateData.recettesEmplacementsPublicitaires || 0;

  const totalRevenus =
    loyersAnnuels +
    revenusSCI +
    recettesExceptionnelles +
    recettesEmplacementsPublicitaires;

  // Check if Micro-Fonciers is applicable
  const limiteNonAtteinte = totalRevenus <= 15000;
  const regimeMicroFoncierPossible = !(revenusFoncierReels > 0);

  // Determine if all conditions are met and set concatener to "Oui" or "Non"
  const allConditionsMet =
    locationsNues === 'Oui' && regimeMicroFoncierPossible && limiteNonAtteinte;
  const concatener = allConditionsMet ? 'Oui' : 'Non';

  const regimeMicroFoncierApplicable = allConditionsMet;

  // Calculate tax values
  const abattementForfaitaire = totalRevenus * 0.3;
  const revenuNetImposable = totalRevenus - abattementForfaitaire;
  const montantImpot = revenuNetImposable * tauxMarginalImposition;
  const prelevementsSociaux = revenuNetImposable * 0.172;
  const csgDeductible = revenuNetImposable * 0.068; // 6.8% of taxable income is deductible
  const irpp = montantImpot + prelevementsSociaux - csgDeductible;
  const totalPrelevements = irpp;

  // Calculate cash flow
  const revenuNetApresImpot = totalRevenus - totalPrelevements;
  const tresorerieDegageeHorsEmprunts = totalRevenus - irpp;
  const tresorerieTotaleDegagee =
    tresorerieDegageeHorsEmprunts - remboursementEmprunt;

  // Calculate profitability
  const rentabiliteNetteImpot =
    (revenuNetApresImpot / montantInvestissement) * 100;

  // Get application indice
  const applicationIndice = data.realEstateData.applicationIndice || 'Non';
  const indiceMoyen = applicationIndice === 'Oui' ? 0.02 : 0; // 2% if application indice is enabled
  const currentYear = new Date(
    data.investementCalculatorData.dateAcquisition
  ).getFullYear();
  // Calculate projection data
  const projectionData = calculateProjectionData(
    totalRevenus,
    indiceMoyen,
    tauxMarginalImposition,
    remboursementEmprunt,
    currentYear
  );

  // Check if the limit is exceeded in any year
  const limitExceededYear = projectionData.find(
    (item) => item.limitExceeded
  )?.limitExceededYear;

  // Calculate plus-value data, stopping at the year when the limit is exceeded if applicable
  const plusValueData = calculatePlusValueData(
    montantInvestissement,
    nombreParts,
    data.investementCalculatorData,
    limitExceededYear
  );

  // Update projectionData with totalTresorerieGeneree from plusValueData
  for (
    let i = 0;
    i < Math.min(projectionData.length, plusValueData.length);
    i++
  ) {
    projectionData[i].totalTresorerieGeneree =
      plusValueData[i].totalTresorerieGeneree;
  }

  // Create message
  let message = regimeMicroFoncierApplicable
    ? `Prévisionnel Micro-Fonciers applicable - Revenus locatifs: ${formatCurrency(totalRevenus)} (limite: 15 000€)`
    : `Régime Micro-Fonciers non applicable${totalRevenus > 15000 ? ` - Revenus (${formatCurrency(totalRevenus)}) dépassent la limite de 15 000€` : ''}${autresRevenusLocatifsNue === 'Oui' && revenusFoncierReels > 0 ? ' - Autres revenus locatifs en régime réel existants' : ''}`;

  // Add information about when the limit will be exceeded if applicable
  if (limitExceededYear && regimeMicroFoncierApplicable) {
    message += ` - Limite de 15 000€ sera dépassée en ${limitExceededYear}`;
  }

  return {
    // Conditions
    locationsNues,
    loyersAnnuels,
    autresRevenusLocatifsNue,
    microFonciers,
    foncierReels: revenusFoncierReels,
    regimeMicroFoncierPossible,
    revenusSCI,
    recettesExceptionnelles,
    recettesEmplacementsPublicitaires,
    totalRevenus,
    limiteNonAtteinte,
    concatener,
    regimeMicroFoncierApplicable,
    applicationIndice,

    // Tax calculations
    abattementForfaitaire,
    revenuNetImposable,
    tauxMarginalImposition,
    montantImpot,
    prelevementsSociaux,
    csgDeductible,
    irpp,
    totalPrelevements,

    // Cash flow
    revenuNetApresImpot,
    tresorerieDegageeHorsEmprunts,
    remboursementEmprunt,
    tresorerieTotaleDegagee,

    // Profitability
    rentabiliteNetteImpot,

    // Projections
    indiceMoyen,
    projectionData,
    limitExceededYear, // Add the year when the limit will be exceeded

    // Plus-value
    plusValueData,

    // Message
    message,
  };
}

/**
 * Calculates projection data for 30 years or until produits exceed 15,000€
 * @returns An object containing the projection data and the year when the limit was exceeded (if applicable)
 */
function calculateProjectionData(
  totalRevenus: number,
  indiceMoyen: number,
  tauxMarginalImposition: number,
  remboursementEmprunt: number,
  currentYear: number
): ProjectionDataItem[] {
  const projectionData: ProjectionDataItem[] = [];
  let cumulTresorerie = 0;
  let limitExceededYear = 0;

  for (let i = 0; i < 30; i++) {
    // Year calculation (using 1-based index for year number)
    const annee = currentYear + i + 1;

    // Calculate annual revenue with index increase
    const produits = Math.round(totalRevenus * (1 + indiceMoyen) ** i);

    // Check if produits exceed 15,000€ limit before calculating other values
    if (produits > 15000 && limitExceededYear === 0) {
      limitExceededYear = annee;

      // Still calculate all values for the limit year
      // 30% flat-rate deduction for Micro-Fonciers regime
      const abattement = Math.round(produits * 0.3);

      // Taxable income after deduction
      const resultatImposable = produits - abattement;

      // Income tax based on marginal tax rate
      const impot = Math.round(resultatImposable * tauxMarginalImposition);

      // Social charges (17.2%)
      const prelevements = Math.round(resultatImposable * 0.172);

      // Deductible CSG (6.8%)
      const csgDed = Math.round(resultatImposable * 0.068);

      // Total tax (income tax + social charges - deductible CSG)
      const irpp = impot + prelevements - csgDed;

      // Cash flow before loan payments
      const tresorerieHorsEmprunts = produits - irpp;

      // Total cash flow after loan payments (if applicable)
      const tresorerieTotale =
        tresorerieHorsEmprunts - (i < 25 ? remboursementEmprunt : 0);

      // Cumulative cash flow (running total)
      cumulTresorerie += tresorerieTotale;

      // Add the year when the limit is exceeded with a flag
      projectionData.push({
        annee,
        produits,
        abattement,
        resultatImposable,
        impot,
        prelevements,
        csgDed,
        irpp,
        tresorerieHorsEmprunts,
        tresorerieTotale,
        cumulTresorerie,
        totalTresorerieGeneree: 0,
        limitExceeded: true,
        limitExceededYear: annee,
      });

      // Stop the loop after adding the year when the limit is exceeded
      break;
    }

    // 30% flat-rate deduction for Micro-Fonciers regime
    const abattement = Math.round(produits * 0.3);

    // Taxable income after deduction
    const resultatImposable = produits - abattement;

    // Income tax based on marginal tax rate
    const impot = Math.round(resultatImposable * tauxMarginalImposition);

    // Social charges (17.2%)
    const prelevements = Math.round(resultatImposable * 0.172);

    // Deductible CSG (6.8%)
    const csgDed = Math.round(resultatImposable * 0.068);

    // Total tax (income tax + social charges - deductible CSG)
    const irpp = impot + prelevements - csgDed;

    // Cash flow before loan payments
    const tresorerieHorsEmprunts = produits - irpp;

    // Total cash flow after loan payments (if applicable)
    // Loan payments only apply for the first 25 years
    const tresorerieTotale =
      tresorerieHorsEmprunts - (i < 25 ? remboursementEmprunt : 0);

    // Cumulative cash flow (running total)
    cumulTresorerie += tresorerieTotale;

    // Add this year's data to the projection array
    projectionData.push({
      annee,
      produits,
      abattement,
      resultatImposable,
      impot,
      prelevements,
      csgDed,
      irpp,
      tresorerieHorsEmprunts,
      tresorerieTotale,
      cumulTresorerie,
      // This will be updated later with the corresponding plusValue.totalTresorerieGeneree
      totalTresorerieGeneree: 0,
      limitExceeded: false,
    });
  }

  return projectionData;
}

/**
 * Calculates plus-value data for 30 years or until the 15,000€ limit is exceeded
 * Based on the Excel formulas provided
 * @param limitExceededYear The year when the 15,000€ limit will be exceeded (if applicable)
 */
function calculatePlusValueData(
  montantInvestissement: number,
  nombreParts: number,
  investementCalculatorData: IInvestementCalculatorData,
  limitExceededYear?: number
): PlusValueDataItem[] {
  const prixAchat = montantInvestissement;
  const plusValueData: PlusValueDataItem[] = [];
  const startYear =
    new Date(investementCalculatorData.dateAcquisition).getFullYear() + 1;
  const { soldByYear } = investementCalculatorData;
  // Fixed acquisition costs
  const initialAugmentationPrixAcquisition = 15000;
  const laterAugmentationPrixAcquisition = 45000;

  // Calculate the maximum number of years to process
  // If limitExceededYear is provided, we'll stop at that year
  const maxYears = limitExceededYear
    ? Math.min(30, limitExceededYear - startYear + 1)
    : 30;
  for (let i = 0; i < maxYears; i++) {
    // Year calculation (using 1-based index for year number)
    const annee = i + 1;

    // Calculate property value with appreciation
    // For the first year, use the initial investment * 1.03
    // For subsequent years, use the previous year's price * 1.03
    let prixVente: number;
    if (i === 0) {
      prixVente = Math.round(prixAchat * 1.03);
    } else {
      prixVente = Math.round(plusValueData[i - 1].prixVente * 1.03);
    }

    // No additional price increase
    const augmentationPrixVente = 0;

    // Acquisition costs increase after 5 years
    const augmentationPrixAcquisition =
      i < 5
        ? initialAugmentationPrixAcquisition
        : laterAugmentationPrixAcquisition;

    // Calculate plus-value (price - acquisition cost - acquisition increase)
    // This matches the Excel formula: =+C43+C44-C45-C46
    const plusValue =
      prixVente +
      augmentationPrixVente -
      prixAchat -
      augmentationPrixAcquisition;

    // Calculate plus-value per part (only if positive)
    // This matches the Excel formula: =IF(C47>0,+C47/C48,0)
    const plusValueParPart = plusValue > 0 ? plusValue / nombreParts : 0;

    // Income tax abatement calculations
    // Based on the Excel formula for Taux Abattement Impôt sur le revenu
    // 0% for first 5 years, then 6% per year, capped at 100% after 21 years
    let tauxAbattementImpotRevenu = 0;
    if (i < 5) {
      tauxAbattementImpotRevenu = 0;
    } else if (i === 5) {
      tauxAbattementImpotRevenu = 0.06; // 6% at year 6
    } else if (i < 21) {
      // Add 6% each year, based on the formula =+H51+6%
      tauxAbattementImpotRevenu = 0.06 * (i - 4); // i-4 because we start at 6% for year 6
    } else {
      tauxAbattementImpotRevenu = 1; // 100% after 21 years
    }

    // Calculate abattement based on the Excel formula: =+C47*C51
    const abattementImpotRevenu = plusValue * tauxAbattementImpotRevenu;

    // Calculate base taxable based on the Excel formula: =IF(C47>0,+C47-C52,0)
    const baseTaxableImpotRevenu =
      plusValue > 0 ? plusValue - abattementImpotRevenu : 0;

    // Calculate base taxable per part based on the Excel formula: =C53/C48
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;

    // Calculate income tax based on the Excel formula: =+C53*0.19
    const impotRevenu = baseTaxableImpotRevenu * 0.19;

    // Social charges abatement calculations
    // Based on the Excel formula for Taux Abattement Prélèvements Sociaux
    let tauxAbattementPrelevementsSociaux = 0;
    if (i < 5) {
      tauxAbattementPrelevementsSociaux = 0;
    } else if (i === 5) {
      tauxAbattementPrelevementsSociaux = 0.0165; // 1.65% at year 6
    } else if (i < 21) {
      // Add 1.65% each year, based on the formula =+H57+1.65%
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 4); // i-4 because we start at 1.65% for year 6
    } else if (i === 21) {
      tauxAbattementPrelevementsSociaux = 0.28; // 28% at year 22
    } else if (i === 22) {
      tauxAbattementPrelevementsSociaux = 0.37; // 37% at year 23
    } else if (i === 23) {
      tauxAbattementPrelevementsSociaux = 0.46; // 46% at year 24
    } else if (i === 24) {
      tauxAbattementPrelevementsSociaux = 0.55; // 55% at year 25
    } else if (i === 25) {
      tauxAbattementPrelevementsSociaux = 0.64; // 64% at year 26
    } else if (i === 26) {
      tauxAbattementPrelevementsSociaux = 0.73; // 73% at year 27
    } else if (i === 27) {
      tauxAbattementPrelevementsSociaux = 0.82; // 82% at year 28
    } else if (i === 28) {
      tauxAbattementPrelevementsSociaux = 0.91; // 91% at year 29
    } else {
      tauxAbattementPrelevementsSociaux = 1; // 100% at year 30
    }

    // Calculate abattement based on the Excel formula: =+C47*C57
    const abattementPrelevementsSociaux =
      plusValue * tauxAbattementPrelevementsSociaux;

    // Calculate base taxable based on the Excel formula: =+C47-C58
    const baseTaxablePrelevementsSociaux =
      plusValue - abattementPrelevementsSociaux;

    // Calculate social charges based on the Excel formula: =IF(C59>0,+C59*0.172,0)
    const prelevementsSociauxPlusValue =
      baseTaxablePrelevementsSociaux > 0
        ? baseTaxablePrelevementsSociaux * 0.172
        : 0;

    // No additional taxation for now (would require lookup table)
    // In the Excel, this uses VLOOKUP: =VLOOKUP((C54),'BAREME TAX° SUP°'!$B:$F,4,TRUE)
    const taxationSupplementaire = 0;

    // Total tax on capital gain based on the Excel formula: =+C55+C60+C64
    const totalImpositionPlusValue =
      impotRevenu + prelevementsSociauxPlusValue + taxationSupplementaire;
    // Calculate total cash generated from sale based on the Excel formula: =+C43-'Onglet Emprunt'!$K$24-C65
    // Get the remaining loan amount for this year from soldByYear, or 0 if not available
    const remainingLoanAmount = soldByYear[startYear + annee] || 0;
    // Calculate total cash generated from sale: sale price - tax on capital gain - remaining loan
    const totalTresorerieGeneree =
      prixVente - totalImpositionPlusValue - remainingLoanAmount;

    plusValueData.push({
      annee,
      prixVente,
      augmentationPrixVente,
      prixAchat,
      augmentationPrixAcquisition,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociauxPlusValue,
      taxationSupplementaire,
      totalImpositionPlusValue,
      totalTresorerieGeneree,
    });
  }

  return plusValueData;
}
