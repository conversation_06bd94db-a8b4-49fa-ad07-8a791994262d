import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  LMPSansTVAFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/lmp-sans-tva';

/**
 * Calculates all the data for the LMP sans TVA tax regime
 * @param data Input data from the parent component
 * @returns Calculated data for the LMP sans TVA form
 */
export function calculateLMPSansTVAData(
  data: FormDataInvestementAnalysistSteps
): LMPSansTVAFormData {
  // Get values from previous steps
  const locationsMeublees = data?.realEstateData?.locationsMeublees || 'Non';
  const monthlyPayment = data?.investementCalculatormonthlyPayment || 0;
  const locationsSaisonnieresMeublees =
    data?.realEstateData?.locationsSaisonnieres || 'Non';

  // Determine if either location type is 'Oui'
  const hasMeubleeOrSaisonniere =
    locationsMeublees === 'Oui' || locationsSaisonnieresMeublees === 'Oui';
  const concatener = hasMeubleeOrSaisonniere ? 'Oui' : 'Non';

  // Get rental income - following Excel formula: =+'Cas d''étude - 1ère année'!B13+'Profil Investisseur'!J31
  const caseStudyRentalIncome =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;
  const profileInvestorRentalIncome = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).reduce((sum, asset) => {
    return sum + (parseFloat(asset.annualRent) || 0);
  }, 0);

  const revenusLocatifsMeublesChargesComprises =
    caseStudyRentalIncome + profileInvestorRentalIncome;

  // Get number of parts
  const nombrePartsFoyerFiscal =
    data?.investementCalculatorData?.nombreParts || 1;

  // Calculate limit
  const limiteRevenusLocatifsMeuble = nombrePartsFoyerFiscal * 23000;

  // Check if limit is reached
  const limiteAtteinte =
    revenusLocatifsMeublesChargesComprises <= limiteRevenusLocatifsMeuble
      ? 'NON'
      : 'OUI';

  // Check if other rental income exists
  const revenusLocatifsAutres =
    data?.investementCalculatorData?.revenuFiscalReference > 0 ? 'OUI' : 'NON';

  // Get micro-BIC rental income
  const revenusLocatifsMeublesMicroBIC = profileInvestorRentalIncome || 0;

  // Check if real regime can be maintained
  const possibiliteMaintenirRegimeReel =
    revenusLocatifsMeublesMicroBIC > 0 ? 'NON' : 'OUI';

  // Check if BIC real regime is applicable
  const regimeLMPApplicable =
    concatener !== 'Oui'
      ? 'NON'
      : possibiliteMaintenirRegimeReel === 'NON'
        ? 'NON'
        : limiteAtteinte === 'OUI' && revenusLocatifsAutres === 'OUI'
          ? 'OUI'
          : 'SUR OPTION';

  // Get values for calculations
  const prixAcquisition = data?.realEstateData?.prixAcquisitionMontant || 0;
  const chargesEntretien =
    data?.investementCalculatorData?.chargesEntretien || 0;
  const chargesLocatives =
    data?.investementCalculatorData?.chargesLocatives || 0;
  const assuranceNonOccupant =
    data?.investementCalculatorData?.assurancesNonOccupant || 0;
  const assuranceEmprunteur =
    data?.investementCalculatorData?.assurancesEmprunteur || 0;
  const fraisEmprunts = data?.investementCalculatorData?.fraisEmprunts || 0;
  const taxesFoncieres = data?.investementCalculatorData?.taxesFoncieres || 0;
  const cfe = data?.investementCalculatorData?.CFE || 0;
  const fraisBancaires = data?.investementCalculatorData?.fraisBancaires || 0;
  const honorairesComptables =
    data?.investementCalculatorData?.fraisComptables || 0;
  const honorairesJuridiques =
    data?.investementCalculatorData?.fraisJuridiques || 0;
  const fraisDomiciliation = 0; // Not provided in the data, using 0 as default
  const amortissementAnnuel =
    data?.investementCalculatorData?.amortissementAnnuel || 0;
  const tauxMarginalImposition =
    data?.investementCalculatorData?.tauxMarginalImposition || 0.3;
  const globalRevenuesExcludingLMP =
    data?.investementCalculatorData?.revenuFiscalReference || 0;

  // Set index value
  const indiceMoyen =
    data?.realEstateData?.applicationIndice === 'Oui' ? 0.02 : 0;

  // Initialize arrays for projection and plus-value data
  const projectionData: ProjectionDataItem[] = [];
  const plusValueData: PlusValueDataItem[] = [];

  // Get start year from loan data or use current year
  const startYear = new Date().getFullYear();
  let rentalIncome = revenusLocatifsMeublesChargesComprises;
  let cumulativeCashFlow = 0;

  // Generate 30-year projection data
  for (let i = 0; i < 30; i++) {
    const year = startYear + i;

    // Calculate rental income with index
    if (i > 0) {
      rentalIncome =
        indiceMoyen > 0.00001 ? rentalIncome * (1 + indiceMoyen) : 0;
    }

    // Calculate taxes foncières with 5% annual increase
    const taxesFoncieresYear =
      i === 0 ? taxesFoncieres : projectionData[i - 1].propertyTaxes * 1.05;

    // Get loan interest for the year
    // This would normally come from a loan calculation table
    const interetsEmprunt =
      i < 25 ? (monthlyPayment ? monthlyPayment * 12 * 0.7 : 0) : 0; // Simplified calculation

    // Calculate management charges
    const managementCharges = chargesEntretien;
    const rentalCharges = chargesLocatives;
    const nonOccupantInsurance = assuranceNonOccupant;
    const loanInsurance = assuranceEmprunteur;
    const loanFees = i === 0 ? fraisEmprunts : 0;
    const propertyTaxes = taxesFoncieresYear;
    const cfeYear = i === 0 ? 0 : cfe;
    const loanInterest = interetsEmprunt;
    const bankFees = fraisBancaires;
    const accountingFees = honorairesComptables;
    const legalFees = honorairesJuridiques;
    const domiciliationFees = fraisDomiciliation;
    const amortization = amortissementAnnuel;

    // Calculate accounting result
    const accountingResult =
      rentalIncome -
      (managementCharges +
        rentalCharges +
        nonOccupantInsurance +
        loanInsurance +
        loanFees +
        propertyTaxes +
        cfeYear +
        loanInterest +
        bankFees +
        accountingFees +
        legalFees +
        domiciliationFees +
        amortization);

    // Calculate fiscal result
    const fiscalResult = accountingResult;

    // Check if deficit
    const isDeficit = fiscalResult < 0 ? 'OUI' : 'NON';

    // Check if deficit is greater than global revenues
    const isDeficitGreaterThanGlobalRevenues =
      -fiscalResult > globalRevenuesExcludingLMP ? 'OUI' : 'NON';

    // Concatenate deficit status
    const concatenatedStatus = `${isDeficit}${isDeficitGreaterThanGlobalRevenues}`;

    // Calculate deficit imputable to global revenues
    const deficitImputableToGlobalRevenues =
      concatenatedStatus === 'OUIOUI'
        ? fiscalResult + globalRevenuesExcludingLMP
        : concatenatedStatus === 'OUINON'
          ? fiscalResult
          : 0;

    // Calculate reportable fiscal deficit
    const reportableFiscalDeficit =
      isDeficitGreaterThanGlobalRevenues === 'OUI'
        ? fiscalResult + globalRevenuesExcludingLMP
        : 0;

    // Calculate revenues for SSI calculation
    const revenuesForSSICalculation = fiscalResult <= 0 ? 0 : fiscalResult;

    // Calculate SSI contributions (minimum 1150)
    const ssiContributions = revenuesForSSICalculation * 0.35;

    // Calculate IR taxable base
    const irTaxableBase =
      fiscalResult <= 0 ? 0 : fiscalResult - ssiContributions;

    // Calculate income tax
    const incomeTax =
      irTaxableBase <= 0 ? 0 : irTaxableBase * tauxMarginalImposition;

    // Calculate total taxes
    const totalTaxes = ssiContributions + incomeTax;

    // Calculate cash flow excluding loans
    const cashFlowExcludingLoans =
      rentalIncome -
      (managementCharges +
        rentalCharges +
        nonOccupantInsurance +
        loanInsurance +
        propertyTaxes +
        cfeYear +
        bankFees +
        accountingFees +
        legalFees +
        domiciliationFees) -
      totalTaxes;

    // Calculate loan repayments based on duration
    const loanRepayment15Years =
      i < 15
        ? monthlyPayment
          ? monthlyPayment * 12 - loanInterest - loanFees
: 0
        : 0;

    const loanRepayment20Years =
      i < 20
        ? monthlyPayment
          ? monthlyPayment * 12 - loanInterest - loanFees
: 0
        : 0;

    const loanRepayment25Years =
      i < 25
        ? monthlyPayment
          ? monthlyPayment * 12 - loanInterest - loanFees
: 0
        : 0;

    // Calculate total cash flow based on loan duration
    const loanDuration = data?.investementCalculatorData?.dureeDetention || 15;
    let loanRepayment = 0;

    if (loanDuration === 15) {
      loanRepayment = loanRepayment15Years;
    } else if (loanDuration === 20) {
      loanRepayment = loanRepayment20Years;
    } else if (loanDuration === 25) {
      loanRepayment = loanRepayment25Years;
    }

    const totalCashFlow = cashFlowExcludingLoans - loanRepayment;

    // Update cumulative cash flow
    cumulativeCashFlow += totalCashFlow;

    // Add to projection data
    projectionData.push({
      year,
      rentalIncome,
      maintenanceCharges: managementCharges,
      rentalCharges,
      nonOccupantInsurance,
      loanInsurance,
      loanFees,
      propertyTaxes,
      cfe: cfeYear,
      loanInterest,
      bankFees,
      accountingFees,
      legalFees,
      domiciliationFees,
      amortization,
      accountingResult,
      fiscalResult,
      isDeficit,
      globalRevenuesExcludingLMP,
      isDeficitGreaterThanGlobalRevenues,
      concatenatedStatus,
      deficitImputableToGlobalRevenues,
      reportableFiscalDeficit,
      revenuesForSSICalculation,
      ssiContributions,
      irTaxableBase,
      incomeTax,
      totalTaxes,
      cashFlowExcludingLoans,
      loanRepayment15Years,
      loanRepayment20Years,
      loanRepayment25Years,
      totalCashFlow,
      cumulativeCashFlow,
    });

    // Generate plus-value data
    const salePrice = prixAcquisition * Math.pow(1.03, i + 1);
    const salePriceIncrease = 0; // Placeholder
    const acquisitionPrice = prixAcquisition;
    const acquisitionPriceIncrease = prixAcquisition * 0.075; // 7.5% increase
    const plusValue =
      salePrice +
      salePriceIncrease -
      (acquisitionPrice + acquisitionPriceIncrease);

    // Calculate net book value and deducted amortization
    const netBookValue = prixAcquisition - amortissementAnnuel * (i + 1);
    const deductedAmortization = amortissementAnnuel * (i + 1);

    // Calculate fiscal plus value
    const fiscalPlusValue = salePrice - netBookValue;

    // Get fiscal result excluding plus value
    const fiscalResultExcludingPlusValue = fiscalResult;

    // Calculate fiscal result with plus value
    const fiscalResultWithPlusValue =
      fiscalResultExcludingPlusValue + fiscalPlusValue;

    // Calculate short term plus value (equal to deducted amortization)
    const shortTermPlusValue = deductedAmortization;

    // Calculate short term taxation
    const marginalTaxRate = tauxMarginalImposition;
    const shortTermIncomeTax = shortTermPlusValue * marginalTaxRate;
    const ssiContributionsPlusValue = shortTermPlusValue * 0.35;
    const totalShortTermTaxation =
      shortTermIncomeTax + ssiContributionsPlusValue;

    // Calculate long term plus value with abatement based on years
    let longTermPlusValue = 0;
    if (i >= 2) {
      // Apply abatement based on years
      let abatementRate = 0;
      if (i >= 5) {
        abatementRate = Math.min(0.1 * (i - 4), 1); // 10% per year after 5 years, max 100%
      }

      longTermPlusValue = plusValue * (1 - abatementRate);
    }

    // Calculate long term taxation
    const incomeTax12_8Percent =
      longTermPlusValue <= 0 ? 0 : longTermPlusValue * 0.128;
    const socialContributions17_2Percent =
      longTermPlusValue <= 0 ? 0 : longTermPlusValue * 0.172;
    const totalLongTermTaxation =
      incomeTax12_8Percent + socialContributions17_2Percent;

    // Calculate remaining loan
    const remainingLoan =
      i < 25
        ? monthlyPayment
          ? monthlyPayment * 12 * (25 - i)
: 0
        : 0;

    // Calculate treasury after loan repayment
    const treasuryAfterLoanRepayment =
      salePrice -
      totalShortTermTaxation -
      totalLongTermTaxation -
      remainingLoan;

    // Add to plus-value data
    plusValueData.push({
      year,
      salePrice,
      salePriceIncrease,
      acquisitionPrice,
      acquisitionPriceIncrease,
      plusValue,
      netBookValue,
      deductedAmortization,
      fiscalPlusValue,
      fiscalResultExcludingPlusValue,
      fiscalResultWithPlusValue,
      shortTermPlusValue,
      marginalTaxRate,
      shortTermIncomeTax,
      ssiContributions: ssiContributionsPlusValue,
      totalShortTermTaxation,
      longTermPlusValue,
      incomeTax12_8Percent,
      socialContributions17_2Percent,
      totalLongTermTaxation,
      remainingLoan,
      treasuryAfterLoanRepayment,
    });
  }

  return {
    locationsMeublees,
    locationsSaisonnieresMeublees,
    concatener,
    revenusLocatifsMeublesChargesComprises,
    nombrePartsFoyerFiscal,
    limiteRevenusLocatifsMeuble,
    limiteAtteinte,
    revenusLocatifsAutres,
    revenusLocatifsMeublesMicroBIC,
    possibiliteMaintenirRegimeReel,
    conclusion: regimeLMPApplicable,
    regimeLMPApplicable,
    indiceMoyen,
    projectionData,
    plusValueData,
  };
}
