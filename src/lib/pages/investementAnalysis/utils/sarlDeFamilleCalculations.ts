import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  SARLDeFamilleFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/sarl-de-famille';

/**
 * Calculates all the data for the SARL DE FAMILLE tax regime
 * @param data Input data from the parent component
 * @returns Calculated data for the SARL DE FAMILLE form
 */
export function calculateSARLDeFamilleData(
  data: FormDataInvestementAnalysistSteps
): SARLDeFamilleFormData {
  // Get values from previous steps
  const locationsMeublees = data?.realEstateData?.locationsMeublees || 'Non';
  const nombreAssociesRequis = data?.realEstateData?.nombreAssocies >= 2 ? 'OUI' : 'NON';
  const associesLigneDirecte = data?.realEstateData?.sarlAssociesLigneDirecte || 'Non';
  const associesFreresSoeurs = data?.realEstateData?.sarlAssociesFreresSoeurs || 'Non';
  const associesConjointsPACS = data?.realEstateData?.sarlAssociesConjointsPACS || 'Non';

  // Calculate eligibility for SARL DE FAMILLE
  let eligibiliteSARLDeFamille = 'NON';
  if (locationsMeublees === 'Non') {
    eligibiliteSARLDeFamille = 'NON';
  } else if (nombreAssociesRequis === 'OUI') {
    eligibiliteSARLDeFamille = 'OUI';
  } else if (associesLigneDirecte === 'Oui' || associesFreresSoeurs === 'Oui' || associesConjointsPACS === 'Oui') {
    eligibiliteSARLDeFamille = 'OUI';
  }

  // Get rental income and fiscal data
  const revenusLocatifsMeublesChargesComprises = 25000; // Default value, should be from data
  const nombrePartsFoyerFiscal = data?.investementCalculatorData?.nombreParts || 1;
  const limiteRevenusLocatifsMeuble = nombrePartsFoyerFiscal * 23000;
  const limiteAtteinte = revenusLocatifsMeublesChargesComprises <= limiteRevenusLocatifsMeuble ? 'NON' : 'OUI';

  // Calculate if other rental income exists
  const revenusLocatifsAutres = 'NON'; // Default value, should be calculated based on data

  // Check conditions for BIC Réel regime
  const hasLimiteAtteinte = limiteAtteinte === 'OUI';
  const hasRevenusLocatifsAutres = revenusLocatifsAutres === 'OUI';

  // Set concatener to "Oui" or "Non" based on conditions
  const concatener = (hasLimiteAtteinte || hasRevenusLocatifsAutres) ? 'Oui' : 'Non';

  // Determine if BIC Réel regime is applicable
  const regimeBICReelApplicable = concatener === 'Oui' ? 'NON' : 'OUI';

  // Determine if SARL DE FAMILLE regime is applicable
  const regimeSARLDeFamilleApplicable = locationsMeublees === 'Non' ? 'NON' :
                                       (eligibiliteSARLDeFamille + regimeBICReelApplicable === 'OUIOUI' ? 'OUI' : 'NON');

  // Initialize arrays for projection and plus-value data
  const projectionData: ProjectionDataItem[] = [];
  const plusValueData: PlusValueDataItem[] = [];

  // Get values for calculations
  const indiceMoyen = data?.realEstateData?.applicationIndice === 'Oui' ? 0.02 : 0;
  const startYear = new Date().getFullYear();
  const prixAcquisition = data?.investementCalculatorData?.montantInvestissement || 0;
  const tauxMarginalImposition = data?.investementCalculatorData?.tauxMarginalImposition || 0.3;

  // Get initial values for the first year
  let rentalIncome = data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;
  const maintenanceCharges = data?.investementCalculatorData?.chargesEntretien || 0;
  const rentalCharges = data?.investementCalculatorData?.chargesLocatives || 0;
  const nonOccupantInsurance = data?.investementCalculatorData?.assurancesNonOccupant || 0;
  const loanInsurance = data?.investementCalculatorData?.assurancesEmprunteur || 0;
  const loanFees = data?.investementCalculatorData?.fraisEmprunts || 0;
  const taxesFoncieres = data?.investementCalculatorData?.taxesFoncieres || 0;
  const cfeYear = data?.investementCalculatorData?.CFE || 0;
  const bankFees = data?.investementCalculatorData?.fraisBancaires || 0;

  // Other charges
  const accountingFees = data?.realEstateData?.fraisComptables || 0;
  const legalFees = data?.realEstateData?.fraisJuridiques || 0;
  const domiciliationFees = 0; // Default value
  const managerRemuneration = data?.realEstateData?.fraisGerant || 0;

  // Amortization calculation
  const amortizationAmount = data?.investementCalculatorData?.montantInvestissement * 0.05; // 5% annual amortization

  // Generate 30-year projection data
  for (let i = 0; i < 30; i++) {
    const year = startYear + i;

    // Calculate rental income with index
    if (i > 0) {
      rentalIncome =
        indiceMoyen > 0.00001 ? rentalIncome * (1 + indiceMoyen) : 0;
    }

    // Calculate taxes foncières with 5% annual increase
    const propertyTaxes =
      i === 0 ? taxesFoncieres : projectionData[i - 1].propertyTaxes * 1.05;

    // Get loan interest for the year
    // This would normally come from a loan calculation table
    const loanInterest = i < 25 ?
      getLoanInterestForYear(data, i) : 0;

    // Calculate total management charges
    const totalManagementCharges =
      maintenanceCharges + rentalCharges + nonOccupantInsurance +
      loanInsurance + loanFees + propertyTaxes + cfeYear +
      loanInterest + bankFees;

    // Calculate total other charges
    const totalOtherCharges =
      accountingFees + legalFees + domiciliationFees + managerRemuneration;

    // Calculate amortization
    const amortization = amortizationAmount;
    const cumulativeAmortization = i === 0 ? amortization : projectionData[i - 1].cumulativeAmortization + amortization;

    // Calculate accounting result without amortization
    const accountingResult = rentalIncome - totalManagementCharges - totalOtherCharges;

    // Check if amortization is greater than accounting result
    const isAmortizationGreaterThanResult = amortization > accountingResult ? 'OUI' : 'NON';

    // Calculate accounting result with amortization
    const accountingResultWithAmortization = accountingResult - amortization;

    // Calculate reported amortization
    const reportedAmortization = isAmortizationGreaterThanResult === 'OUI' ? -accountingResultWithAmortization : 0;

    // Calculate cumulative reported amortization
    let cumulativeReportedAmortization = 0;
    if (i === 0) {
      cumulativeReportedAmortization = reportedAmortization;
    } else {
      if (isAmortizationGreaterThanResult === 'OUI') {
        cumulativeReportedAmortization = projectionData[i - 1].cumulativeReportedAmortization + reportedAmortization;
      } else if (accountingResultWithAmortization > projectionData[i - 1].cumulativeReportedAmortization) {
        cumulativeReportedAmortization = 0;
      } else {
        cumulativeReportedAmortization = projectionData[i - 1].cumulativeReportedAmortization - accountingResultWithAmortization;
      }
    }

    // Calculate fiscal result
    const fiscalResult = isAmortizationGreaterThanResult === 'OUI' ? 0 :
                        (cumulativeReportedAmortization > accountingResultWithAmortization ? 0 :
                        (accountingResultWithAmortization - cumulativeReportedAmortization));

    // Calculate income tax
    const incomeTax = fiscalResult < 0 ? 0 : fiscalResult * tauxMarginalImposition;

    // Calculate social contributions
    const socialContributions = fiscalResult < 0 ? 0 : fiscalResult * 0.17;

    // Calculate CSG deduction
    const csgDeduction = fiscalResult < 0 ? 0 : -fiscalResult * 0.07;

    // Calculate IRPP
    const irpp = incomeTax + socialContributions + csgDeduction;

    // Minimum social contributions for SARL de famille manager
    const minimumSocialContributions = 1150;

    // Calculate treasury without loans
    const treasuryWithoutLoans = accountingResult - irpp - minimumSocialContributions;

    // Calculate loan repayments
    const loanRepayment15Years = getLoanRepaymentForYear(data, 15, i);
    const loanRepayment20Years = getLoanRepaymentForYear(data, 20, i);
    const loanRepayment25Years = getLoanRepaymentForYear(data, 25, i);

    // Calculate total treasury
    const totalTreasury = treasuryWithoutLoans - loanRepayment15Years - loanRepayment20Years - loanRepayment25Years;

    // Calculate cumulative treasury
    const cumulativeTreasury = i === 0 ? totalTreasury : projectionData[i - 1].cumulativeTreasury + totalTreasury;

    // Add to projection data
    projectionData.push({
      year,
      rentalIncome,
      maintenanceCharges,
      rentalCharges,
      nonOccupantInsurance,
      loanInsurance,
      loanFees,
      propertyTaxes,
      cfe: cfeYear,
      loanInterest,
      bankFees,
      totalManagementCharges,
      accountingFees,
      legalFees,
      domiciliationFees,
      managerRemuneration,
      totalOtherCharges,
      amortization,
      cumulativeAmortization,
      accountingResult,
      isAmortizationGreaterThanResult,
      accountingResultWithAmortization,
      reportedAmortization,
      cumulativeReportedAmortization,
      fiscalResult,
      incomeTax,
      socialContributions,
      csgDeduction,
      irpp,
      minimumSocialContributions,
      treasuryWithoutLoans,
      loanRepayment15Years,
      loanRepayment20Years,
      loanRepayment25Years,
      totalTreasury,
      cumulativeTreasury,
    });

    // Generate plus-value data
    const salePrice = prixAcquisition * Math.pow(1.03, i + 1);
    const salePriceIncrease = 0; // Placeholder
    const acquisitionPrice = prixAcquisition;
    const amortizationPracticed = cumulativeAmortization;
    const netAcquisitionPrice = acquisitionPrice - amortizationPracticed;
    const acquisitionPriceIncrease = prixAcquisition * 0.075; // 7.5% increase
    const plusValue = salePrice + salePriceIncrease - (netAcquisitionPrice + acquisitionPriceIncrease);
    const numberOfParts = nombrePartsFoyerFiscal;
    const plusValuePerPart = plusValue / numberOfParts;

    // Calculate income tax abatement rate based on years
    let incomeTaxAbatementRate = 0;
    if (i >= 5) {
      incomeTaxAbatementRate = Math.min(0.06 * (i - 4), 1); // 6% per year after 5 years, max 100%
    }

    // Calculate income tax abatement
    const incomeTaxAbatement = plusValue * incomeTaxAbatementRate;

    // Calculate income tax base
    const incomeTaxBase = plusValuePerPart > 0 ? plusValue - incomeTaxAbatement : 0;
    const incomeTaxBasePerPart = incomeTaxBase / numberOfParts;

    // Calculate income tax at 19%
    const incomeTax19Percent = incomeTaxBase * 0.19;

    // Calculate social contributions abatement rate based on years
    let socialContributionsAbatementRate = 0;
    if (i >= 5) {
      socialContributionsAbatementRate = Math.min(0.0165 * (i - 4), 1); // 1.65% per year after 5 years, max 100%
    }

    // Calculate social contributions abatement
    const socialContributionsAbatement = plusValue * socialContributionsAbatementRate;

    // Calculate social contributions base
    const socialContributionsBase = plusValue - socialContributionsAbatement;

    // Calculate social contributions at 17.2%
    const socialContributions17_2Percent = socialContributionsBase > 0 ? socialContributionsBase * 0.172 : 0;

    // Calculate additional taxation based on plus value per part
    let additionalTaxation = 0;
    if (plusValuePerPart > 50000) {
      // This is a simplified calculation - in reality would use a lookup table
      additionalTaxation = plusValuePerPart * 0.02;
    }

    // Calculate total taxation
    const totalTaxation = incomeTax19Percent + socialContributions17_2Percent + additionalTaxation;

    // Calculate remaining loan
    const remainingLoan = i < 25 ? getRemainingLoanForYear(data, i) : 0;

    // Calculate treasury after loan repayment
    const treasuryAfterLoanRepayment = salePrice - totalTaxation - remainingLoan;

    // Add to plus-value data
    plusValueData.push({
      year,
      salePrice,
      salePriceIncrease,
      acquisitionPrice,
      amortizationPracticed,
      netAcquisitionPrice,
      acquisitionPriceIncrease,
      plusValue,
      numberOfParts,
      plusValuePerPart,
      incomeTaxAbatementRate,
      incomeTaxAbatement,
      incomeTaxBase,
      incomeTaxBasePerPart,
      incomeTax19Percent,
      socialContributionsAbatementRate,
      socialContributionsAbatement,
      socialContributionsBase,
      socialContributions17_2Percent,
      additionalTaxation,
      totalTaxation,
      remainingLoan,
      treasuryAfterLoanRepayment,
    });
  }

  return {
    locationsMeublees,
    nombreAssociesRequis,
    associesLigneDirecte,
    associesFreresSoeurs,
    associesConjointsPACS,
    eligibiliteSARLDeFamille,
    revenusLocatifsMeublesChargesComprises,
    nombrePartsFoyerFiscal,
    limiteRevenusLocatifsMeuble,
    limiteAtteinte,
    revenusLocatifsAutres,
    concatener,
    regimeBICReelApplicable,
    regimeSARLDeFamilleApplicable,
    indiceMoyen,
    projectionData,
    plusValueData,
  };
}

/**
 * Helper function to get loan interest for a specific year
 */
function getLoanInterestForYear(data: FormDataInvestementAnalysistSteps, year: number): number {
  // This is a simplified calculation - in reality would use loan amortization table
  const loanAmount = data.investementCalculatorData.loanAmount;
  const loanTerm = 25; // Default to 25 years
  const interestRate = 0.03; // 3% interest rate

  if (year >= loanTerm) return 0;

  // Simple declining interest calculation
  return loanAmount * interestRate * (1 - year / loanTerm);
}

/**
 * Helper function to get loan repayment for a specific year based on loan term
 */
function getLoanRepaymentForYear(data: FormDataInvestementAnalysistSteps, loanTerm: number, year: number): number {
  // Check if the loan term matches the actual loan term
  const actualLoanTerm = 25; // Default to 25 years

  if (actualLoanTerm !== loanTerm || year >= loanTerm) return 0;

  // Simple calculation for loan repayment (principal only)
  const loanAmount = data.investementCalculatorData.loanAmount;
  return loanAmount / loanTerm;
}

/**
 * Helper function to get remaining loan for a specific year
 */
function getRemainingLoanForYear(data: FormDataInvestementAnalysistSteps, year: number): number {
  const loanAmount = data.investementCalculatorData.loanAmount;
  const loanTerm = 25; // Default to 25 years

  if (year >= loanTerm) return 0;

  // Simple calculation for remaining loan
  return loanAmount * (1 - year / loanTerm);
}
