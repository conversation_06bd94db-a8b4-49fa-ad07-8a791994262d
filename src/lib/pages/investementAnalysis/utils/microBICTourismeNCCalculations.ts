import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  MicroBICTourismeNCFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/micro-bic-tourisme-nc';
import type { AssetInfo } from '~/lib/types/mircro-foncier';

/**
 * Calculates all the data for the Micro-BIC Tourisme Non Classé tax regime
 * @param data Input data from the parent component
 * @param formatCurrency Function to format currency values
 * @returns Calculated data for the Micro-BIC Tourisme NC form
 */
export function calculateMicroBICTourismeNCData(
  data: FormDataInvestementAnalysistSteps,
  formatCurrency: (value: number) => string
): MicroBICTourismeNCFormData {
  // Get values from previous steps - following Excel formula: =+'Profil Investisseur'!I31+'Profil Investisseur'!J31+'Cas d''étude - 1ère année'!B13
  // In our context, we need to sum the relevant rental income values from the profile investor and case study data
  const profileInvestorRentalIncome = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).reduce((sum: number, asset: AssetInfo) => {
    return sum + (Number(asset.annualRent) || 0);
  }, 0);

  const caseStudyRentalIncome =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;

  // Calculate total rental income
  const loyersAnnuels = profileInvestorRentalIncome + caseStudyRentalIncome;

  // Get values for SCI parts, exceptional receipts, and advertising receipts
  const revenusSCI = data?.realEstateData?.revenusPartsSCI || 0;
  const recettesExceptionnelles =
    data?.realEstateData?.recettesExceptionnelles || 0;
  const recettesEmplacementsPublicitaires =
    data?.realEstateData?.recettesEmplacementsPublicitaires || 0;

  // Calculate total revenues
  const totalRevenus =
    loyersAnnuels +
    revenusSCI +
    recettesExceptionnelles +
    recettesEmplacementsPublicitaires;

  // Check if there are other rental incomes at the real regime
  const autresRevenusLocatifsReels = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).some(
    (asset: AssetInfo) =>
      asset.applicableTaxRegime === 'Réel' ||
      asset.applicableTaxRegime === 'Réel simplifié'
  )
    ? 'OUI'
    : 'NON';

  // Check if micro-BIC regime is possible
  const regimeMicroBICPossible = autresRevenusLocatifsReels === 'NON';

  // Get other rental incomes at the micro-BIC regime
  const autresRevenusLocatifsMicroBIC = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).reduce((sum: number, asset: AssetInfo) => {
    return asset.applicableTaxRegime === 'Micro-BIC'
      ? sum + (Number(asset.annualRent) || 0)
      : sum;
  }, 0);

  // Check if micro-BIC regime can be maintained (total revenues <= 15000)
  const possibiliteMaintienRegimeMicroBIC = totalRevenus <= 15000;

  // Concatenate conditions to check if micro-BIC regime is applicable
  const locationsMeublees = data?.realEstateData?.locationsMeublees || 'NON';
  const concatener = locationsMeublees === 'OUI' && regimeMicroBICPossible && possibiliteMaintienRegimeMicroBIC
                ? 'Oui'
                : 'Non';
  const regimeMicroBICApplicable = concatener;

  // Get application index
  const applicationIndice = data?.realEstateData?.applicationIndice || 'NON';

  // Calculate tax values
  const abattementForfaitaire = totalRevenus * 0.3; // 30% abatement for non-classified tourism
  const revenuNetImposable = totalRevenus - abattementForfaitaire;

  // Get marginal tax rate
  const tauxMarginalImposition =
    data?.investementCalculatorData?.tauxMarginalImposition || 0;

  // Calculate taxes
  const montantImpot = revenuNetImposable * tauxMarginalImposition;
  const prelevementsSociaux = revenuNetImposable * 0.172; // 17.2% social charges
  const csgDeductible = -revenuNetImposable * 0.07; // 7% deductible CSG
  const irpp = montantImpot + prelevementsSociaux + csgDeductible;
  const totalPrelevements = montantImpot + prelevementsSociaux;

  // Calculate cash flow
  const revenuNetApresImpot = totalRevenus - totalPrelevements;
  const tresorerieDegageeHorsEmprunts = revenuNetApresImpot;

  // Get loan repayment amount
  const remboursementEmprunt = 0; // This would come from loan data

  // Calculate total treasury
  const tresorerieTotaleDegagee =
    tresorerieDegageeHorsEmprunts - remboursementEmprunt;
  const tresorerieCumulee = tresorerieTotaleDegagee; // First year only, would be cumulative in projections

  // Calculate index for projections
  const indiceMoyen = applicationIndice === 'OUI' ? 0.02 : 0; // 2% if index is applied, 0% otherwise

  // Generate 30-year projections
  const projectionData: ProjectionDataItem[] = [];
  const plusValueData: PlusValueDataItem[] = [];

  // Get acquisition date and calculate start year
  const dateAcquisition =
    data?.investementCalculatorData?.dateAcquisition || '2023-01-01';
  const startYear = new Date(dateAcquisition).getFullYear();

  // Get number of parts
  const nombreParts = data?.investementCalculatorData?.nombreParts || 1;

  // Get property acquisition price
  const prixAcquisition = data?.realEstateData?.prixAcquisitionMontant || 0;

  // Generate projection data for 30 years
  let cumulTresorerie = 0;
  for (let i = 0; i < 30; i++) {
    const year = startYear + i;
    const produits =
      i === 0
        ? totalRevenus
        : projectionData[i - 1].produits * (1 + indiceMoyen);

    // Check if micro-BIC regime is still applicable (revenue <= 15000)
    const regimeApplicable = produits <= 15000;

    const abattement = produits * 0.3; // 30% abatement
    const resultatImposable = produits - abattement;
    const impot = resultatImposable * tauxMarginalImposition;
    const prelevements = resultatImposable * 0.172;
    const csgDed = -resultatImposable * 0.07;
    const irpp = impot + prelevements + csgDed;

    // Calculate treasury
    const tresorerieHorsEmprunts = produits - irpp;

    // Loan repayment based on loan duration
    // This is a simplified version, would need actual loan data
    const dureeEmprunt = 15; // Example duration
    const remboursementEmprunt = i < dureeEmprunt ? 0 : 0; // Would use actual loan payment

    const tresorerieTotale = tresorerieHorsEmprunts - remboursementEmprunt;
    cumulTresorerie += tresorerieTotale;

    projectionData.push({
      year,
      produits,
      abattement,
      resultatImposable,
      impot,
      prelevements,
      csgDed,
      irpp,
      tresorerieHorsEmprunts,
      remboursementEmprunt,
      tresorerieTotale,
      tresorerieCumulee: cumulTresorerie,
    });

    // Generate plus-value data
    const prixVente = prixAcquisition * Math.pow(1.03, i + 1); // 3% annual appreciation
    const augmentationPrixVente = 0; // Would be calculated based on actual data
    const augmentationPrixAcquisition = 0; // Would be calculated based on actual data

    const plusValue =
      prixVente +
      augmentationPrixVente -
      prixAcquisition -
      augmentationPrixAcquisition;
    const plusValueParPart = plusValue / nombreParts;

    // Calculate tax abatements based on holding period
    let tauxAbattementImpotRevenu = 0;
    if (i >= 21) {
      tauxAbattementImpotRevenu = 1; // 100% after 22 years
    } else if (i >= 5) {
      tauxAbattementImpotRevenu = 0.06 * (i - 5); // 6% per year after 6 years
    }

    let tauxAbattementPrelevementsSociaux = 0;
    if (i >= 29) {
      tauxAbattementPrelevementsSociaux = 1; // 100% after 30 years
    } else if (i >= 21) {
      tauxAbattementPrelevementsSociaux = 0.09 * (i - 21); // 9% per year after 22 years
    } else if (i >= 5) {
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 5); // 1.65% per year after 6 years
    }

    const abattementImpotRevenu = plusValue * tauxAbattementImpotRevenu;
    const baseTaxableImpotRevenu = Math.max(
      0,
      plusValue - abattementImpotRevenu
    );
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;
    const impotRevenu = baseTaxableImpotRevenu * 0.19; // 19% flat tax on real estate capital gains

    const abattementPrelevementsSociaux =
      plusValue * tauxAbattementPrelevementsSociaux;
    const baseTaxablePrelevementsSociaux = Math.max(
      0,
      plusValue - abattementPrelevementsSociaux
    );
    const prelevementsSociaux = baseTaxablePrelevementsSociaux * 0.172;

    // Additional taxation based on plus-value amount per part
    let taxationSupplementaire = 0;
    // This would be a complex calculation based on a tax scale
    // Simplified version for now
    if (baseTaxableImpotRevenuParPart > 50000) {
      taxationSupplementaire = (baseTaxableImpotRevenuParPart - 50000) * 0.02;
    }

    const totalImposition =
      impotRevenu + prelevementsSociaux + taxationSupplementaire;
    const tresorerieGeneree = prixVente - totalImposition;

    plusValueData.push({
      year,
      prixVente,
      augmentationPrixVente,
      prixAcquisition,
      augmentationPrixAcquisition,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociaux,
      taxationSupplementaire,
      totalImposition,
      tresorerieGeneree,
    });
  }

  // Prepare warning message if regime is not applicable
  const message = !regimeMicroBICApplicable
    ? "Régime Micro-BIC Tourisme Non Classé n'est plus applicable au cas étudié, un changement de régime est à opérer vers le régime BIC réel"
    : undefined;

  return {
    locationsMeublees,
    loyersAnnuels,
    autresRevenusLocatifsMeubles: '',
    revenusSCI,
    recettesExceptionnelles,
    recettesEmplacementsPublicitaires,
    autresRevenusLocatifsReels,
    regimeMicroBICPossible,
    autresRevenusLocatifsMicroBIC,
    possibiliteMaintienRegimeMicroBIC,
    totalRevenus,
    concatener,
    regimeMicroBICApplicable,
    applicationIndice,
    abattementForfaitaire,
    revenuNetImposable,
    tauxMarginalImposition,
    montantImpot,
    prelevementsSociaux,
    csgDeductible,
    irpp,
    totalPrelevements,
    revenuNetApresImpot,
    tresorerieDegageeHorsEmprunts,
    remboursementEmprunt,
    tresorerieTotaleDegagee,
    tresorerieCumulee,
    indiceMoyen,
    projectionData,
    plusValueData,
    message,
  };
}
