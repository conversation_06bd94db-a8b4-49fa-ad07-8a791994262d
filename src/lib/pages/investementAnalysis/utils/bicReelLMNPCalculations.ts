import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  BICReelLMNPFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/bic-reel-lmnp'; 
/**
 * Calculates all the data for the BIC-REEL LMNP tax regime
 * @param data Input data from the parent component
 * @returns Calculated data for the BIC-REEL LMNP form
 */
export function calculateBICReelLMNPData(
  data: FormDataInvestementAnalysistSteps
): BICReelLMNPFormData {
  // Get values from previous steps
  const locationsMeublees = data?.realEstateData?.locationsMeublees || 'Non';
  const locationsSaisonnieresMeublees =
    data?.realEstateData?.locationsSaisonnieres || 'Non';

  // Determine if either location type is 'Oui'
  const hasMeubleeOrSaisonniere = locationsMeublees === 'Oui' || locationsSaisonnieresMeublees === 'Oui';
  const concatener = hasMeubleeOrSaisonniere ? 'Oui' : 'Non';

  // Get rental income - following Excel formula: =+'Cas d''étude - 1ère année'!B13+'Profil Investisseur'!J31
  const caseStudyRentalIncome =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges + data?.investementCalculatorData?.chargesComprises;
  const profileInvestorRentalIncome =
    data.profileInvestorData?.taxRegimeTotals?.bic_reel_simplifie || 0;
  const revenusLocatifsMeublesChargesComprises =
    caseStudyRentalIncome + profileInvestorRentalIncome;

  // Get number of parts
  const nombrePartsFoyerFiscal =
    data?.investementCalculatorData?.nombreParts || 1;

  // Calculate limit
  const limiteRevenusLocatifsMeuble = nombrePartsFoyerFiscal * 23000;

  // Check if limit is reached
  const limiteAtteinte =
    revenusLocatifsMeublesChargesComprises <= limiteRevenusLocatifsMeuble
      ? 'NON'
      : 'OUI';

      const revenusLocatifsMeublesSuperieurMoitieRevenus = 
        revenusLocatifsMeublesChargesComprises > data?.investementCalculatorData?.revenuFiscalReference / 2
          ? 'OUI'
          : 'NON';

  // Get micro-BIC rental income
  const revenusLocatifsMeublesMicroBIC = data?.profileInvestorData?.taxRegimeTotals?.micro_bic || 0;

  // Check if real regime can be maintained
  const possibiliteMaintenirRegimeReel =
    revenusLocatifsMeublesMicroBIC > 0 ? 'NON' : 'OUI';

  // Determine if BIC-REEL LMNP regime is applicable
  let regimeBICReelApplicable = 'NON';
  if (concatener !== 'Oui') {
    regimeBICReelApplicable = 'NON';
  } else if (possibiliteMaintenirRegimeReel === 'NON') {
    regimeBICReelApplicable = 'NON';
  } else if (limiteAtteinte === 'OUI') {
    regimeBICReelApplicable = 'NON';
  } else {
    regimeBICReelApplicable = 'OUI';
  }

  // Get index value
  const applicationIndice = data?.realEstateData?.applicationIndice || 'Non';
  const indiceMoyen = applicationIndice === 'Oui' ? 0.02 : 0;

  // Generate projection data for 30 years
  const projectionData: ProjectionDataItem[] = [];
  const plusValueData: PlusValueDataItem[] = [];

  // Get loan start date
  const dateAcquisition =
    data?.investementCalculatorData?.dateAcquisition || '2020-12-31';
  const startYear = new Date(dateAcquisition).getFullYear();

  // Get tax rate
  const tauxMarginalImposition =
    data?.investementCalculatorData?.tauxMarginalImposition || 0.3;

  // Get property values
  const prixAcquisition = data?.realEstateData?.prixAcquisitionMontant || 0;

  // Get charges
  const chargesEntretien =
    data?.investementCalculatorData?.chargesEntretien || 0;
  const chargesLocatives =
    data?.investementCalculatorData?.chargesLocatives || 0;
  const assuranceNonOccupant =
    data?.investementCalculatorData?.assurancesNonOccupant || 0;
  const assuranceEmprunteur =
    data?.investementCalculatorData?.assurancesEmprunteur || 0;
  const fraisEmprunts = data?.investementCalculatorData?.fraisEmprunts || 0;
  const taxesFoncieres = data?.investementCalculatorData?.taxesFoncieres || 0;
  const cfe = data?.investementCalculatorData?.CFE || 0;
  const fraisBancaires = data?.investementCalculatorData?.fraisBancaires || 0;

  // Get honoraires
  const honorairesComptables =
    data?.investementCalculatorData?.fraisComptables || 0;
  const honorairesJuridiques =
    data?.investementCalculatorData?.fraisJuridiques || 0;
  const fraisDomiciliation = 0; // Not provided in the data, using default
  const remunerationGerant = data?.investementCalculatorData?.fraisGerant || 0;

  // Get amortization values
  const amortissementAnnuel =
    data?.investementCalculatorData?.quotePartGrosOeuvre || 0;

  // Generate projection data
  let rentalIncome = revenusLocatifsMeublesChargesComprises;
  let cumulativeCashFlow = 0;

  for (let i = 0; i < 30; i++) {
    const year = startYear + i;

    // Calculate rental income with index
    if (i > 0) {
      rentalIncome =
        indiceMoyen > 0.00001 ? rentalIncome * (1 + indiceMoyen) : 0;
    }

    // Calculate taxes foncières with 5% annual increase
    const taxesFoncieresYear =
      i === 0 ? taxesFoncieres : projectionData[i - 1].managementCharges * 1.05;

    // Get loan interest for the year
    // This would normally come from a loan calculation table
    const interetsEmprunt = 0; // Placeholder, would need actual loan data

    // Calculate management charges
    const managementCharges =
      chargesEntretien +
      chargesLocatives +
      assuranceNonOccupant +
      assuranceEmprunteur +
      fraisEmprunts +
      taxesFoncieresYear +
      cfe +
      interetsEmprunt +
      fraisBancaires;

    // Calculate other charges
    const otherCharges =
      honorairesComptables +
      honorairesJuridiques +
      fraisDomiciliation +
      remunerationGerant;

    // Calculate accounting result without amortization
    const accountingResultWithoutAmortization =
      rentalIncome - managementCharges - otherCharges;

    // Calculate amortization
    const amortization = amortissementAnnuel;

    // Calculate cumulative amortization
    const cumulativeAmortization =
      i === 0
        ? amortization
        : projectionData[i - 1].cumulativeAmortization + amortization;

    // Check if amortization is greater than accounting result
    const amortizationGreaterThanResult =
      amortization > accountingResultWithoutAmortization ? 'OUI' : 'NON';

    // Calculate accounting result BIC
    const accountingResultBIC =
      accountingResultWithoutAmortization - amortization;

    // Calculate reported amortization
    const reportedAmortization =
      amortizationGreaterThanResult === 'OUI' ? -accountingResultBIC : 0;

    // Calculate cumulative reported amortization
    const cumulativeReportedAmortization =
      i === 0
        ? reportedAmortization
        : projectionData[i - 1].cumulativeReportedAmortization +
          reportedAmortization;

    // Calculate cumulative reported amortization 2
    const cumulativeReportedAmortization2 = cumulativeReportedAmortization;

    // Calculate taxable result
    let taxableResult = 0;
    if (amortizationGreaterThanResult === 'OUI') {
      taxableResult = 0;
    } else if (cumulativeReportedAmortization2 > accountingResultBIC) {
      taxableResult = 0;
    } else if (cumulativeReportedAmortization2 < accountingResultBIC) {
      taxableResult = accountingResultBIC - cumulativeReportedAmortization2;
    }

    // Calculate income tax
    const incomeTax =
      taxableResult < 0 ? 0 : taxableResult * tauxMarginalImposition;

    // Calculate social charges (17%)
    const socialCharges = taxableResult < 0 ? 0 : taxableResult * 0.17;

    // Calculate deductible CSG (7%)
    const deductibleCSG = taxableResult < 0 ? 0 : -taxableResult * 0.07;

    // Calculate total tax
    const totalTax = incomeTax + socialCharges + deductibleCSG;

    // Calculate cash flow excluding loans
    const cashFlowExcludingLoans =
      accountingResultWithoutAmortization - totalTax;

    // Calculate loan repayments (would need actual loan data)
    const loanRepayments = 0; // Placeholder

    // Calculate total cash flow
    const totalCashFlow = cashFlowExcludingLoans - loanRepayments;

    // Calculate cumulative cash flow
    cumulativeCashFlow += totalCashFlow;

    // Add to projection data
    projectionData.push({
      year,
      rentalIncome,
      managementCharges,
      otherCharges,
      amortization,
      cumulativeAmortization,
      accountingResult: accountingResultWithoutAmortization,
      amortizationGreaterThanResult,
      accountingResultBIC,
      reportedAmortization,
      cumulativeReportedAmortization,
      cumulativeReportedAmortization2,
      taxableResult,
      incomeTax,
      socialCharges,
      deductibleCSG,
      totalTax,
      cashFlowExcludingLoans,
      loanRepayments,
      totalCashFlow,
      cumulativeCashFlow,
    });

    // Generate plus-value data
    const salePrice = prixAcquisition * Math.pow(1.03, i + 1);
    const salePriceIncrease = 0; // Placeholder
    const acquisitionPrice = prixAcquisition;
    const amortizationPracticed = cumulativeAmortization;
    const netAcquisitionPrice = acquisitionPrice - amortizationPracticed;
    const acquisitionPriceIncrease = 0; // Placeholder
    const plusValue =
      salePrice +
      salePriceIncrease -
      (netAcquisitionPrice + acquisitionPriceIncrease);
    const numberOfParts = nombrePartsFoyerFiscal;
    const plusValuePerPart = plusValue / numberOfParts;

    // Calculate abatement rates based on years of ownership
    let incomeTaxAbatementRate = 0;
    if (i >= 5) incomeTaxAbatementRate = 0.06 * (i - 4);
    if (i >= 22) incomeTaxAbatementRate = 1; // 100% after 22 years

    let socialChargesAbatementRate = 0;
    if (i >= 5) socialChargesAbatementRate = 0.0165 * (i - 4);
    if (i >= 22) socialChargesAbatementRate = 0.28 + 0.09 * (i - 21);
    if (i >= 30) socialChargesAbatementRate = 1; // 100% after 30 years

    // Calculate abatements
    const incomeTaxAbatement = plusValue * incomeTaxAbatementRate;
    const socialChargesAbatement = plusValue * socialChargesAbatementRate;

    // Calculate taxable bases
    const incomeTaxBase =
      plusValuePerPart > 0 ? plusValue - incomeTaxAbatement : 0;
    const incomeTaxBasePerPart = incomeTaxBase / numberOfParts;
    const socialChargesBase = plusValue - socialChargesAbatement;

    // Calculate taxes
    const incomeTax19Percent = incomeTaxBase * 0.19;
    const socialCharges17_2Percent =
      socialChargesBase > 0 ? socialChargesBase * 0.172 : 0;

    // Calculate additional taxation
    // This would be based on a tax table lookup
    const additionalTaxation = 0; // Placeholder
    const taxationAbatementField = ''; // Placeholder
    const taxationAbatementValue = 0; // Placeholder
    const additionalTaxationAmount = 0; // Placeholder

    // Calculate total plus-value tax
    const totalPlusValueTax =
      incomeTax19Percent + socialCharges17_2Percent + additionalTaxationAmount;

    // Calculate total treasury generated
    const totalTreasuryGenerated = salePrice - totalPlusValueTax;

    // Add to plus-value data
    plusValueData.push({
      year,
      salePrice,
      salePriceIncrease,
      acquisitionPrice,
      amortizationPracticed,
      netAcquisitionPrice,
      acquisitionPriceIncrease,
      plusValue,
      numberOfParts,
      plusValuePerPart,
      incomeTaxAbatementRate,
      incomeTaxAbatement,
      incomeTaxBase,
      incomeTaxBasePerPart,
      incomeTax19Percent,
      socialChargesAbatementRate,
      socialChargesAbatement,
      socialChargesBase,
      socialCharges17_2Percent,
      additionalTaxation,
      taxationAbatementField,
      taxationAbatementValue,
      additionalTaxationAmount,
      totalPlusValueTax,
      totalTreasuryGenerated,
    });
  }

  return {
    locationsMeublees,
    locationsSaisonnieresMeublees,
    concatener,
    revenusLocatifsMeublesChargesComprises,
    nombrePartsFoyerFiscal,
    limiteRevenusLocatifsMeuble,
    limiteAtteinte,
    revenusLocatifsMeublesSuperieurMoitieRevenus,
    revenusLocatifsMeublesMicroBIC,
    possibiliteMaintenirRegimeReel,
    regimeBICReelApplicable,
    indiceMoyen,
    projectionData,
    plusValueData,
  };
}
