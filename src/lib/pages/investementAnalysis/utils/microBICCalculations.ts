import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  MicroBICFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/micro-bic';
import type { AssetInfo } from '~/lib/types/mircro-foncier';

/**
 * Calculates all the data for the Micro-BIC tax regime
 * @param data Input data from the parent component
 * @param formatCurrency Function to format currency values
 * @returns Calculated data for the Micro-BIC form
 */
export function calculateMicroBICData(
  data: FormDataInvestementAnalysistSteps,
  formatCurrency: (value: number) => string
): MicroBICFormData {
  // Get values from previous steps - following Excel formula: =+'Profil Investisseur'!I31+'Profil Investisseur'!J31+'Profil Investisseur'!K31+'Cas d''étude - 1ère année'!B13
  // In our context, we need to sum the relevant rental income values from the profile investor and case study data
  const profileInvestorRentalIncome = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).reduce((sum, asset) => sum + (parseFloat(asset.annualRent) || 0), 0);
  const caseStudyRentalIncome =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;
  const loyersAnnuels = profileInvestorRentalIncome + caseStudyRentalIncome;
  const montantInvestissement =
    data?.realEstateData?.prixAcquisitionMontant || 0;
  // Get monthly payment and convert to annual
  const remboursementEmprunt = data?.investementCalculatorData.monthlyPayment * 12 || 0;

  // Get tax rate from case study data
  const tauxMarginalImposition =
    data?.investementCalculatorData?.tauxMarginalImposition || 0.3;

  // Get number of parts from case study data
  const nombreParts = data?.investementCalculatorData?.nombreParts || 1;

  // Get profile investor data
  const assetsInfoTable = data?.profileInvestorData?.assetsInfoTable || [];

  // Count properties with different rental modes and tax regimes
  let microBIC = 0;
  let bicReels = 0;

  assetsInfoTable.forEach((asset: AssetInfo) => {
    if (asset.applicableTaxRegime === 'Micro-BIC') {
      microBIC++;
    } else if (
      asset.applicableTaxRegime === 'BIC au régime réel' ||
      asset.applicableTaxRegime === 'BIC au régime réel simplifié'
    ) {
      bicReels++;
    }
  });

  // Micro-BIC conditions
  const locationsMeublees = data.realEstateData.locationsMeublees || 'Oui';
  const hasOtherBICReels = bicReels > 0;
  const autresRevenusLocatifsMeubles = hasOtherBICReels ? 'Oui' : 'Non'; // Display value
  const revenusSCI = data.realEstateData.revenusPartsSCI || 0;
  const recettesExceptionnelles =
    data.realEstateData.recettesExceptionnelles || 0;
  const recettesEmplacementsPublicitaires =
    data.realEstateData.recettesEmplacementsPublicitaires || 0;

  // Check if other rental income exists at the real regime
  const autresRevenusLocatifsReels = hasOtherBICReels ? 'Oui' : 'Non'; // Display value

  // Check if Micro-BIC is possible (boolean for evaluation)
  const regimeMicroBICPossible = !hasOtherBICReels;

  // Other rental income at Micro-BIC regime
  const autresRevenusLocatifsMicroBIC = microBIC > 0 ? loyersAnnuels : 0;

  // Check if Micro-BIC can be maintained (limit of 77,700€) (boolean for evaluation)
  const possibiliteMaintienRegimeMicroBIC =
    loyersAnnuels + autresRevenusLocatifsMicroBIC <= 77700;

  // Calculate total revenues
  const totalRevenus =
    loyersAnnuels +
    revenusSCI +
    recettesExceptionnelles +
    recettesEmplacementsPublicitaires +
    autresRevenusLocatifsMicroBIC;

  // Boolean evaluation for regime applicability
  const regimeMicroBICApplicable =
    locationsMeublees === 'Oui' &&
    regimeMicroBICPossible &&
    possibiliteMaintienRegimeMicroBIC;

  // For display purposes, create a string that shows "Oui" or "Non"
  const concatener = regimeMicroBICApplicable ? 'Oui' : 'Non';

  // Application of index for projections
  const applicationIndice = data.realEstateData.applicationIndice || 'Oui';
  const indiceMoyen = applicationIndice === 'Oui' ? 0.02 : 0; // 2% if yes, 0% if no

  // Calculate tax values
  const abattementForfaitaire = totalRevenus * 0.5; // 50% flat-rate deduction for Micro-BIC
  const revenuNetImposable = totalRevenus - abattementForfaitaire;
  const montantImpot = revenuNetImposable * tauxMarginalImposition;
  const prelevementsSociaux = revenuNetImposable * 0.172;
  const csgDeductible = revenuNetImposable * 0.068; // 6.8% of taxable income is deductible
  const irpp = montantImpot + prelevementsSociaux - csgDeductible;
  const totalPrelevements = irpp;

  // Calculate cash flow
  const revenuNetApresImpot = totalRevenus - totalPrelevements;
  const tresorerieDegageeHorsEmprunts = totalRevenus - irpp;
  const tresorerieTotaleDegagee =
    tresorerieDegageeHorsEmprunts - remboursementEmprunt;
  const tresorerieCumulee = tresorerieTotaleDegagee; // First year only

  // Generate 30-year projections
  const projectionData: ProjectionDataItem[] = [];
  let cumulativeTreasury = 0;
  let limitExceededYear = 0;

  // Get loan duration from the data
  const loanDuration = data?.investementCalculatorData?.dureeDetention || 20;

  // Get the start year from the acquisition date
  const startYear = new Date(
    data?.investementCalculatorData?.dateAcquisition || '2020-12-31'
  ).getFullYear();

  for (let i = 0; i < 30; i++) {
    const year = startYear + i;

    // Calculate annual rent with index increase if applicable
    const produits = loyersAnnuels * Math.pow(1 + indiceMoyen, i);

    // Check if produits exceed 77,700€ limit
    if (produits > 77700 && limitExceededYear === 0) {
      limitExceededYear = year;

      // Still calculate all values for the limit year
      // 50% flat-rate deduction for Micro-BIC regime
      const abattement = Math.round(produits * 0.5);

      // Taxable income after deduction
      const resultatImposable = produits - abattement;

      // Income tax based on marginal tax rate
      const impot = Math.round(resultatImposable * tauxMarginalImposition);

      // Social charges (17.2%)
      const prelevements = Math.round(resultatImposable * 0.172);

      // Deductible CSG (6.8%)
      const csgDed = Math.round(resultatImposable * 0.068);

      // Total tax (income tax + social charges - deductible CSG)
      const irpp = impot + prelevements - csgDed;

      // Cash flow before loan payments
      const tresorerieHorsEmprunts = produits - irpp;

      // Loan payment (only for the duration of the loan)
      const remboursementAnnuel = i < loanDuration ? remboursementEmprunt : 0;

      // Total cash flow
      const tresorerieTotale = tresorerieHorsEmprunts - remboursementAnnuel;

      // Cumulative treasury
      cumulativeTreasury += tresorerieTotale;

      // Add the year when the limit is exceeded with a flag
      projectionData.push({
        year,
        produits,
        abattement,
        resultatImposable,
        impot,
        prelevements,
        csgDed,
        irpp,
        tresorerieHorsEmprunts,
        remboursementEmprunt: remboursementAnnuel,
        tresorerieTotale,
        tresorerieCumulee: cumulativeTreasury,
        limitExceeded: true,
        limitExceededYear: year
      });

      // Stop the loop after adding the year when the limit is exceeded
      break;
    }

    // 50% flat-rate deduction for Micro-BIC regime
    const abattement = Math.round(produits * 0.5);

    // Taxable income after deduction
    const resultatImposable = produits - abattement;

    // Income tax based on marginal tax rate
    const impot = Math.round(resultatImposable * tauxMarginalImposition);

    // Social charges (17.2%)
    const prelevements = Math.round(resultatImposable * 0.172);

    // Deductible CSG (6.8%)
    const csgDed = Math.round(resultatImposable * 0.068);

    // Total tax (income tax + social charges - deductible CSG)
    const irpp = impot + prelevements - csgDed;

    // Cash flow before loan payments
    const tresorerieHorsEmprunts = produits - irpp;

    // Loan payment (only for the duration of the loan)
    const remboursementAnnuel = i < loanDuration ? remboursementEmprunt : 0;

    // Total cash flow
    const tresorerieTotale = tresorerieHorsEmprunts - remboursementAnnuel;

    // Cumulative treasury
    cumulativeTreasury += tresorerieTotale;

    projectionData.push({
      year,
      produits,
      abattement,
      resultatImposable,
      impot,
      prelevements,
      csgDed,
      irpp,
      tresorerieHorsEmprunts,
      remboursementEmprunt: remboursementAnnuel,
      tresorerieTotale,
      tresorerieCumulee: cumulativeTreasury,
      limitExceeded: false
    });
  }

  // Generate plus-value projections
  const plusValueData: PlusValueDataItem[] = [];

  // Get property value
  const prixAcquisitionInitial = montantInvestissement;

  // Annual property value increase (3%)
  const annualIncrease = 0.03;

  // Calculate the maximum number of years to process
  // If limitExceededYear is provided, we'll stop at that year
  const maxYears = limitExceededYear
    ? Math.min(30, limitExceededYear - startYear + 1)
    : 30;

  for (let i = 0; i < maxYears; i++) {
    const year = startYear + i;

    // Calculate property value with 3% annual increase
    const prixVente = prixAcquisitionInitial * Math.pow(1 + annualIncrease, i);

    // Additional increases from the form
    const augmentationPrixVente = 0; // This would come from the form

    // Original acquisition price
    const prixAcquisition = prixAcquisitionInitial;

    // Additional acquisition costs
    const augmentationPrixAcquisition = 0; // This would come from the form

    // Calculate plus-value
    const plusValue =
      prixVente +
      augmentationPrixVente -
      prixAcquisition -
      augmentationPrixAcquisition;

    // Calculate plus-value per part (only if positive)
    const plusValueParPart = plusValue > 0 ? plusValue / nombreParts : 0;

    // Income tax abatement calculations
    // 0% for first 5 years, then 6% per year, capped at 100% after 21 years
    let tauxAbattementImpotRevenu = 0;
    if (i < 5) {
      tauxAbattementImpotRevenu = 0;
    } else if (i === 5) {
      tauxAbattementImpotRevenu = 0.06; // 6% at year 6
    } else if (i < 21) {
      // Add 6% each year
      tauxAbattementImpotRevenu = 0.06 * (i - 4); // i-4 because we start at 6% for year 6
    } else {
      tauxAbattementImpotRevenu = 1; // 100% after 21 years
    }

    // Calculate abattement
    const abattementImpotRevenu = plusValue * tauxAbattementImpotRevenu;

    // Calculate base taxable
    const baseTaxableImpotRevenu =
      plusValue > 0 ? plusValue - abattementImpotRevenu : 0;

    // Calculate base taxable per part
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;

    // Calculate income tax (19%)
    const impotRevenu = baseTaxableImpotRevenu * 0.19;

    // Social charges abatement calculations
    // More complex formula for social charges abatement
    let tauxAbattementPrelevementsSociaux = 0;
    if (i < 5) {
      tauxAbattementPrelevementsSociaux = 0;
    } else if (i === 5) {
      tauxAbattementPrelevementsSociaux = 0.0165; // 1.65% at year 6
    } else if (i < 21) {
      // Add 1.65% each year
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 4);
    } else if (i === 21) {
      tauxAbattementPrelevementsSociaux = 0.28; // 28% at year 22
    } else if (i === 22) {
      tauxAbattementPrelevementsSociaux = 0.37; // 37% at year 23
    } else if (i === 23) {
      tauxAbattementPrelevementsSociaux = 0.46; // 46% at year 24
    } else if (i === 24) {
      tauxAbattementPrelevementsSociaux = 0.55; // 55% at year 25
    } else if (i === 25) {
      tauxAbattementPrelevementsSociaux = 0.64; // 64% at year 26
    } else if (i === 26) {
      tauxAbattementPrelevementsSociaux = 0.73; // 73% at year 27
    } else if (i === 27) {
      tauxAbattementPrelevementsSociaux = 0.82; // 82% at year 28
    } else if (i === 28) {
      tauxAbattementPrelevementsSociaux = 0.91; // 91% at year 29
    } else {
      tauxAbattementPrelevementsSociaux = 1; // 100% at year 30
    }

    // Calculate social charges abatement
    const abattementPrelevementsSociaux =
      plusValue * tauxAbattementPrelevementsSociaux;

    // Calculate base taxable for social charges
    const baseTaxablePrelevementsSociaux =
      plusValue - abattementPrelevementsSociaux;

    // Calculate social charges (17.2%)
    const prelevementsSociaux =
      baseTaxablePrelevementsSociaux > 0
        ? baseTaxablePrelevementsSociaux * 0.172
        : 0;

    // Additional taxation based on the amount of plus-value per part
    // This is a simplified version - in reality, it would use a lookup table
    let taxationSupplementaire = 0;

    // Calculate total tax on plus-value
    const totalImposition =
      impotRevenu + prelevementsSociaux + taxationSupplementaire;

    // Calculate net treasury generated from sale
    const tresorerieGeneree =
      prixVente - (i < loanDuration ? prixAcquisition : 0) - totalImposition;

    plusValueData.push({
      year,
      prixVente,
      augmentationPrixVente,
      prixAcquisition,
      augmentationPrixAcquisition,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociaux,
      taxationSupplementaire,
      totalImposition,
      tresorerieGeneree,
    });
  }

  return {
    // Conditions
    locationsMeublees,
    loyersAnnuels,
    autresRevenusLocatifsMeubles,
    revenusSCI,
    recettesExceptionnelles,
    recettesEmplacementsPublicitaires,
    autresRevenusLocatifsReels,
    regimeMicroBICPossible,
    autresRevenusLocatifsMicroBIC,
    possibiliteMaintienRegimeMicroBIC,
    totalRevenus,
    concatener,
    regimeMicroBICApplicable,
    applicationIndice,

    // Tax calculations
    abattementForfaitaire,
    revenuNetImposable,
    tauxMarginalImposition,
    montantImpot,
    prelevementsSociaux,
    csgDeductible,
    irpp,
    totalPrelevements,

    // Cash flow
    revenuNetApresImpot,
    tresorerieDegageeHorsEmprunts,
    remboursementEmprunt,
    tresorerieTotaleDegagee,
    tresorerieCumulee,

    // Projections
    indiceMoyen,
    projectionData,
    plusValueData,
    limitExceededYear, // Add the year when the limit will be exceeded

    // Message for alerts
    message: regimeMicroBICApplicable
      ? (limitExceededYear ? `Le régime Micro-BIC sera applicable jusqu'en ${limitExceededYear}, année où les revenus locatifs dépasseront la limite de 77 700€.` : undefined)
      : "Le régime Micro-BIC n'est pas applicable au cas étudié. Veuillez vérifier les conditions d'éligibilité.",
  };
}
