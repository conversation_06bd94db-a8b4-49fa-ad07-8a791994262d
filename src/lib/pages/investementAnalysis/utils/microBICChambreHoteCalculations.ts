import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import type {
  MicroBICChambreHoteFormData,
  ProjectionDataItem,
  PlusValueDataItem,
} from '~/lib/types/micro-bic-chambre-hote';

/**
 * Calculates all the data for the Micro-BIC Chambres d'Hotes et Tourisme Classe tax regime
 * @param data Input data from the parent component
 * @param formatCurrency Function to format currency values
 * @returns Calculated data for the Micro-BIC Chambres d'Hotes form
 */
export function calculateMicroBICChambreHoteData(
  data: FormDataInvestementAnalysistSteps,
  formatCurrency: (value: number) => string
): MicroBICChambreHoteFormData {
  // Get values from previous steps - following Excel formula: =+'Profil Investisseur'!I31+'Profil Investisseur'!J31+'Cas d''étude - 1ère année'!B13
  // In our context, we need to sum the relevant rental income values from the profile investor and case study data
  const profileInvestorRentalIncome = (
    data?.profileInvestorData?.assetsInfoTable || []
  ).reduce((sum, asset) => {
    return sum + (parseFloat(asset.annualRent) || 0);
  }, 0);

  const caseStudyRentalIncome =
    data?.investementCalculatorData?.loyersAnnuelHorsCharges || 0;

  // Calculate total rental income
  const loyersAnnuels = profileInvestorRentalIncome + caseStudyRentalIncome;

  // Check if there are other rental incomes in the real regime
  const autresRevenusLocatifsReels =
    profileInvestorRentalIncome > 0 ? 'OUI' : 'NON';

  // Determine if Micro-BIC regime is possible
  const regimeMicroBICPossible = autresRevenusLocatifsReels === 'NON';

  // Get other rental incomes in the Micro-BIC regime
  const autresRevenusLocatifsMicroBIC = profileInvestorRentalIncome;

  // Check if the Micro-BIC regime can be maintained (total income <= 77,700€)
  const totalRevenus = loyersAnnuels + autresRevenusLocatifsMicroBIC;
  const possibiliteMaintienRegimeMicroBIC = totalRevenus <= 77700;

  // Concatenate conditions to determine if the Micro-BIC regime is applicable
  const locationsSaisonnieres = data?.realEstateData?.locationsSaisonnieres || 'OUI';

  // Check if all conditions are met
  const allConditionsMet = locationsSaisonnieres === 'OUI' &&
                          regimeMicroBICPossible &&
                          possibiliteMaintienRegimeMicroBIC;

  // Set concatener to "Oui" or "Non" based on all conditions
  const concatener = allConditionsMet ? 'Oui' : 'Non';

  const regimeMicroBICApplicable = allConditionsMet;

  // Calculate tax values
  const abattementForfaitaire = totalRevenus * 0.5; // 50% abattement for Micro-BIC Chambres d'Hotes
  const revenuNetImposable = totalRevenus - abattementForfaitaire;
  const tauxMarginalImposition = data?.investementCalculatorData?.tauxMarginalImposition || 0.3;
  const montantImpot = revenuNetImposable * tauxMarginalImposition;
  const prelevementsSociaux = revenuNetImposable * 0.172; // 17.2% social charges
  const csgDeductible = -revenuNetImposable * 0.07; // 7% deductible CSG
  const irpp = montantImpot + prelevementsSociaux + csgDeductible;
  const totalPrelevements = montantImpot + prelevementsSociaux;

  // Calculate cash flow
  const revenuNetApresImpot = totalRevenus - totalPrelevements;
  const tresorerieDegageeHorsEmprunts = revenuNetApresImpot;
  const remboursementEmprunt = data?.investementCalculatorData.monthlyPayment * 12 || 0;
  const tresorerieTotaleDegagee = tresorerieDegageeHorsEmprunts - remboursementEmprunt;
  const tresorerieCumulee = tresorerieTotaleDegagee;

  // Generate 30-year projection data
  const applicationIndice = data?.realEstateData?.applicationIndice || 'OUI';
  const indiceMoyen = applicationIndice === 'OUI' ? 0.02 : 0; // 2% if index is applied, 0% otherwise

  const projectionData: ProjectionDataItem[] = [];
  const plusValueData: PlusValueDataItem[] = [];

  // Get the current year
  const currentYear = new Date().getFullYear();

  // Calculate projection data for 30 years
  let cumulTresorerie = 0;
  for (let i = 0; i < 30; i++) {
    const year = currentYear + i;
    const produits = i === 0
      ? totalRevenus
      : Math.min(projectionData[i-1].produits * (1 + indiceMoyen), 188700);

    const abattement = produits * 0.5;
    const resultatImposable = produits - abattement;
    const impot = resultatImposable * tauxMarginalImposition;
    const prelevements = resultatImposable * 0.172;
    const csgDed = -resultatImposable * 0.07;
    const irpp = impot + prelevements + csgDed;
    const tresorerieHorsEmprunts = produits - irpp;

    // Calculate loan repayment based on loan duration
    const loanDuration = data?.investementCalculatorData?.dureeDetention || 15;
    const remboursementEmprunt = i < loanDuration ? (data?.investementCalculatorData.monthlyPayment * 12 || 0) : 0;

    const tresorerieTotale = tresorerieHorsEmprunts - remboursementEmprunt;
    cumulTresorerie += tresorerieTotale;

    projectionData.push({
      year,
      produits,
      abattement,
      resultatImposable,
      impot,
      prelevements,
      csgDed,
      irpp,
      tresorerieHorsEmprunts,
      remboursementEmprunt,
      tresorerieTotale,
      tresorerieCumulee: cumulTresorerie
    });
  }

  // Calculate plus-value data for 30 years
  const prixAcquisition = data?.realEstateData?.prixAcquisitionMontant || 0;
  let prixVente = prixAcquisition * 1.03; // Initial price with 3% increase

  for (let i = 0; i < 30; i++) {
    const year = currentYear + i;

    // Calculate price increase each year (3%)
    if (i > 0) {
      prixVente = plusValueData[i-1].prixVente * 1.03;
    }

    const augmentationPrixVente = 0; // Additional price increase
    const augmentationPrixAcquisition = 0; // Additional acquisition price increase
    const plusValue = prixVente + augmentationPrixVente - prixAcquisition - augmentationPrixAcquisition;
    const nombreParts = data?.investementCalculatorData?.nombreParts || 1;
    const plusValueParPart = plusValue / nombreParts;

    // Calculate tax abatements based on holding period
    let tauxAbattementImpotRevenu = 0;
    if (i >= 22) {
      tauxAbattementImpotRevenu = 1; // 100% after 22 years
    } else if (i >= 6) {
      tauxAbattementImpotRevenu = 0.06 * (i - 5); // 6% per year starting from year 6
    }

    const abattementImpotRevenu = plusValue * tauxAbattementImpotRevenu;
    const baseTaxableImpotRevenu = Math.max(0, plusValue - abattementImpotRevenu);
    const baseTaxableImpotRevenuParPart = baseTaxableImpotRevenu / nombreParts;
    const impotRevenu = baseTaxableImpotRevenu * 0.19; // 19% tax rate

    // Calculate social charges abatements
    let tauxAbattementPrelevementsSociaux = 0;
    if (i >= 30) {
      tauxAbattementPrelevementsSociaux = 1; // 100% after 30 years
    } else if (i >= 22) {
      tauxAbattementPrelevementsSociaux = 0.09 * (i - 21); // 9% per year starting from year 22
    } else if (i >= 6) {
      tauxAbattementPrelevementsSociaux = 0.0165 * (i - 5); // 1.65% per year starting from year 6
    }

    const abattementPrelevementsSociaux = plusValue * tauxAbattementPrelevementsSociaux;
    const baseTaxablePrelevementsSociaux = plusValue - abattementPrelevementsSociaux;
    const prelevementsSociaux = Math.max(0, baseTaxablePrelevementsSociaux * 0.172);

    // Calculate additional taxation based on income level
    let taxationSupplementaire = 0;
    // This is a simplified calculation - in real implementation, you would use a lookup table
    if (plusValueParPart > 50000) {
      taxationSupplementaire = baseTaxableImpotRevenu * 0.02; // Example rate
    }

    const totalImposition = impotRevenu + prelevementsSociaux + taxationSupplementaire;
    const tresorerieGeneree = prixVente - totalImposition;

    plusValueData.push({
      year,
      prixVente,
      augmentationPrixVente,
      prixAcquisition,
      augmentationPrixAcquisition,
      plusValue,
      nombreParts,
      plusValueParPart,
      tauxAbattementImpotRevenu,
      abattementImpotRevenu,
      baseTaxableImpotRevenu,
      baseTaxableImpotRevenuParPart,
      impotRevenu,
      tauxAbattementPrelevementsSociaux,
      abattementPrelevementsSociaux,
      baseTaxablePrelevementsSociaux,
      prelevementsSociaux,
      taxationSupplementaire,
      totalImposition,
      tresorerieGeneree
    });
  }

  return {
    locationsSaisonnieres,
    loyersAnnuels,
    autresRevenusLocatifsReels,
    regimeMicroBICPossible,
    autresRevenusLocatifsMicroBIC,
    possibiliteMaintienRegimeMicroBIC,
    totalRevenus,
    concatener,
    regimeMicroBICApplicable,
    applicationIndice,
    abattementForfaitaire,
    revenuNetImposable,
    tauxMarginalImposition,
    montantImpot,
    prelevementsSociaux,
    csgDeductible,
    irpp,
    totalPrelevements,
    revenuNetApresImpot,
    tresorerieDegageeHorsEmprunts,
    remboursementEmprunt,
    tresorerieTotaleDegagee,
    tresorerieCumulee,
    indiceMoyen,
    projectionData,
    plusValueData,
    message: regimeMicroBICApplicable
      ? "Le régime Micro-BIC Chambres d'Hôtes et Tourisme Classé est applicable à votre situation."
      : "Le régime Micro-BIC Chambres d'Hôtes et Tourisme Classé n'est pas applicable à votre situation."
  };
}
