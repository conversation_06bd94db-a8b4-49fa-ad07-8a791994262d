export interface ProjectionDataItem {
  year: number;
  produits: number;
  abattement: number;
  resultatImposable: number;
  impot: number;
  prelevements: number;
  csgDed: number;
  irpp: number;
  tresorerieHorsEmprunts: number;
  remboursementEmprunt: number;
  tresorerieTotale: number;
  tresorerieCumulee: number;
  limitExceeded?: boolean;
  limitExceededYear?: number;
}

export interface PlusValueDataItem {
  year: number;
  prixVente: number;
  augmentationPrixVente: number;
  prixAcquisition: number;
  augmentationPrixAcquisition: number;
  plusValue: number;
  nombreParts: number;
  plusValueParPart: number;
  tauxAbattementImpotRevenu: number;
  abattementImpotRevenu: number;
  baseTaxableImpotRevenu: number;
  baseTaxableImpotRevenuParPart: number;
  impotRevenu: number;
  tauxAbattementPrelevementsSociaux: number;
  abattementPrelevementsSociaux: number;
  baseTaxablePrelevementsSociaux: number;
  prelevementsSociaux: number;
  taxationSupplementaire: number;
  totalImposition: number;
  tresorerieGeneree: number;
  limitExceeded?: boolean;
}

export interface MicroBICFormData {
  // Conditions
  locationsMeublees: string;
  loyersAnnuels: number;
  autresRevenusLocatifsMeubles: string;
  revenusSCI: number;
  recettesExceptionnelles: number;
  recettesEmplacementsPublicitaires: number;
  autresRevenusLocatifsReels: string;
  regimeMicroBICPossible: boolean;
  autresRevenusLocatifsMicroBIC: number;
  possibiliteMaintienRegimeMicroBIC: boolean;
  totalRevenus: number;
  concatener: string;
  regimeMicroBICApplicable: boolean;
  applicationIndice: string;

  // Tax calculations
  abattementForfaitaire: number;
  revenuNetImposable: number;
  tauxMarginalImposition: number;
  montantImpot: number;
  prelevementsSociaux: number;
  csgDeductible: number;
  irpp: number;
  totalPrelevements: number;

  // Cash flow
  revenuNetApresImpot: number;
  tresorerieDegageeHorsEmprunts: number;
  remboursementEmprunt: number;
  tresorerieTotaleDegagee: number;
  tresorerieCumulee: number;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];
  plusValueData: PlusValueDataItem[];
  limitExceededYear?: number;

  // Message for alerts
  message?: string;
}

export interface MicroBICFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
