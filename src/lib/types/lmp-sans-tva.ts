export interface ProjectionDataItem {
  year: number;
  rentalIncome: number;
  maintenanceCharges: number;
  rentalCharges: number;
  nonOccupantInsurance: number;
  loanInsurance: number;
  loanFees: number;
  propertyTaxes: number;
  cfe: number;
  loanInterest: number;
  bankFees: number;
  accountingFees: number;
  legalFees: number;
  domiciliationFees: number;
  amortization: number;
  accountingResult: number;
  fiscalResult: number;
  isDeficit: string;
  globalRevenuesExcludingLMP: number;
  isDeficitGreaterThanGlobalRevenues: string;
  concatenatedStatus: string;
  deficitImputableToGlobalRevenues: number;
  reportableFiscalDeficit: number;
  revenuesForSSICalculation: number;
  ssiContributions: number;
  irTaxableBase: number;
  incomeTax: number;
  totalTaxes: number;
  cashFlowExcludingLoans: number;
  loanRepayment15Years: number;
  loanRepayment20Years: number;
  loanRepayment25Years: number;
  totalCashFlow: number;
  cumulativeCashFlow: number;
  limitExceeded?: boolean;
  limitExceededYear?: number;
}

export interface PlusValueDataItem {
  year: number;
  salePrice: number;
  salePriceIncrease: number;
  acquisitionPrice: number;
  acquisitionPriceIncrease: number;
  plusValue: number;
  netBookValue: number;
  deductedAmortization: number;
  fiscalPlusValue: number;
  fiscalResultExcludingPlusValue: number;
  fiscalResultWithPlusValue: number;
  shortTermPlusValue: number;
  marginalTaxRate: number;
  shortTermIncomeTax: number;
  ssiContributions: number;
  totalShortTermTaxation: number;
  longTermPlusValue: number;
  incomeTax12_8Percent: number;
  socialContributions17_2Percent: number;
  totalLongTermTaxation: number;
  remainingLoan: number;
  treasuryAfterLoanRepayment: number;
  limitExceeded?: boolean;
}

export interface LMPSansTVAFormData {
  // Conditions
  locationsMeublees: string;
  locationsSaisonnieresMeublees: string;
  concatener: boolean;
  revenusLocatifsMeublesChargesComprises: number;
  nombrePartsFoyerFiscal: number;
  limiteRevenusLocatifsMeuble: number;
  limiteAtteinte: string;
  revenusLocatifsAutres: string;
  revenusLocatifsMeublesMicroBIC: number;
  possibiliteMaintenirRegimeReel: string;
  conclusion: string;
  regimeLMPApplicable: string;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];
  limitExceededYear?: number;

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // Message for alerts
  message?: string;
}

export interface LMPSansTVAFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
