export interface ProjectionDataItem {
  year: number;
  rentalIncome: number;
  managementCharges: number;
  otherCharges: number;
  amortization: number;
  cumulativeAmortization: number;
  accountingResult: number;
  amortizationGreaterThanResult: string;
  accountingResultBIC: number;
  reportedAmortization: number;
  cumulativeReportedAmortization: number;
  cumulativeReportedAmortization2: number;
  taxableResult: number;
  incomeTax: number;
  socialCharges: number;
  deductibleCSG: number;
  totalTax: number;
  cashFlowExcludingLoans: number;
  loanRepayments: number;
  totalCashFlow: number;
  cumulativeCashFlow: number;
  limitExceeded?: boolean;
  limitExceededYear?: number;
}

export interface PlusValueDataItem {
  year: number;
  salePrice: number;
  salePriceIncrease: number;
  acquisitionPrice: number;
  amortizationPracticed: number;
  netAcquisitionPrice: number;
  acquisitionPriceIncrease: number;
  plusValue: number;
  numberOfParts: number;
  plusValuePerPart: number;
  incomeTaxAbatementRate: number;
  incomeTaxAbatement: number;
  incomeTaxBase: number;
  incomeTaxBasePerPart: number;
  incomeTax19Percent: number;
  socialChargesAbatementRate: number;
  socialChargesAbatement: number;
  socialChargesBase: number;
  socialCharges17_2Percent: number;
  additionalTaxation: number;
  taxationAbatementField: string;
  taxationAbatementValue: number;
  additionalTaxationAmount: number;
  totalPlusValueTax: number;
  totalTreasuryGenerated: number;
  limitExceeded?: boolean;
}

export interface BICReelLMNPFormData {
  // Conditions
  locationsMeublees: string;
  locationsSaisonnieresMeublees: string;
  concatener: boolean;
  revenusLocatifsMeublesChargesComprises: number;
  nombrePartsFoyerFiscal: number;
  limiteRevenusLocatifsMeuble: number;
  limiteAtteinte: string;
  revenusLocatifsMeublesSuperieurMoitieRevenus: string;
  revenusLocatifsMeublesMicroBIC: number;
  possibiliteMaintenirRegimeReel: string;
  regimeBICReelApplicable: string;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];
  limitExceededYear?: number;

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // Message for alerts
  message?: string;
}

export interface BICReelLMNPFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
