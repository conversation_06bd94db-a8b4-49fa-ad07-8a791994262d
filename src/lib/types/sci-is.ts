export interface ProjectionDataItem {
  year: number;
  rentalIncome: number;
  maintenanceCharges: number;
  rentalCharges: number;
  nonOccupantInsurance: number;
  loanInsurance: number;
  loanFees: number;
  propertyTaxes: number;
  cfe: number;
  loanInterest: number;
  bankFees: number;
  accountingFees: number;
  legalFees: number;
  domiciliationFees: number;
  amortization: number;
  accountingResult: number;
  fiscalResult: number;
  isDeficit: boolean;
  cumulativeFiscalResult: number;
  reducedRateIS: number;
  normalRateIS: number;
  totalIS: number;
  canDistributeDividends: boolean;
  distributableDividends: number;
  cumulativeDividends: number;
  remainingDividends: number;
  flatTax: number;
  netDividends: number;
  cashFlowBeforeLoans: number;
  loanRepayment15Years: number;
  loanRepayment20Years: number;
  loanRepayment25Years: number;
  totalCashFlow: number;
  cumulativeCashFlow: number;
}

export interface PlusValueDataItem {
  year: number;
  salePrice: number;
  acquisitionPrice: number;
  deductedAmortization: number;
  netBookValue: number;
  fiscalPlusValue: number;
  cumulativeFiscalResult: number;
  fiscalResultBeforeTax: number;
  isDeficit: boolean;
  reducedRateIS: number;
  normalRateIS: number;
  totalIS: number;
  canDistributeDividends: boolean;
  distributableDividends: number;
  cumulativeDividends: number;
  totalDistributableDividends: number;
  availableCashBeforeDividends: number;
  distributableDividendsForYear: number;
  flatTax: number;
  netDividendsFlatTax: number;
  cashAfterDividendDistribution: number;
  remainingLoan: number;
  treasuryAfterLoanRepayment: number;
}

export interface SCIPartsDataItem {
  year: number;
  propertyValue: number;
  treasuryWithoutDividends: number;
  otherAssets: number;
  associateAccounts: number;
  loans: number;
  theoreticalSalePrice: number;
  acquisitionValue: number;
  plusValueOnParts: number;
  flatTax: number;
  netDividendsFlatTax: number;
}

export interface SCIISFormData {
  // Conditions
  locationsMeublees: string;
  locationsSaisonnieresMeublees: string;
  nombreAssociesRequis: string;
  concatener: boolean;
  regimeApplicable: string;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // SCI Parts
  sciPartsData: SCIPartsDataItem[];

  // Message for alerts
  message?: string;
}

export interface SCIISFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
