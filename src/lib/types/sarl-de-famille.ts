export interface ProjectionDataItem {
  year: number;
  rentalIncome: number;
  maintenanceCharges: number;
  rentalCharges: number;
  nonOccupantInsurance: number;
  loanInsurance: number;
  loanFees: number;
  propertyTaxes: number;
  cfe: number;
  loanInterest: number;
  bankFees: number;
  totalManagementCharges: number;
  accountingFees: number;
  legalFees: number;
  domiciliationFees: number;
  managerRemuneration: number;
  totalOtherCharges: number;
  amortization: number;
  cumulativeAmortization: number;
  accountingResult: number;
  isAmortizationGreaterThanResult: string;
  accountingResultWithAmortization: number;
  reportedAmortization: number;
  cumulativeReportedAmortization: number;
  fiscalResult: number;
  incomeTax: number;
  socialContributions: number;
  csgDeduction: number;
  irpp: number;
  minimumSocialContributions: number;
  treasuryWithoutLoans: number;
  loanRepayment15Years: number;
  loanRepayment20Years: number;
  loanRepayment25Years: number;
  totalTreasury: number;
  cumulativeTreasury: number;
}

export interface PlusValueDataItem {
  year: number;
  salePrice: number;
  salePriceIncrease: number;
  acquisitionPrice: number;
  amortizationPracticed: number;
  netAcquisitionPrice: number;
  acquisitionPriceIncrease: number;
  plusValue: number;
  numberOfParts: number;
  plusValuePerPart: number;
  incomeTaxAbatementRate: number;
  incomeTaxAbatement: number;
  incomeTaxBase: number;
  incomeTaxBasePerPart: number;
  incomeTax19Percent: number;
  socialContributionsAbatementRate: number;
  socialContributionsAbatement: number;
  socialContributionsBase: number;
  socialContributions17_2Percent: number;
  additionalTaxation: number;
  totalTaxation: number;
  remainingLoan: number;
  treasuryAfterLoanRepayment: number;
}

export interface SARLDeFamilleFormData {
  // Conditions
  locationsMeublees: string;
  nombreAssociesRequis: string;
  associesLigneDirecte: string;
  associesFreresSoeurs: string;
  associesConjointsPACS: string;
  eligibiliteSARLDeFamille: string;
  revenusLocatifsMeublesChargesComprises: number;
  nombrePartsFoyerFiscal: number;
  limiteRevenusLocatifsMeuble: number;
  limiteAtteinte: string;
  revenusLocatifsAutres: string;
  concatener: string;
  regimeBICReelApplicable: string;
  regimeSARLDeFamilleApplicable: string;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // Message for alerts
  message?: string;
}

export interface SARLDeFamilleFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
