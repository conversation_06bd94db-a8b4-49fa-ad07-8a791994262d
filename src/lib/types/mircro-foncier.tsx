export interface ProjectionDataItem {
  annee: number;
  produits: number;
  abattement: number;
  resultatImposable: number;
  impot: number;
  prelevements: number;
  csgDed: number;
  irpp: number;
  tresorerieHorsEmprunts: number;
  tresorerieTotale: number;
  cumulTresorerie: number;
}

export interface PlusValueDataItem {
  annee: number;
  prixVente: number;
  augmentationPrixVente: number;
  prixAchat: number;
  augmentationPrixAcquisition: number;
  plusValue: number;
  nombreParts: number;
  plusValueParPart: number;
  tauxAbattementImpotRevenu: number;
  abattementImpotRevenu: number;
  baseTaxableImpotRevenu: number;
  baseTaxableImpotRevenuParPart: number;
  impotRevenu: number;
  tauxAbattementPrelevementsSociaux: number;
  abattementPrelevementsSociaux: number;
  baseTaxablePrelevementsSociaux: number;
  prelevementsSociauxPlusValue: number;
  taxationSupplementaire: number;
  totalImpositionPlusValue: number;
  totalTresorerieGeneree: number;
}

// Asset info type for the profile investor data
export interface AssetInfo {
  applicableTaxRegime: string;
  [key: string]: any;
}

export interface MicroFonciersFormData {
  // Conditions
  locationsNues: string;
  loyersAnnuels: number;
  autresRevenusLocatifsNue: string;
  microFonciers: number;
  foncierReels: number;
  regimeMicroFoncierPossible: boolean;
  revenusSCI: number;
  recettesExceptionnelles: number;
  recettesEmplacementsPublicitaires: number;
  totalRevenus: number;
  limiteNonAtteinte: boolean;
  concatener: string;
  regimeMicroFoncierApplicable: boolean;
  applicationIndice: string;

  // Tax calculations
  abattementForfaitaire: number;
  revenuNetImposable: number;
  tauxMarginalImposition: number;
  montantImpot: number;
  prelevementsSociaux: number;
  csgDeductible: number;
  irpp: number;
  totalPrelevements: number;

  // Cash flow
  revenuNetApresImpot: number;
  tresorerieDegageeHorsEmprunts: number;
  remboursementEmprunt: number;
  tresorerieTotaleDegagee: number;

  // Profitability
  rentabiliteNetteImpot: number;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // Message
  message: string;
}

export interface MicroFonciersFormProps {
  data: any;
  setParentFormData: (data: any) => void;
}
