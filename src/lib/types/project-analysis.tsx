export interface FinancialDataItem {
  label: string;
  value: number | string;
  isEditable?: boolean;
  onChange?: (value: string) => void;
  comment?: string;
  colorScheme?: string;
}

export interface FinancialSection {
  title: string;
  data: FinancialDataItem[];
  showComments?: boolean;
}

// Props interfaces
export interface FinanceTableProps {
  title: string;
  data: FinancialDataItem[];
  showComments?: boolean;
}

export interface ProjectAnalysisProps {
  onInputChange: (event: { target: { name: string; value: number } }) => void;
  monthlyPayment: number;
}
