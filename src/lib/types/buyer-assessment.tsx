export interface ITableAssetsInfo {
  number: number;
  rentalMode: string;
  annualRent: string;
  legalStatus: string;
  specialTaxStatus: string;
  applicableTaxRegime: string;
}
export type SetParentFormDataType = (data: any) => void;

export interface FormDataBuyerAssessmentSteps {
  professionalFamilyData: IProfessionalFamilyData;
  economicData: IEconomicData;
  financeAndHeritageData: IFinanceAndHeritage;
  motivationsObjectivesData: IMotivationsObjectivesData;
  profileInvestorData: IProfileInvestor;
}
export interface IProfessionalFamilyData {
  lastName: string;
  firstName: string;
  profession: string;
  email: string;
  phone: string;
  gender: string;
  marriageStatus: string;
  celibataire: string;
  statutProfessionnel: string;
  typeContrat: string;
  statutProfessionnelConjoint: string;
  typeContratConjoint: string;
  marriageDate: string;
  regime: string;
  livingTogether: string;
  previousMarriage: string;
  pacsStatus: string;
  pacsDate: string;
  veufStatus: string;
  veufDate: string;
  concubinageStatus: string;
  concubinageAmount: string;
  alimonyStatus: string;
  alimonyAmount: string;
  numberOfChildren: string;
  fiscallyChargedChildren: string;
  successionsDetails: string;
}

export interface IEconomicData {
  status: string;
  homeownerStatus: string;
  rowDataTable: IEconomicRowDataTable[];
  financialDetails: IFinancialDetails;
}
export interface ICreditTable {
  id: number;
  type: string;
  startDate: string;
  amount: string;
  monthlyPayment: string;
  contractType: string;
}
export interface IPatrimoineTable {
  bien?: string;
  monsieur?: number;
  madame?: number;
  nature?: string;
  revenu?: number;
  total?: number;
  patrimoineImmobilier?: number;
}
export interface IPatrimoineForm {
  ownsPatrimoine: string;
  patrimoineTable: IPatrimoineTable[];
  totalRevenue: string;
}
export interface IFinanceForm {
  outstandingCredit: string;
  savings: string;
  savingsAmount: string;
  contribution: string;
  contributionAmount: string;
  monthlyPaymentsTotal: string;
  amountBorrowedTotal: string;
  credits: ICreditTable[];
}
export interface IFinanceAndHeritage {
  financeForm: IFinanceForm;
  patrimoineForm: IPatrimoineForm;
}

// Interface for storing totals by tax regime
interface TaxRegimeTotals {
  [key: string]: number;
}

export interface IProfileInvestor {
  buyingOption: string;
  isMarried: string;
  regimeMatrimonial: string;
  numberOfPerson: string;
  ownsRealEstate: string;
  numberOfRealEstate: string;
  assetsInfoTable: ITableAssetsInfo[];
  taxRegimeTotals?: TaxRegimeTotals;
}

type GetCreditByDebtRatioPerYearsType = (years: number) => number;
interface ITextColor {
  title: string;
  color: string;
}
export interface ICalculatedBudgetData {
  rowData: Array<{ category: string; value: string | number }>;
  creditRowData: Array<{ category: string; value: number }>;
  debtToIncomeRatio: number;
  creditStatus: ITextColor;
  maxAcquisitionValue: number;
  taxRatio: number;
  interestRate: number;
  annuityPayment: number;
  notaryFeeContributionStatus: ITextColor;
  livingAllowanceStatus: ITextColor;
  personalExpenseStatus: ITextColor;
  monthlyLoanRepayment: number;
  debtConclusion: ITextColor;
  depositConclusion: ITextColor;
  getCreditByDebtRatioPerYears: GetCreditByDebtRatioPerYearsType;
  livingAllowance: number;
  remainingLivingAllowanceAfterExpenses: number;
}

export interface IEconomicRowDataTable {
  id: string;
  category: string;
  monsieur: string;
  madame: string;
  total: number;
}

export interface IFinancialDetails {
  numOfParts: string;
  fiscalIncome: string;
  incomeTax: string;
  salaryOtherIncome: string;
  bicIncome: boolean;
  bncIncome: boolean;
  otherIncome: boolean;
  capitalIncome: boolean;
  propertyIncome: boolean;
  isfLiable: string;
  isfNet: string;
  futurePartsThreeYears: string;
  futurePartsFiveYears: string;
}

export interface IPrioritiesMotivations {
  patrimony: string;
  diversify: string;
  additionalIncome: string;
  debtRepayment: string;
  investmentCapacity: string;
  organizeTransmission: string;
  taxReduction: string;
  resaleValue: string;
}

export interface IObjectivesMotivations {
  transmit: string;
  resale: string;
  conserve: string;
  usagePersonal: string;
  researchZone: string;
}

export interface IPropertyDetailsMotivations {
  acquisitionDuration: string;
  buyWith: string;
  maritalStatus: string;
  numberOfPeople: string;
  propertyType: string;
  numberOfRooms: string;
  furnished: string;
  rentalType: string;
  rentalDuration: string;
  researchLocation: string;
  searchThrough: string;
  propertyManagement: string;
  additionalDescription: string;
}

export interface IMotivationsObjectivesData {
  priorities: IPrioritiesMotivations;
  objectives: IObjectivesMotivations;
  propertyDetails: IPropertyDetailsMotivations;
}
