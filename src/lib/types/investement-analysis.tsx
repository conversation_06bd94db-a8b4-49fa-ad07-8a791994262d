import type { IProfileInvestor } from './buyer-assessment';
import type { FonciersReelsFormData } from './fonciers-reels';
import type { MicroBICFormData } from './micro-bic';
import type { MicroBICChambreHoteFormData } from './micro-bic-chambre-hote';
import type { MicroBICTourismeNCFormData } from './micro-bic-tourisme-nc';
import type { BICReelLMNPFormData } from './bic-reel-lmnp';
import type { SCIISFormData } from './sci-is';
import type { LMPSansTVAFormData } from './lmp-sans-tva';
import type { SARLDeFamilleFormData } from './sarl-de-famille';
import type{ LoanScheduleEntry } from '../pages/investementAnalysis/components/CustomLoanSheet';

export interface ProjectionDataItem {
  year: number;
  produits?: number;
  abattement?: number;
  resultatImposable?: number;
  impot?: number;
  prelevements?: number;
  csgDed?: number;
  irpp?: number;
  tresorerieHorsEmprunts?: number;
  tresorerieTotale?: number;
  tresorerieCumulee?: number;
  cumulTresorerie?: number;
  tresorerieRevente?: number;
  totalCashFlow?: number;
  cumulativeCashFlow?: number;
  [key: string]: any; // Allow any other properties
}

export interface IMicroFonciersData {
  revenusBrutsFonciers: number;
  abattementForfaitaire: number;
  revenuNetImposable: number;
  tauxMarginalImposition: number;
  montantImpot: number;
  prelevementsSociaux: number;
  totalPrelevements: number;
  revenuNetApresImpot: number;
  rentabiliteNetteImpot: number;
  regimeMicroFoncierApplicable?: boolean;
  projectionData?: ProjectionDataItem[];
  limitExceededYear?: number;
}

export interface IInvestementCalculatorData {
  // Investissement
  montantInvestissement: number;
  dontTerrain: number;
  dontConstruction: number;
  coutAcquisition: number;
  travaux: number;

  monthlyPayment: number;
  loanAmount: number;
  loanCalculatedData : LoanScheduleEntry[];
  soldByYear: Record<string, number>;
  // Fiscalité
  revenuFiscalReference: number;
  tauxMarginalImposition: number;
  nombreParts: number;

  // Loyers
  loyersAnnuelHorsCharges: number;
  chargesComprises: number;

  // Charges fixes
  chargesLocatives: number;
  assurancesNonOccupant: number;
  assurancesEmprunteur: number;
  chargesEntretien: number;
  taxesFoncieres: number;
  fraisAgences: number;
  fraisEmprunts: number;
  CFE: number;
  fraisBancaires: number;
  interetsEmprunt: number;
  travauxDeductibles: number;

  // Amortissements
  quotePartTerrain: number;
  quotePartGrosOeuvre: number;
  quotePartFacade: number;
  quotePartIGT: number;
  quotePartAgencements: number;
  valeurMeubles: number;
  dureeGrosOeuvre: number;
  dureeFacade: number;
  dureeIGT: number;
  dureeAgencements: number;
  dureeMeubles: number;

  // Date
  dateAcquisition: string;
  dureeDetention: number;
  regime?: string;

  // Honoraires
  fraisComptables?: number;
  fraisJuridiques?: number;
  fraisGerant?: number;
}

// Add regimeApplicable property to all form data interfaces
export interface SyntheseFormData {
  // This interface is intentionally empty as the Synthèse step doesn't have its own data
  // It just displays data from other steps
}

export interface FormDataInvestementAnalysistSteps {
  realEstateData: IRealEstateForm;
  investementCalculatorData: IInvestementCalculatorData;
  profileInvestorData: IProfileInvestor;
  microFonciersData?: IMicroFonciersData;
  fonciersReelsData?: FonciersReelsFormData;
  microBICData?: MicroBICFormData;
  microBICChambreHoteData?: MicroBICChambreHoteFormData;
  microBICTourismeNCData?: MicroBICTourismeNCFormData;
  bicReelLMNPData?: BICReelLMNPFormData;
  sciISData?: SCIISFormData;
  lmpSansTVAData?: LMPSansTVAFormData;
  sarlDeFamilleData?: SARLDeFamilleFormData;
  syntheseData?: SyntheseFormData;
}
export interface IRealEstateForm {
  travauxAmeliorationMontant: number;
  travauxReparationMontant: number;
  travauxTotal: number;
  prixVenteMontant: number;
  prixAcquisitionMontant: number;
  fraisAcquisitionForfait: number;
  travauxDepensesForfait: number;
  capitalSocialSCI: number;
  // Radio button values
  locationsSaisonnieres: string;
  locationsNues: string;
  locationsMeublees: string;
  applicationIndice: string;
  // Société section values
  nombreAssocies: number;
  revenusPartsSCI: number;
  recettesExceptionnelles: number;
  recettesEmplacementsPublicitaires: number;
  // SARL de Famille values
  sarlLocationsMeublees: string;
  sarlAssociesLigneDirecte: string;
  sarlAssociesFreresSoeurs: string;
  sarlAssociesConjointsPACS: string;
  // Plus-Value values
  garantieEviction: string;
  diagnosticsObligatoires: string;
  chargesIndemnites: string;
  fraisAcquisitionReels: string;
  fraisAcquisitionForfaitaire: string;
  depenseTravauxReelles: string;
  fraisVoirie: string;
  // Travaux values
  travauxDeficitFoncier: string;
  travauxConstruction: string;
  travauxConstructionMontant?: number;
  // New travaux deductible radio buttons
  travauxAmeliorationDeductible?: string;
  travauxReparationDeductible?: string;
}
