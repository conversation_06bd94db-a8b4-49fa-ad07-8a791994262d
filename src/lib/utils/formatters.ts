/**
 * Format a number as currency
 * @param value The number to format
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

/**
 * Format a number as percentage
 * @param value The number to format (0-1)
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

/**
 * Get color based on value (positive/negative)
 * @param value The number to check
 * @returns Color string for Chakra UI
 */
export const getValueColor = (value: any): string | undefined => {
  if (value === "N/A" || value === undefined) return undefined;
  return value < 0 ? 'red.500' : value > 0 ? 'green.500' : undefined;
};
