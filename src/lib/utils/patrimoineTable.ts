import type { ColDef } from 'ag-grid-community';

import { BuyingOptions } from '../enums/BuyingOptions';

import { calculateTotalCouple } from './economicDataTransform';

export const getColDefForPatrimoineTable = (buyingOption: string): ColDef[] => {
  const columnDefs: ColDef[] = [
    { headerName: 'Immobilier', field: 'bien', editable: false },
    { headerName: 'Investisseur N°1', field: 'monsieur', editable: true },
    {
      headerName: 'Nature',
      field: 'nature',
      editable: true,
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: {
        values: ['LOCATIF', 'RESIDENCE PRINCIPALE', 'RESIDENCE SECONDAIRE'],
      },
    },
    { headerName: 'Revenu annuel', field: 'revenu', editable: true },
    {
      headerName: 'Patrimoine immobilier',
      field: 'patrimoineImmobilier',
      valueGetter: calculateTotalCouple,
    },
  ];

  if (buyingOption === BuyingOptions.EN_COUPLE) {
    columnDefs.splice(2, 0, {
      headerName: 'Investisseur N°2',
      field: 'madame',
      editable: true,
    });
  }
  return columnDefs;
};
