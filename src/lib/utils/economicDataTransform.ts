import type { ColDef } from 'ag-grid-community';

import { BuyingOptions } from '../enums/BuyingOptions';
import type { IEconomicRowDataTable } from '~/lib/types/buyer-assessment';

// Example data
const data: IEconomicRowDataTable[] = [
  {
    id: 'net_income',
    category: 'Revenus nets avant impôts',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
  {
    id: 'other_income',
    category: 'Autres revenus (dont loyers perçus)',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
  {
    id: 'taxes',
    category: 'Impôts (prélèvements à la source)',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
  {
    id: 'loan_repayments',
    category: 'Remboursements d’emprunts',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
  {
    id: 'monthly_expenses',
    category: 'Dépenses mensuelles récurrentes',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
  {
    id: 'personal_expenses',
    category: 'Dépenses personnelles diverses (estimation)',
    monsieur: '0',
    madame: '0',
    total: 0,
  },
];

export const getColDefForEconomicTable = (buyingOption: string): ColDef[] => {
  const columnDefs: ColDef[] = [
    {
      headerName: 'Ressources et Dépenses',
      field: 'category',
      editable: false,
    },
    {
      headerName: 'Investisseur N°1',
      field: 'monsieur',
      editable: true,
      valueParser: numberParser,
    },
    {
      headerName: 'Total',
      field: 'total',
      editable: false,
      valueGetter: calculateTotalCouple,
    },
  ];

  if (buyingOption === BuyingOptions.EN_COUPLE) {
    columnDefs.splice(2, 0, {
      headerName: 'Investisseur N°2',
      field: 'madame',
      editable: true,
      valueParser: numberParser,
    });
  }
  return columnDefs;
};
export const getDataForEconomicTable = (
  buyingOption: string
): IEconomicRowDataTable[] => {
  if (buyingOption === BuyingOptions.EN_COUPLE) {
    return data.map((item) => ({
      ...item,
      madame: '0',
    }));
  }
  return data;
};
// Function to handle numeric parsing from input
const numberParser = (params: { newValue: string }) => {
  const newValue = parseFloat(params.newValue);
  return isNaN(newValue) ? 0 : newValue;
};

// Function to calculate the total (Monsieur + Madame)
export const calculateTotalCouple = (params: {
  data: IEconomicRowDataTable;
}) => {
  const monsieur = parseFloat(params.data.monsieur) || 0;
  const madame = parseFloat(params.data.madame) || 0;
  params.data.total = monsieur + madame;
  return params.data.total;
};
