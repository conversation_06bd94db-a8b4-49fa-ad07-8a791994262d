'use client';

import { Flex, Text, Stack } from '@chakra-ui/react';
import type { ReactElement } from 'react';

interface FeatureProps {
  text: string;
  iconBg: string;
  fontWeight?: string;
  fontSize?: string;
  icon?: ReactElement;
}

export default function LabelWithIcon({
  text,
  icon,
  iconBg,
  fontSize,
  fontWeight,
}: FeatureProps) {
  return (
    <Stack direction="row" align="center">
      <Flex
        w={8}
        h={8}
        align="center"
        justify="center"
        rounded="half"
        borderRadius={5}
        p={1}
        bg={iconBg}
      >
        {icon}
      </Flex>
      <Text fontWeight={fontWeight} fontSize={fontSize} lineHeight={1.2}>
        {text}
      </Text>
    </Stack>
  );
}
