import { ButtonGroup, Button, Flex } from '@chakra-ui/react';

interface NavigationButtonsProps {
  isLastStep: boolean;
  onNextStep: () => void;
  previousStep: () => void;
  activeStep: number;
}

export function StepperNavigationButtons({
  isLastStep,
  onNextStep,
  previousStep,
  activeStep,
}: NavigationButtonsProps) {
  return (
    <ButtonGroup w="100%" mt={8} justifyContent="center">
      <Flex w="100%" justifyContent="space-between">
        <Button
          onClick={previousStep}
          isDisabled={activeStep === 0}
          colorScheme="teal"
          variant="solid"
        >
          Retour
        </Button>
        {!isLastStep && (
          <Button onClick={onNextStep} colorScheme="teal" variant="outline">
            Suivant
          </Button>
        )}
      </Flex>
    </ButtonGroup>
  );
}
