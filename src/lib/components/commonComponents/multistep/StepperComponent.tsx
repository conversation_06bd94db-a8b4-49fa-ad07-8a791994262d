import {
  Stepper,
  Step,
  StepIndicator,
  StepSeparator,
  Flex,
  Text,
} from '@chakra-ui/react';

interface StepperComponentProps {
  steps: { title: string }[];
  activeStep: number;
  setActiveStep: (index: number) => void;
}

export function StepperComponent({
  steps,
  activeStep,
  setActiveStep,
}: StepperComponentProps) {
  return (
    <Stepper
      index={activeStep}
      size="lg"
      colorScheme="teal"
      mb={8}
      flexWrap="wrap"
      width="100%"
    >
      {steps.map((step, index) => (
        <Step
          key={index}
          onClick={() => setActiveStep(index)}
          width={['100%', 'auto']} // Full width on mobile and auto on larger screens
          mb={4} // Add some bottom margin for spacing between steps
        >
          <Flex
            direction={['column', 'row']} // Stack on mobile, horizontal on desktop
            align="center"
            justify="flex-start"
            width="100%"
            cursor="pointer"
            _hover={{
              transform: 'scale(1.05)',
              transition: 'transform 0.2s ease',
            }} // Add a slight scale effect on hover
          >
            <StepIndicator
              bg={activeStep >= index ? 'teal.500' : 'gray.300'}
              color={activeStep >= index ? 'white' : 'gray.500'}
              borderRadius="full"
              size="sm"
              boxShadow={
                activeStep >= index ? '0 0 8px rgba(0, 128, 128, 0.4)' : 'none'
              }
              transition="background-color 0.3s, box-shadow 0.3s" // Smooth color and shadow transitions
              p={4} // Padding for better indicator size
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {/* Step number */}
              <Text fontSize="md" fontWeight="bold" color="white">
                {index + 1}
              </Text>
            </StepIndicator>

            {/* Step title, displayed next to the step indicator */}
            <Text
              fontSize="sm"
              fontWeight="bold"
              color={activeStep >= index ? 'teal.600' : 'gray.500'}
              ml={[0, 2]} // Margin for spacing next to indicator on larger screens, none on mobile
              mt={[2, 0]} // Margin on top for mobile when stacked
              textAlign={['center', 'left']} // Centered on mobile, left-aligned on desktop
            >
              {step.title}
            </Text>

            {index < steps.length - 1 && (
              <StepSeparator
                borderTopWidth={2}
                borderColor={activeStep >= index ? 'teal.500' : 'gray.300'}
                flex="1"
                height="2px"
                ml={4} // Add margin to separate the indicator and the separator
                opacity={activeStep >= index ? 1 : 0.5} // Fade separator based on active step
              />
            )}
          </Flex>
        </Step>
      ))}
    </Stepper>
  );
}
