import { Text, UnorderedList, ListItem } from '@chakra-ui/react';

interface ListElement {
  id: string;
  text: string;
}

interface ListItemsProps {
  id: string;
  title: string;
  items: ListElement[];
}

const ListItems: React.FC<ListItemsProps> = ({
  id,
  title,
  items,
}: ListItemsProps) => {
  return (
    <div key={id}>
      <Text>{title}</Text>
      <UnorderedList>
        {items.map((listElement) => (
          <ListItem p={2} key={listElement.id}>
            {listElement.text}
          </ListItem>
        ))}
      </UnorderedList>
    </div>
  );
};

export default ListItems;
