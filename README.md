<img src="https://og.sznm.dev/api/generate?heading=mon-partenaire-renta&text=React+vite+template+with+Chakra-UI+and+TypeScript+setup.&template=color&center=true&height=330" />

This is a project bootstrapped with [`@vitejs/app`](https://vitejs.dev/guide/#scaffolding-your-first-vite-project) (`react-ts`), added with [Chakra UI](https://chakra-ui.com) and [TypeScript](https://www.typescriptlang.org) setup.

- ⚡ blazing fast dev server and build
- 🔗 route management added (`react-router-dom` configured)

[**Live Demo**](https://mon-partenaire-renta.vercel.app/)

## Getting Started

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
