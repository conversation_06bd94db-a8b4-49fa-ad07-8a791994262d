{"author": "DoryHk", "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/plus-jakarta-sans": "^5.0.18", "ag-grid-community": "^32.2.2", "ag-grid-react": "^32.2.2", "framer-motion": "^10.16.15", "million": "2.6.4", "react": "^18.2.0", "react-datepicker": "^8.0.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1", "react-slick": "^0.30.2", "react-youtube": "^10.1.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/node": "^20.10.4", "@types/react": "18.2.42", "@types/react-dom": "^18.2.17", "@types/react-helmet": "^6.1.11", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-config-sznm": "^2.0.2", "husky": "^8.0.3", "knip": "^3.6.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^5.10.0", "standard-version": "^9.5.0", "typescript": "5.3.3", "vite": "^5.0.12", "vite-plugin-checker": "^0.6.2", "vite-plugin-pwa": "^0.17.4", "vite-tsconfig-paths": "^4.2.2"}, "engines": {"node": ">=16.x.x", "pnpm": ">=8"}, "name": "mon-partenaire-renta", "private": true, "scripts": {"build": "vite build", "check:turbo": "pnpm dlx turbo@latest", "dev": "vite", "format": "prettier --write src", "knip": "knip", "lint": "eslint src", "lint:fix": "eslint src --fix && pnpm format", "prepare": "husky install", "push-release": "git push --follow-tags origin main", "release": "standard-version", "serve": "vite preview", "type-check": "tsc", "up-interactive": "pnpm up -i", "up-latest": "pnpm up-interactive -L"}, "type": "module", "version": "0.1.0"}